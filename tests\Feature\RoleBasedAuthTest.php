<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RoleBasedAuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_user_redirected_to_admin_dashboard_after_login(): void
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertRedirect('/admin/dashboard');
    }

    public function test_regular_user_redirected_to_user_dashboard_after_login(): void
    {
        $user = User::factory()->create([
            'role' => 'user',
            'email' => '<EMAIL>',
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertRedirect('/user/dashboard');
    }

    public function test_admin_can_access_admin_dashboard(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
    }

    public function test_user_cannot_access_admin_dashboard(): void
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($user)->get('/admin/dashboard');

        $response->assertRedirect('/user/dashboard');
        $response->assertSessionHas('error', 'Access denied. You do not have permission to access this area.');
    }

    public function test_user_can_access_user_dashboard(): void
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($user)->get('/user/dashboard');

        $response->assertStatus(200);
    }

    public function test_admin_cannot_access_user_dashboard(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/user/dashboard');

        $response->assertRedirect('/admin/dashboard');
        $response->assertSessionHas('error', 'Access denied. You do not have permission to access this area.');
    }

    public function test_new_user_registration_defaults_to_user_role(): void
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
        ]);

        $response->assertRedirect('/user/dashboard');
        
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertEquals('user', $user->role);
    }

    public function test_user_model_helper_methods(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'user']);

        $this->assertTrue($admin->isAdmin());
        $this->assertFalse($admin->isUser());
        $this->assertEquals('admin.dashboard', $admin->getDashboardRoute());

        $this->assertTrue($user->isUser());
        $this->assertFalse($user->isAdmin());
        $this->assertEquals('user.dashboard', $user->getDashboardRoute());
    }
}
