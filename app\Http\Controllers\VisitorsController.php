<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class VisitorsController extends Controller
{
    public function courses()
    {
        return view('courses');
    }
    public function about()
    {
        return view('aboutUs');
    }
       public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }

    public function add_category(){
        return view('admin.categories.add_category');
    }

public function store_category(Request $request)
{
    $validated = $request->validate([
        'name' => 'required|string|max:255',
        'slug' => 'nullable|string|max:255',
        'description' => 'nullable|string',
        'color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',
        'icon' => 'nullable|string|max:255',
        'parent_id' => 'nullable', // No validation here anymore
        'sort_order' => 'nullable|integer|min:0',
        'is_active' => 'boolean',
    ]);

    // Force parent_id to null no matter what the form sends
    $validated['parent_id'] = null;

    Category::create($validated);

    return redirect()->route('all.categories');
}



    public function all_categories(){
        // Fetch categories with their parent relationships and pagination
        $categories = Category::with('parent')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(15);

        // Get parent categories for any filtering needs
        $parentCategories = Category::whereNull('parent_id')
            ->orderBy('name')
            ->get();

        return view('admin.categories.all_categories', compact('categories', 'parentCategories'));
    }
}
