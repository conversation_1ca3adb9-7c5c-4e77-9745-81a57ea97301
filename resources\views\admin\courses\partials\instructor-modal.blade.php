<!-- Add Instructor Modal -->
<div class="modal fade" id="addInstructorModal" tabindex="-1" aria-labelledby="addInstructorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addInstructorModalLabel">
                    <i class="uil-user-plus me-2"></i>Add New Instructor
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addInstructorForm">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-danger d-none" id="instructorErrorAlert">
                        <ul class="mb-0" id="instructorErrorList"></ul>
                    </div>
                    
                    <div class="mb-3">
                        <label for="instructorName" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="instructorName" name="name" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="instructorEmail" class="form-label">Email Address <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="instructorEmail" name="email" required>
                        <div class="form-text">This will be used for login credentials</div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="instructorPassword" class="form-label">Password <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="instructorPassword" name="password" 
                                   minlength="8" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggleInstructorPassword">
                                <i class="uil-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                        <div class="form-text">Minimum 8 characters required</div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="uil-info-circle me-2"></i>
                        <strong>Note:</strong> The instructor will be created with admin privileges and can access the admin dashboard.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="uil-times me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" id="saveInstructorBtn">
                        <i class="uil-check me-1"></i>Create Instructor
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
