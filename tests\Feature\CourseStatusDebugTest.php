<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CourseStatusDebugTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $category;
    protected $instructor;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);

        // Create a category
        $this->category = Category::factory()->create([
            'name' => 'Test Category',
            'is_active' => true
        ]);

        // Create an instructor
        $this->instructor = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function debug_status_field_validation_issue()
    {
        // Test with minimal required fields including status
        $courseData = [
            'title' => 'Debug Test Course',
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $courseData);

        // Check if there are any validation errors
        if ($response->getSession()->has('errors')) {
            $errors = $response->getSession()->get('errors');
            dump('Validation errors:', $errors->all());
        }

        // Check the response
        if ($response->isRedirection()) {
            dump('Redirected to:', $response->headers->get('Location'));
        } else {
            dump('Response status:', $response->getStatusCode());
        }

        $response->assertRedirect(route('admin.courses.index'));
        $this->assertDatabaseHas('courses', [
            'title' => 'Debug Test Course',
            'status' => 'draft',
        ]);
    }

    /** @test */
    public function debug_status_field_with_empty_value()
    {
        // Test with empty status value (like selecting "Select Status" option)
        $courseData = [
            'title' => 'Debug Test Course Empty Status',
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'status' => '', // Empty status
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $courseData);

        // This should fail validation
        $response->assertSessionHasErrors(['status']);
        
        // Check the specific error message
        $errors = $response->getSession()->get('errors');
        if ($errors) {
            dump('Status validation error:', $errors->get('status'));
        }
    }

    /** @test */
    public function debug_status_field_with_invalid_value()
    {
        // Test with invalid status value
        $courseData = [
            'title' => 'Debug Test Course Invalid Status',
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'status' => 'invalid_status',
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $courseData);

        // This should fail validation
        $response->assertSessionHasErrors(['status']);
        
        // Check the specific error message
        $errors = $response->getSession()->get('errors');
        if ($errors) {
            dump('Status validation error for invalid value:', $errors->get('status'));
        }
    }

    /** @test */
    public function debug_status_field_missing_completely()
    {
        // Test without status field at all
        $courseData = [
            'title' => 'Debug Test Course No Status',
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            // No status field
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $courseData);

        // This should fail validation
        $response->assertSessionHasErrors(['status']);
        
        // Check the specific error message
        $errors = $response->getSession()->get('errors');
        if ($errors) {
            dump('Status validation error for missing field:', $errors->get('status'));
        }
    }

    /** @test */
    public function debug_form_submission_exactly_as_browser_would()
    {
        // Simulate exactly what a browser would send
        $formData = [
            '_token' => csrf_token(),
            'title' => 'Browser Simulation Test',
            'slug' => '',
            'short_description' => '',
            'description' => '',
            'learning_objectives' => '',
            'requirements' => '',
            'category_id' => (string)$this->category->id,
            'instructor_id' => (string)$this->instructor->id,
            'status' => 'published',
            'duration' => '',
            'difficulty_level' => '',
            'language' => '',
            'price' => '',
            'discount_price' => '',
            'preview_video' => '',
            'meta_title' => '',
            'meta_description' => '',
            'tags' => '',
            // Note: checkboxes not included when unchecked
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $formData);

        if ($response->getSession()->has('errors')) {
            $errors = $response->getSession()->get('errors');
            dump('Browser simulation errors:', $errors->all());
        }

        $response->assertRedirect(route('admin.courses.index'));
        $this->assertDatabaseHas('courses', [
            'title' => 'Browser Simulation Test',
            'status' => 'published',
        ]);
    }
}
