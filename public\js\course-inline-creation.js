/**
 * Course Inline Creation JavaScript
 * Handles inline creation of categories and instructors from course creation form
 */

(function($) {
    'use strict';

    // CSRF token for AJAX requests
    const csrfToken = $('meta[name="csrf-token"]').attr('content');

    // Initialize when document is ready
    $(document).ready(function() {
        initializeCategoryModal();
        initializeInstructorModal();
        initializePasswordToggle();
    });

    /**
     * Initialize Category Modal functionality
     */
    function initializeCategoryModal() {
        // Handle category form submission
        $('#addCategoryForm').on('submit', function(e) {
            e.preventDefault();
            
            const form = $(this);
            const submitBtn = $('#saveCategoryBtn');
            const originalText = submitBtn.html();
            
            // Show loading state
            submitBtn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i>Creating...');
            
            // Clear previous errors
            clearErrors('category');
            
            // Prepare form data
            const formData = {
                _token: csrfToken,
                name: $('#categoryName').val(),
                description: $('#categoryDescription').val(),
                color: $('#categoryColor').val(),
                icon: $('#categoryIcon').val()
            };
            
            // Send AJAX request
            $.ajax({
                url: '/admin/courses/ajax/create-category',
                method: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Add new option to category dropdown
                        const newOption = `<option value="${response.category.id}" selected>${response.category.name}</option>`;
                        $('#category_id').append(newOption);
                        
                        // Show success message
                        showToast(response.message, 'success');
                        
                        // Close modal and reset form
                        $('#addCategoryModal').modal('hide');
                        form[0].reset();
                        $('#categoryColor').val('#6777ef'); // Reset color to default
                    }
                },
                error: function(xhr) {
                    handleAjaxError(xhr, 'category');
                },
                complete: function() {
                    // Reset button state
                    submitBtn.prop('disabled', false).html(originalText);
                }
            });
        });

        // Reset form when modal is hidden
        $('#addCategoryModal').on('hidden.bs.modal', function() {
            $('#addCategoryForm')[0].reset();
            $('#categoryColor').val('#6777ef');
            clearErrors('category');
        });
    }

    /**
     * Initialize Instructor Modal functionality
     */
    function initializeInstructorModal() {
        // Handle instructor form submission
        $('#addInstructorForm').on('submit', function(e) {
            e.preventDefault();
            
            const form = $(this);
            const submitBtn = $('#saveInstructorBtn');
            const originalText = submitBtn.html();
            
            // Show loading state
            submitBtn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i>Creating...');
            
            // Clear previous errors
            clearErrors('instructor');
            
            // Prepare form data
            const formData = {
                _token: csrfToken,
                name: $('#instructorName').val(),
                email: $('#instructorEmail').val(),
                password: $('#instructorPassword').val()
            };
            
            // Send AJAX request
            $.ajax({
                url: '/admin/courses/ajax/create-instructor',
                method: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Add new option to instructor dropdown
                        const newOption = `<option value="${response.instructor.id}" selected>${response.instructor.name}</option>`;
                        $('#instructor_id').append(newOption);
                        
                        // Show success message
                        showToast(response.message, 'success');
                        
                        // Close modal and reset form
                        $('#addInstructorModal').modal('hide');
                        form[0].reset();
                    }
                },
                error: function(xhr) {
                    handleAjaxError(xhr, 'instructor');
                },
                complete: function() {
                    // Reset button state
                    submitBtn.prop('disabled', false).html(originalText);
                }
            });
        });

        // Reset form when modal is hidden
        $('#addInstructorModal').on('hidden.bs.modal', function() {
            $('#addInstructorForm')[0].reset();
            clearErrors('instructor');
        });
    }

    /**
     * Initialize password toggle functionality
     */
    function initializePasswordToggle() {
        $('#toggleInstructorPassword').on('click', function() {
            const passwordField = $('#instructorPassword');
            const toggleIcon = $('#passwordToggleIcon');
            
            if (passwordField.attr('type') === 'password') {
                passwordField.attr('type', 'text');
                toggleIcon.removeClass('uil-eye').addClass('uil-eye-slash');
            } else {
                passwordField.attr('type', 'password');
                toggleIcon.removeClass('uil-eye-slash').addClass('uil-eye');
            }
        });
    }

    /**
     * Clear form errors
     */
    function clearErrors(type) {
        $(`#${type}ErrorAlert`).addClass('d-none');
        $(`#${type}ErrorList`).empty();
        $(`.form-control`).removeClass('is-invalid');
        $(`.invalid-feedback`).text('');
    }

    /**
     * Handle AJAX errors
     */
    function handleAjaxError(xhr, type) {
        if (xhr.status === 422) {
            // Validation errors
            const errors = xhr.responseJSON.errors || {};
            const errorList = $(`#${type}ErrorList`);
            
            // Clear previous errors
            errorList.empty();
            
            // Display validation errors
            Object.keys(errors).forEach(function(field) {
                errors[field].forEach(function(message) {
                    errorList.append(`<li>${message}</li>`);
                });
                
                // Highlight invalid fields
                $(`[name="${field}"]`).addClass('is-invalid');
                $(`[name="${field}"]`).siblings('.invalid-feedback').text(errors[field][0]);
            });
            
            $(`#${type}ErrorAlert`).removeClass('d-none');
        } else {
            // General error
            const message = xhr.responseJSON?.message || 'An error occurred. Please try again.';
            showToast(message, 'error');
        }
    }

    /**
     * Show toast notification
     */
    function showToast(message, type = 'info') {
        const toastClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const toast = $(`
            <div class="alert ${toastClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `);
        
        $('body').append(toast);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            toast.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

})(jQuery);
