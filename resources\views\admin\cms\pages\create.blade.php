<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Create Page - Admin Dashboard</title>
    <link rel="stylesheet" href="/frontend/css/admin.css">
</head>

<body>
    <div id="app">
        <div class="main-wrapper">
            <div class="navbar-bg"></div>
            <nav class="navbar navbar-expand-lg main-navbar">
                <div class="mr-2 form-inline">
                    <ul class="mr-3 navbar-nav d-flex align-items-center">
                        <li><a href="#" data-toggle="sidebar" class="nav-link nav-link-lg"><i class="fas fa-bars"></i></a></li>
                    </ul>
                </div>
                
                <ul class="navbar-nav navbar-right">
                    <li class="dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg nav-link-user">
                            <img alt="image" src="" class="mr-1 rounded-circle">
                            <div class="d-sm-none d-lg-inline-block">Admin</div>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item has-icon text-danger">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </button>
                            </form>
                        </div>
                    </li>
                </ul>
            </nav>
            
            <div class="main-sidebar">
                <aside id="sidebar-wrapper">
                    <div class="sidebar-brand">
                        <a href="{{ route('admin.dashboard') }}">
                            <img class="admin_logo" src="" alt="SkillGro">
                        </a>
                    </div>
                    
                    <ul class="sidebar-menu">
                        <li>
                            <a class="nav-link" href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>

                        <li class="menu-header">Content Management</li>
                        <li class="nav-item dropdown active">
                            <a href="javascript:void(0)" class="nav-link has-dropdown">
                                <i class="fas fa-edit"></i><span>Content Management</span>
                            </a>
                            <ul class="dropdown-menu" style="display: block;">
                                <li class="active"><a class="nav-link" href="{{ route('admin.cms.pages.index') }}">Pages</a></li>
                                <li><a class="nav-link" href="{{ route('admin.cms.posts.index') }}">Blog Posts</a></li>
                                <li><a class="nav-link" href="{{ route('admin.cms.categories.index') }}">Categories</a></li>
                                <li><a class="nav-link" href="{{ route('admin.cms.tags.index') }}">Tags</a></li>
                                <li><a class="nav-link" href="{{ route('admin.cms.media.index') }}">Media Library</a></li>
                                <li><a class="nav-link" href="{{ route('admin.cms.menus.index') }}">Menus</a></li>
                            </ul>
                        </li>

                        <li class="menu-header">Settings</li>
                        <li>
                            <a class="nav-link" href="{{ route('admin.cms.settings.index') }}">
                                <i class="fas fa-cog"></i>
                                <span>Site Settings</span>
                            </a>
                        </li>
                    </ul>
                </aside>
            </div>

            <div class="main-content">
                <section class="section">
                    <div class="section-header">
                        <h1>Create New Page</h1>
                        <div class="section-header-breadcrumb">
                            <div class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></div>
                            <div class="breadcrumb-item"><a href="{{ route('admin.cms.pages.index') }}">Pages</a></div>
                            <div class="breadcrumb-item active">Create</div>
                        </div>
                    </div>

                    <div class="section-body">
                        <form method="POST" action="{{ route('admin.cms.pages.store') }}" enctype="multipart/form-data">
                            @csrf
                            <div class="row">
                                <div class="col-12 col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h4>Page Content</h4>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group">
                                                <label>Title</label>
                                                <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                       name="title" value="{{ old('title') }}" required>
                                                @error('title')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="form-group">
                                                <label>Slug</label>
                                                <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                                       name="slug" value="{{ old('slug') }}">
                                                <small class="form-text text-muted">Leave empty to auto-generate from title</small>
                                                @error('slug')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="form-group">
                                                <label>Content</label>
                                                <textarea class="form-control @error('content') is-invalid @enderror" 
                                                          name="content" rows="15" required>{{ old('content') }}</textarea>
                                                @error('content')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="form-group">
                                                <label>Excerpt</label>
                                                <textarea class="form-control @error('excerpt') is-invalid @enderror" 
                                                          name="excerpt" rows="3">{{ old('excerpt') }}</textarea>
                                                @error('excerpt')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card">
                                        <div class="card-header">
                                            <h4>SEO Settings</h4>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group">
                                                <label>Meta Title</label>
                                                <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                                                       name="meta_title" value="{{ old('meta_title') }}">
                                                @error('meta_title')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="form-group">
                                                <label>Meta Description</label>
                                                <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                                          name="meta_description" rows="3">{{ old('meta_description') }}</textarea>
                                                @error('meta_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h4>Publish</h4>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group">
                                                <label>Status</label>
                                                <select class="form-control @error('status') is-invalid @enderror" name="status">
                                                    <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                                    <option value="published" {{ old('status') == 'published' ? 'selected' : '' }}>Published</option>
                                                    <option value="private" {{ old('status') == 'private' ? 'selected' : '' }}>Private</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="form-group">
                                                <div class="control-label">Page Options</div>
                                                <label class="custom-switch mt-2">
                                                    <input type="checkbox" name="is_homepage" value="1" 
                                                           class="custom-switch-input" {{ old('is_homepage') ? 'checked' : '' }}>
                                                    <span class="custom-switch-indicator"></span>
                                                    <span class="custom-switch-description">Set as Homepage</span>
                                                </label>
                                            </div>

                                            <div class="form-group">
                                                <button type="submit" class="btn btn-primary btn-block">Create Page</button>
                                                <a href="{{ route('admin.cms.pages.index') }}" class="btn btn-secondary btn-block">Cancel</a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card">
                                        <div class="card-header">
                                            <h4>Featured Image</h4>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group">
                                                <input type="file" class="form-control @error('featured_image') is-invalid @enderror" 
                                                       name="featured_image" accept="image/*">
                                                @error('featured_image')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </section>
            </div>

            <footer class="main-footer">
                <div class="footer-left">
                    Copyright &copy; {{ date('Y') }} <div class="bullet"></div> Design By <a href="#">Lernovate</a>
                </div>
                <div class="footer-right">
                    <span>Version: 2.5.0</span>
                </div>
            </footer>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ asset('frontend/js/admin.js') }}"></script>
</body>
</html>
