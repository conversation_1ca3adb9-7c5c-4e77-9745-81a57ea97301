<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $role
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Ensure user has a role, default to 'user' if null
        if (empty($user->role)) {
            $user->role = 'user';
            $user->save();
        }

        // Check if user has the required role
        if ($user->role !== $role) {
            // Prevent redirect loops by checking current route
            $currentRoute = $request->route()->getName();

            // If user is admin but trying to access user route, redirect to admin dashboard
            if ($user->isAdmin() && $currentRoute !== 'admin.dashboard') {
                return redirect()->route('admin.dashboard')->with('error', 'Access denied. You do not have permission to access this area.');
            }
            // If user is regular user but trying to access admin route, redirect to user dashboard
            elseif ($user->isUser() && $currentRoute !== 'user.dashboard') {
                return redirect()->route('user.dashboard')->with('error', 'Access denied. You do not have permission to access this area.');
            }
            // If we're already on the correct dashboard but role doesn't match, there's a data issue
            else {
                // Log the issue and allow access to prevent infinite loop
                \Log::warning('Role mismatch detected', [
                    'user_id' => $user->id,
                    'user_role' => $user->role,
                    'required_role' => $role,
                    'current_route' => $currentRoute
                ]);

                // Fix the user role if it's obviously wrong
                if ($currentRoute === 'user.dashboard' && $user->role !== 'user') {
                    $user->role = 'user';
                    $user->save();
                } elseif ($currentRoute === 'admin.dashboard' && $user->role !== 'admin') {
                    $user->role = 'admin';
                    $user->save();
                }
            }
        }

        return $next($request);
    }
}
