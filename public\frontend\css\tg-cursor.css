.cb-cursor {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1100;
    contain: layout style size;
    pointer-events: none;
    will-change: transform;
    -webkit-transition: opacity 3s, color 0.4s;
    -o-transition: opacity 3s, color 0.4s;
    -moz-transition: opacity 3s, color 0.4s;
    transition: opacity 3s, color 0.4s;
}
.cb-cursor:before {
    content: "";
    position: absolute;
    top: -25px;
    left: -25px;
    width: 50px;
    height: 50px;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    background: var(--tg-theme-primary);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    -webkit-transition: opacity 1s, -webkit-transform 0.3s ease-in-out;
    transition: opacity 1s, -webkit-transform 0.3s ease-in-out;
    -o-transition: opacity 1s, -o-transform 0.3s ease-in-out;
    -moz-transition: transform 0.3s ease-in-out, opacity 1s, -moz-transform 0.3s ease-in-out;
    transition: transform 0.3s ease-in-out, opacity 1s;
    transition: transform 0.3s ease-in-out, opacity 1s, -webkit-transform 0.3s ease-in-out, -moz-transform 0.3s ease-in-out, -o-transform 0.3s ease-in-out;
    opacity: 1 !important;
}
.cb-cursor-text {
    position: absolute;
    top: -50px;
    left: -50px;
    width: 100px;
    height: 100px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-transform: scale(0) rotate(10deg);
    -moz-transform: scale(0) rotate(10deg);
    -ms-transform: scale(0) rotate(10deg);
    -o-transform: scale(0) rotate(10deg);
    transform: scale(0) rotate(10deg);
    opacity: 0;
    color: white;
    font-weight: 700;
    font-size: 14px;
    line-height: 18px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0;
    -webkit-transition: opacity 0.4s, -webkit-transform 0.3s;
    transition: opacity 0.4s, -webkit-transform 0.3s;
    -o-transition: opacity 0.4s, -o-transform 0.3s;
    -moz-transition: opacity 0.4s, transform 0.3s, -moz-transform 0.3s;
    transition: opacity 0.4s, transform 0.3s;
    transition: opacity 0.4s, transform 0.3s, -webkit-transform 0.3s, -moz-transform 0.3s, -o-transform 0.3s;
}
@supports (mix-blend-mode: exclusion) {
    .cb-cursor.-exclusion,
    .cb-cursor.-opaque {
        mix-blend-mode: exclusion;
    }
}
@supports (mix-blend-mode: exclusion) {
    .cb-cursor.-exclusion:before,
    .cb-cursor.-opaque:before {
        background: white;
    }
}
.cb-cursor.-normal, .cb-cursor.-text {
    mix-blend-mode: normal;
}
.cb-cursor.-black {
    color: #182029;
}
.cb-cursor.-black::before {
    background: currentColor;
    opacity: .95 !important;
}
.cb-cursor.-theme {
    color: var(--tg-theme-primary);
}
.cb-cursor.-theme .cb-cursor-text {
    font-weight: var(--tg-fw-extra-bold);
    color: var(--tg-common-color-black-2);
}
.cb-cursor.-theme::before {
    background: currentColor;
    opacity: .95 !important;
}
.cb-cursor.-inverse {
    color: white;
}
.cb-cursor.-visible:before {
    -webkit-transform: scale(0.2);
    -moz-transform: scale(0.2);
    -ms-transform: scale(0.2);
    -o-transform: scale(0.2);
    transform: scale(0.2);
}
.cb-cursor.-visible.-active:before {
    -webkit-transform: scale(0.23);
    -moz-transform: scale(0.23);
    -ms-transform: scale(0.23);
    -o-transform: scale(0.23);
    transform: scale(0.23);
    -webkit-transition-duration: 0.2s;
    -moz-transition-duration: 0.2s;
    -o-transition-duration: 0.2s;
    transition-duration: 0.2s;
}
.cb-cursor.-pointer:before {
    -webkit-transform: scale(0.15);
    -moz-transform: scale(0.15);
    -ms-transform: scale(0.15);
    -o-transform: scale(0.15);
    transform: scale(0.15);
}
.cb-cursor.-text:before {
    opacity: 0.85;
    -webkit-transform: scale(1.7);
    -moz-transform: scale(1.7);
    -ms-transform: scale(1.7);
    -o-transform: scale(1.7);
    transform: scale(1.7);
}
.cb-cursor.-text .cb-cursor-text {
    opacity: 1;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
}
.cb-cursor.-text.-active:before {
    -webkit-transform: scale(1.6);
    -moz-transform: scale(1.6);
    -ms-transform: scale(1.6);
    -o-transform: scale(1.6);
    transform: scale(1.6);
    -webkit-transition-duration: 0.2s;
    -moz-transition-duration: 0.2s;
    -o-transition-duration: 0.2s;
    transition-duration: 0.2s;
}
.cb-cursor.-opaque:before {
    -webkit-transform: scale(1.32);
    -moz-transform: scale(1.32);
    -ms-transform: scale(1.32);
    -o-transform: scale(1.32);
    transform: scale(1.32);
}
.cb-cursor.-opaque.-active:before {
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2);
}
.cb-cursor.-lg:before {
    -webkit-transform: scale(2);
    -moz-transform: scale(2);
    -ms-transform: scale(2);
    -o-transform: scale(2);
    transform: scale(2);
}
.cb-cursor.-hidden:before {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
}
@media (max-width: 767.98px) {
    .cb-cursor {
        display: none !important;
    }
}