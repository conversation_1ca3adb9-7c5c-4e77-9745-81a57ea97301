<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Menu extends Model
{
    protected $fillable = [
        'name',
        'location',
        'items',
        'is_active',
    ];

    protected $casts = [
        'items' => 'array',
        'is_active' => 'boolean',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByLocation($query, $location)
    {
        return $query->where('location', $location);
    }

    // Accessors
    public function getItemsCountAttribute(): int
    {
        return count($this->items ?? []);
    }

    // Methods
    public function addItem(array $item): void
    {
        $items = $this->items ?? [];
        $items[] = $item;
        $this->items = $items;
        $this->save();
    }

    public function removeItem(int $index): void
    {
        $items = $this->items ?? [];
        if (isset($items[$index])) {
            unset($items[$index]);
            $this->items = array_values($items);
            $this->save();
        }
    }

    public function updateItem(int $index, array $item): void
    {
        $items = $this->items ?? [];
        if (isset($items[$index])) {
            $items[$index] = $item;
            $this->items = $items;
            $this->save();
        }
    }
}
