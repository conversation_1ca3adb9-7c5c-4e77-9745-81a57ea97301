<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Pages - Admin Dashboard</title>
    <link rel="stylesheet" href="/frontend/css/admin.css">
</head>

<body>
    <div id="app">
        <div class="main-wrapper">
            <div class="navbar-bg"></div>
            <nav class="navbar navbar-expand-lg main-navbar">
                <div class="mr-2 form-inline">
                    <ul class="mr-3 navbar-nav d-flex align-items-center">
                        <li><a href="#" data-toggle="sidebar" class="nav-link nav-link-lg"><i class="fas fa-bars"></i></a></li>
                    </ul>
                </div>
                
                <ul class="navbar-nav navbar-right">
                    <li class="dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg nav-link-user">
                            <img alt="image" src="" class="mr-1 rounded-circle">
                            <div class="d-sm-none d-lg-inline-block">Admin</div>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item has-icon text-danger">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </button>
                            </form>
                        </div>
                    </li>
                </ul>
            </nav>
            
            <div class="main-sidebar">
                <aside id="sidebar-wrapper">
                    <div class="sidebar-brand">
                        <a href="{{ route('admin.dashboard') }}">
                            <img class="admin_logo" src="" alt="SkillGro">
                        </a>
                    </div>
                    
                    <ul class="sidebar-menu">
                        <li>
                            <a class="nav-link" href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>

                        <li class="menu-header">Content Management</li>
                        <li class="nav-item dropdown active">
                            <a href="javascript:void(0)" class="nav-link has-dropdown">
                                <i class="fas fa-edit"></i><span>Content Management</span>
                            </a>
                            <ul class="dropdown-menu" style="display: block;">
                                <li class="active"><a class="nav-link" href="{{ route('admin.cms.pages.index') }}">Pages</a></li>
                                <li><a class="nav-link" href="{{ route('admin.cms.posts.index') }}">Blog Posts</a></li>
                                <li><a class="nav-link" href="{{ route('admin.cms.categories.index') }}">Categories</a></li>
                                <li><a class="nav-link" href="{{ route('admin.cms.tags.index') }}">Tags</a></li>
                                <li><a class="nav-link" href="{{ route('admin.cms.media.index') }}">Media Library</a></li>
                                <li><a class="nav-link" href="{{ route('admin.cms.menus.index') }}">Menus</a></li>
                            </ul>
                        </li>

                        <li class="menu-header">Settings</li>
                        <li>
                            <a class="nav-link" href="{{ route('admin.cms.settings.index') }}">
                                <i class="fas fa-cog"></i>
                                <span>Site Settings</span>
                            </a>
                        </li>
                    </ul>
                </aside>
            </div>

            <div class="main-content">
                <section class="section">
                    <div class="section-header">
                        <h1>Pages</h1>
                        <div class="section-header-button">
                            <a href="{{ route('admin.cms.pages.create') }}" class="btn btn-primary">Add New Page</a>
                        </div>
                    </div>

                    <div class="section-body">
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ session('success') }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        @endif

                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>All Pages</h4>
                                        <div class="card-header-form">
                                            <form method="GET">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" placeholder="Search" name="search" value="{{ request('search') }}">
                                                    <div class="input-group-btn">
                                                        <button class="btn btn-primary"><i class="fas fa-search"></i></button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Title</th>
                                                        <th>Status</th>
                                                        <th>Author</th>
                                                        <th>Created</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @forelse($pages as $page)
                                                        <tr>
                                                            <td>
                                                                <strong>{{ $page->title }}</strong>
                                                                @if($page->is_homepage)
                                                                    <span class="badge badge-info ml-1">Homepage</span>
                                                                @endif
                                                                <br>
                                                                <small class="text-muted">{{ $page->slug }}</small>
                                                            </td>
                                                            <td>
                                                                <span class="badge badge-{{ $page->status === 'published' ? 'success' : ($page->status === 'draft' ? 'warning' : 'secondary') }}">
                                                                    {{ ucfirst($page->status) }}
                                                                </span>
                                                            </td>
                                                            <td>{{ $page->creator->name }}</td>
                                                            <td>{{ $page->created_at->format('M d, Y') }}</td>
                                                            <td>
                                                                <a href="{{ route('admin.cms.pages.edit', $page) }}" class="btn btn-sm btn-primary">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="{{ route('admin.cms.pages.show', $page) }}" class="btn btn-sm btn-info">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                <form method="POST" action="{{ route('admin.cms.pages.destroy', $page) }}" class="d-inline">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </form>
                                                            </td>
                                                        </tr>
                                                    @empty
                                                        <tr>
                                                            <td colspan="5" class="text-center">No pages found</td>
                                                        </tr>
                                                    @endforelse
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    @if($pages->hasPages())
                                        <div class="card-footer">
                                            {{ $pages->links() }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <footer class="main-footer">
                <div class="footer-left">
                    Copyright &copy; {{ date('Y') }} <div class="bullet"></div> Design By <a href="#">Lernovate</a>
                </div>
                <div class="footer-right">
                    <span>Version: 2.5.0</span>
                </div>
            </footer>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ asset('frontend/js/admin.js') }}"></script>
</body>
</html>
