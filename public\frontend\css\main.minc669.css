@import url(https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&amp;family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,400;1,500;1,600&amp;display=swap);
.img,
a,
button,
img {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
body,
p {
    line-height: var(--tg-body-line-height);
    color: var(--tg-body-color);
}
body,
input,
p,
textarea {
    color: var(--tg-body-color);
}
.btn,
.fix,
.instructor-active,
.mobile-menu-visible,
.swiper-container {
    overflow: hidden;
}
#preloader,
.offCanvas__info,
.offCanvas__overly {
    position: fixed;
    top: 0;
    height: 100%;
}
.tg-header__phone img,
.tg-header__top-info li img {
    opacity: 0.6;
}
.banner__content .title .svg-icon path,
.section__title .title .svg-icon path {
    stroke: var(--tg-heading-color);
}
.faq__img-three:hover .faq__img-shape svg .animation-dashed,
.features__item-five:hover .shape-two svg .animation-dashed {
    animation-play-state: running;
}
.btn,
.cart__table tbody td,
.dashboard__review-table thead {
    vertical-align: middle;
}
:root {
    --tg-body-font-family: "Inter", sans-serif;
    --tg-heading-font-family: "Poppins", sans-serif;
    --tg-icon-font-family: "Font Awesome 5 Free";
    --tg-body-font-size: 16px;
    --tg-body-line-height: 1.75;
    --tg-heading-line-height: 1.3;
    --tg-body-color: #6d6c80;
    --tg-heading-color: #161439;
    --tg-theme-primary: #5751e1;
    --tg-theme-secondary: #ffc224;
    --tg-common-color-blue: #050071;
    --tg-common-color-blue-2: #282568;
    --tg-common-color-indigo: #9b51e0;
    --tg-common-color-purple: #8121fb;
    --tg-common-color-pink: #ff429d;
    --tg-common-color-red: #e11b24;
    --tg-common-color-orange: #fd7e14;
    --tg-common-color-yellow: #f8bc24;
    --tg-common-color-yellow-2: #fbe67b;
    --tg-common-color-yellow-3: #fcb428;
    --tg-common-color-green: #12bb6a;
    --tg-common-color-teal: #219653;
    --tg-common-color-cyan: #00aee5;
    --tg-common-color-white: #fff;
    --tg-common-color-gray: #f7f7f9;
    --tg-common-color-gray-2: #efeefe;
    --tg-common-color-gray-3: #7f7e97;
    --tg-common-color-gray-4: #acaacc;
    --tg-common-color-gray-5: #b2bbcc;
    --tg-common-color-gray-6: #d7d7df;
    --tg-common-color-gray-7: #f6f6f6;
    --tg-common-color-gray-8: #f5f5f4;
    --tg-common-color-gray-9: #f9f9f9;
    --tg-common-color-gray-10: #f8f8f8;
    --tg-common-color-dark: #1c1a4a;
    --tg-common-color-black: #06042e;
    --tg-common-color-black-2: #161439;
    --tg-common-color-black-3: #000;
    --tg-border-1: #c9c9dd;
    --tg-border-2: #d0dae9;
    --tg-border-3: #e2e2e2;
    --tg-border-4: #d7dce3;
    --tg-border-5: #2f466a;
    --tg-border-6: #dfdfdf;
    --tg-fw-extra-bold: 800;
    --tg-fw-bold: 700;
    --tg-fw-semi-bold: 600;
    --tg-fw-medium: 500;
    --tg-fw-regular: 400;
    --tg-fw-light: 300;
}
body {
    font-family: var(--tg-body-font-family);
    font-size: var(--tg-body-font-size);
    font-weight: var(--tg-fw-regular);
}
.btn,
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: var(--tg-fw-semi-bold);
    font-family: var(--tg-heading-font-family);
}
.img,
img {
    max-width: 100%;
    transition: 0.3s ease-out;
}
a,
button {
    color: var(--tg-theme-primary);
    outline: 0;
    text-decoration: none;
    transition: 0.3s ease-out;
}
.btn,
.offCanvas__overly {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.btn:focus,
.button:focus,
a:focus {
    text-decoration: none;
    outline: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}
a:hover,
button:hover {
    color: var(--tg-theme-secondary);
    text-decoration: none;
}
.account__check-forgot a,
.account__switch p a,
.coupon__code-info a:hover,
.order__info-wrap p a,
.view-all-categories a span {
    text-decoration: underline;
}
button:focus,
input:focus,
textarea,
textarea:focus {
    outline: 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    color: var(--tg-heading-color);
    margin-top: 0;
    line-height: var(--tg-heading-line-height);
    text-transform: unset;
}
.banner__student .content .title,
p {
    font-family: var(--tg-body-font-family);
}
h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
    color: inherit;
}
h1 {
    font-size: 2.5rem;
}
h2 {
    font-size: 2rem;
}
h3 {
    font-size: 1.75rem;
}
h4 {
    font-size: 1.5rem;
}
h5 {
    font-size: 1.25rem;
}
h6 {
    font-size: 1rem;
}
label,
p {
    font-weight: var(--tg-fw-regular);
    font-size: var(--tg-body-font-size);
}
.list-wrap {
    margin: 0;
    padding: 0;
}
.list-wrap li {
    list-style: none;
}
p {
    margin-bottom: 15px;
}
hr {
    border-bottom: 1px solid var(--tg-common-color-gray);
    border-top: 0;
    margin: 30px 0;
    padding: 0;
}
label {
    color: var(--tg-heading-color);
    cursor: pointer;
}
input[type="color"] {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    background: 0 0;
    border: 0;
    cursor: pointer;
    height: 100%;
    width: 100%;
    padding: 0;
    border-radius: 50%;
}
::-moz-selection {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    text-shadow: none;
}
::selection {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    text-shadow: none;
}
::-moz-placeholder {
    color: var(--tg-body-color);
    font-size: var(--tg-body-font-size);
    opacity: 1;
}
::placeholder {
    color: var(--tg-body-color);
    font-size: var(--tg-body-font-size);
    opacity: 1;
}
.clear {
    clear: both;
}
.container {
    padding-left: 15px;
    padding-right: 15px;
    max-width: 1440px;
}
.row {
    --bs-gutter-x: 30px;
}
.gutter-y-30 {
    --bs-gutter-y: 30px;
}
.gx-0 {
    --bs-gutter-x: 0;
}
.comment-form .row,
.contact-form-wrap .row,
.event__item-wrap .row,
.gutter-20 {
    --bs-gutter-x: 20px;
}
@media (max-width: 1500px) {
    .container {
        max-width: 1320px;
    }
}
@media (max-width: 1199.98px) {
    .container {
        max-width: 960px;
    }
}
@media (max-width: 991.98px) {
    .container {
        max-width: 720px;
    }
}
@media (max-width: 767.98px) {
    .container {
        max-width: 100%;
    }
}
.custom-container {
    max-width: 1680px;
}
@media (max-width: 1800px) {
    .custom-container {
        max-width: 1680px;
    }
}
@media (max-width: 1500px) {
    .custom-container {
        max-width: 1320px;
    }
}
@media (max-width: 1199.98px) {
    .custom-container {
        max-width: 960px;
    }
}
@media (max-width: 991.98px) {
    .custom-container {
        max-width: 720px;
    }
}
@media (max-width: 767.98px) {
    .custom-container {
        max-width: 100%;
    }
}
.include-bg {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}
.tg-button-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
}
.btn {
    user-select: none;
    -moz-user-select: none;
    background: var(--tg-theme-primary) none repeat scroll 0 0;
    border: none;
    color: var(--tg-common-color-white);
    cursor: pointer;
    display: inline-block;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 1.12;
    margin-bottom: 0;
    padding: 16px 30px;
    text-align: center;
    text-transform: capitalize;
    touch-action: manipulation;
    transition: 0.3s ease-out;
    -webkit-border-radius: 50px;
    -moz-border-radius: 50px;
    -o-border-radius: 50px;
    -ms-border-radius: 50px;
    border-radius: 50px;
    white-space: nowrap;
    box-shadow: 4px 6px 0 0 var(--tg-common-color-blue);
}
.btn-three,
.btn-two {
    background: var(--tg-theme-secondary);
}
.btn:focus-visible,
.btn:hover {
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
    box-shadow: 0 0 0 0 var(--tg-common-color-blue);
}
.btn.btn-border,
.btn.white-btn {
    background: var(--tg-common-color-white);
}
.btn .text {
    display: block;
}
.about__contact,
.btn.tg-svg {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 10px;
}
.btn .svg-icon {
    width: 14px;
    display: block;
    margin-top: -3px;
}
.btn.btn-border {
    border: 1px solid var(--tg-theme-primary);
    color: var(--tg-theme-primary);
    padding: 19px 23px 16px;
}
.btn.btn-border svg path {
    stroke: var(--tg-theme-primary);
}
.btn.btn-border:focus-visible,
.btn.btn-border:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.btn.btn-border:focus-visible svg path,
.btn.btn-border:hover svg path {
    stroke: var(--tg-common-color-white);
}
.btn.white-btn {
    color: var(--tg-theme-secondary);
}
.btn.white-btn svg path {
    stroke: var(--tg-theme-secondary);
}
.btn-two {
    border: 2px solid var(--tg-common-color-black-3);
    -webkit-box-shadow: 4px 4px 0 0 #3d3d3d;
    -moz-box-shadow: 4px 4px 0 0 #3d3d3d;
    -ms-box-shadow: 4px 4px 0 0 #3d3d3d;
    -o-box-shadow: 4px 4px 0 0 #3d3d3d;
    box-shadow: 4px 4px 0 0 #3d3d3d;
    color: var(--tg-common-color-black-3);
}
.btn-three,
.btn-three svg,
.btn-two svg {
    color: var(--tg-common-color-black-3) !important;
}
.btn-two:focus-visible,
.btn-two:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    box-shadow: 0 0 0 0 var(--tg-common-color-blue);
    border-color: var(--tg-theme-primary);
}
.btn-two:focus-visible svg,
.btn-two:hover svg {
    color: var(--tg-common-color-white) !important;
}
.btn-three {
    box-shadow: none;
}
.btn-four {
    box-shadow: none;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -o-border-radius: 8px;
    -ms-border-radius: 8px;
    border-radius: 8px;
}
.arrow-btn {
    --arrow-hover-move-x: -110%;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: 0.3s linear;
}
.arrow-btn svg {
    color: var(--tg-common-color-white);
    width: 15px;
    transition: 0.3s ease-out;
    transform: translateY(-1px);
}
.arrow-btn svg path {
    transition: transform 0.38s cubic-bezier(0.37, 0.08, 0.02, 0.93),
        opacity 0.18s ease-out;
}
.arrow-btn svg path:first-of-type {
    transform: translateX(0);
    opacity: 1;
    transition-delay: 0.15s, 0.15s;
}
.arrow-btn svg path:nth-of-type(2) {
    transform: translateX(calc(1 * var(--arrow-hover-move-x)));
    opacity: 0.5;
    transition-delay: 0s, 0s;
}
.arrow-btn:focus-visible svg,
.arrow-btn:hover svg {
    color: var(--tg-heading-color);
}
.arrow-btn:focus-visible svg path:first-of-type,
.arrow-btn:hover svg path:first-of-type {
    transform: translateX(calc(-1 * var(--arrow-hover-move-x)));
    opacity: 0;
    transition-delay: 0s, 0s;
}
.arrow-btn:focus-visible svg path:nth-of-type(2),
.arrow-btn:hover svg path:nth-of-type(2) {
    transform: translateX(0) translateY(0);
    opacity: 1;
    transition-delay: 0.15s, 0.15s;
}
#preloader .loader .loader-container,
#preloader .loader .loader-container::before {
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    top: 50%;
    left: 50%;
    position: absolute;
}
#preloader {
    background-color: var(--tg-common-color-white);
    width: 100%;
    margin-top: 0;
    z-index: 9999;
}
#preloader .loader .loader-container {
    transform: translate(-50%, -50%);
    border: 3px solid var(--tg-common-color-gray);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
#preloader .loader .loader-container::before {
    content: "";
    display: block;
    transform: translate(-50%, -50%);
    border-top: 3px solid var(--tg-theme-primary);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    animation: 1.8s ease-in-out infinite loaderspin;
    -webkit-animation: 1.8s ease-in-out infinite loaderspin;
}
#preloader .loader .loader-icon,
.about__images .popup-video {
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}
#preloader .loader .loader-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    text-align: center;
}
#preloader .loader .loader-icon img {
    animation: 0.9s infinite alternate loaderpulse;
    width: 40px;
}
@keyframes loaderspin {
    0% {
        transform: translate(-50%, -50%) rotate(0);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}
@-webkit-keyframes loaderspin {
    0% {
        transform: translate(-50%, -50%) rotate(0);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}
@keyframes loaderpulse {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.2);
    }
}
.grey-bg {
    background: var(--tg-common-color-gray);
}
.grey-bg-two {
    background: var(--tg-common-color-gray-9);
}
.white-bg {
    background: var(--tg-common-color-white);
}
.black-bg,
.footer__area {
    background: var(--tg-common-color-black);
}
.offCanvas__info {
    background: var(--tg-common-color-white) none repeat scroll 0 0;
    padding: 30px;
    right: 0;
    transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -moz-transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -webkit-transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -ms-transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -o-transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    width: 340px;
    z-index: 999;
    overflow-y: scroll;
    -webkit-transform: translateX(100%);
    -moz-transform: translateX(100%);
    -ms-transform: translateX(100%);
    -o-transform: translateX(100%);
    transform: translateX(100%);
}
.offCanvas__info.active {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
}
.offCanvas__info::-webkit-scrollbar {
    width: 0;
}
.offCanvas__close-icon {
    margin-top: -16px;
    text-align: right;
}
.offCanvas__close-icon button {
    background: 0 0;
    border: 0;
    color: var(--tg-theme-primary);
    cursor: pointer;
    font-size: 20px;
    padding: 0;
}
.offCanvas__logo img {
    max-height: 34px;
}
.offCanvas__side-info {
    border-top: 1px solid var(--tg-theme-primary);
    padding-top: 25px;
}
.offCanvas__side-info .contact-list h4 {
    color: var(--tg-heading-color);
    font-weight: 700;
    font-size: 18px;
}
.offCanvas__side-info .contact-list p {
    color: var(--tg-body-color);
    margin: 0 0 2px;
    line-height: 26px;
}
.offCanvas__social-icon a {
    color: var(--tg-theme-primary);
    display: inline-block;
    margin-right: 20px;
    text-align: center;
}
.offCanvas__social-icon a:hover {
    color: var(--tg-theme-secondary);
}
.offCanvas__overly {
    background: #000;
    left: 0;
    width: 100%;
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s ease-out;
}
.offCanvas__overly.active {
    opacity: 0.7;
    visibility: visible;
}
.breadcrumb__bg {
    background-size: cover;
    background-position: center;
    padding: 107px 0;
    position: relative;
    z-index: 1;
    overflow: hidden;
}
.breadcrumb__bg-two {
    padding: 61px 0;
}
.breadcrumb__bg-two .breadcrumb__shape-wrap img:nth-child(3),
.tgmenu__main-menu li.menu-item-has-children .dropdown-btn {
    display: none;
}
.breadcrumb__bg-three {
    min-height: 300px;
}
.breadcrumb__content .title {
    margin-bottom: 10px;
    font-size: 40px;
    line-height: 1.2;
}
@media (max-width: 1199.98px) {
    .breadcrumb__content .title {
        font-size: 36px;
    }
}
@media (max-width: 767.98px) {
    .breadcrumb__content .title {
        font-size: 30px;
    }
    .breadcrumb__shape-wrap img:nth-child(2) {
        display: none;
    }
}
.breadcrumb__content .breadcrumb {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    margin-bottom: 0;
    gap: 10px;
}
.breadcrumb__content .breadcrumb > * {
    font-size: 16px;
    color: var(--tg-theme-primary);
}
.breadcrumb__content .breadcrumb > * a {
    color: var(--tg-common-color-dark);
}
.breadcrumb__content .breadcrumb .breadcrumb-separator {
    line-height: 1;
    font-size: 16px;
    font-weight: 700;
    margin-top: 2px;
    opacity: 0.5;
    color: var(--tg-common-color-gray-3);
}
.breadcrumb__shape-wrap img {
    position: absolute;
    z-index: -1;
}
.breadcrumb__shape-wrap img:first-child {
    left: 100px;
    top: 62px;
}
.breadcrumb__shape-wrap img:nth-child(2) {
    right: 32%;
    top: 21%;
}
.breadcrumb__shape-wrap img:nth-child(3) {
    right: 20%;
    bottom: 15%;
}
@media (max-width: 1500px) {
    .breadcrumb__bg {
        padding: 100px 0;
    }
    .breadcrumb__shape-wrap img:first-child {
        left: 69px;
        top: 52px;
    }
    .breadcrumb__shape-wrap img:nth-child(2) {
        right: 38%;
    }
    .breadcrumb__shape-wrap img:nth-child(3) {
        right: 23%;
        bottom: 13%;
    }
}
.breadcrumb__shape-wrap img:nth-child(4) {
    right: 8%;
    top: 8%;
}
.breadcrumb__shape-wrap img:nth-child(5) {
    right: 0;
    top: 0;
}
.tgmenu__search-form .select2-container {
    margin-left: -29px;
}
.tgmenu__search-form
    .select2-container
    .select2-selection--single
    .select2-selection__rendered {
    padding-left: 37px;
    padding-right: 28px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    font-size: 14px;
    font-family: var(--tg-heading-font-family);
    color: var(--tg-heading-color);
    font-weight: 500;
}
.tgmenu__search-form .select2-container .select2-selection--single {
    height: auto;
}
.course-category-dropdown {
    background: var(--tg-common-color-white);
    border: 1px solid var(--tg-border-2);
    margin: 0;
}
.course-category-dropdown .select2-results__option--selectable:hover,
.scroll__top,
.section__title.white-title .sub-title {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.course-category-dropdown .select2-results__options {
    margin-top: 7px;
}
.course-category-dropdown .select2-results__option {
    padding: 1px 10px;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    transition: 0.3s ease-out;
}
.scroll__top {
    width: 40px;
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: -10%;
    right: 50px;
    font-size: 16px;
    border-radius: 4px;
    z-index: 99;
    text-align: center;
    cursor: pointer;
    transition: 1s;
    border: none;
}
@media (max-width: 1199.98px) {
    .breadcrumb__shape-wrap img:nth-child(5) {
        right: -30px;
        width: 400px;
        height: 285px;
    }
    .scroll__top {
        right: 25px;
        bottom: 25px;
    }
}
@media (max-width: 991.98px) {
    .breadcrumb__shape-wrap img:nth-child(2) {
        right: 31%;
    }
    .breadcrumb__shape-wrap img:nth-child(4) {
        right: 11%;
        top: 16%;
    }
    .breadcrumb__shape-wrap img:nth-child(5),
    .tg-header__top {
        display: none;
    }
    .scroll__top {
        right: 30px;
    }
}
.scroll__top.open {
    bottom: 30px;
}
.scroll__top::after {
    position: absolute;
    z-index: -1;
    content: "";
    top: 100%;
    left: 5%;
    height: 10px;
    width: 90%;
    opacity: 1;
    background: radial-gradient(
        ellipse at center,
        rgba(0, 0, 0, 0.25) 0,
        rgba(0, 0, 0, 0) 80%
    );
}
.scroll__top:hover,
.tgmenu__action-eight .list-wrap li .mini-cart-count {
    background: var(--tg-theme-secondary);
    color: var(--tg-common-color-white);
}
.section__title .sub-title {
    display: inline-block;
    line-height: 1.62;
    background: var(--tg-common-color-gray-2);
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
    padding: 3px 16px;
    font-weight: var(--tg-fw-medium);
    color: var(--tg-theme-primary);
    margin: 0 0 14px;
}
.section__title .title {
    font-size: 36px;
    line-height: 1.33;
    margin: 0;
    letter-spacing: -0.75px;
    text-transform: capitalize;
}
@media (max-width: 767.98px) {
    .scroll__top {
        right: 15px;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
    }
    .scroll__top.open {
        bottom: 15px;
    }
    .section__title .title {
        font-size: 29px;
    }
}
.section__title .title span:not(.svg-icon) {
    color: var(--tg-theme-primary);
    position: relative;
    color: var(--tg-common-color-white);
    padding: 0 13px 0 20px;
    font-weight: var(--tg-fw-bold);
    display: inline-block;
    z-index: 1;
}
.tg-header__contact .content a:hover,
.tgmenu__navbar-wrap ul li .sub-menu li.active > a,
.tgmenu__navbar-wrap ul li .sub-menu li:hover > a,
.tgmenu__navbar-wrap > ul > li.active a,
.tgmenu__navbar-wrap > ul > li:hover a {
    color: var(--tg-theme-primary);
}
.section__title .title span:not(.svg-icon) > svg {
    position: absolute;
    left: 0;
    top: -5px;
    width: 100%;
    height: 59px;
    z-index: -1;
    color: var(--tg-theme-secondary);
}
@media (max-width: 767.98px) {
    .section__title .title span:not(.svg-icon) > svg {
        height: 45px;
        top: -2px;
    }
    .section__title p br {
        display: none;
    }
}
.section__title .title.bold {
    font-weight: 700;
}
.section__title .title .svg-icon {
    position: absolute;
    right: -50px;
    top: -50px;
    width: 61px;
    height: 68px;
}
.section__title .title .svg-icon svg {
    display: block;
    width: 100%;
    height: 100%;
}
.section__title p {
    margin-bottom: 0;
    margin-top: 15px;
}
.section__title.white-title .title,
.section__title.white-title .title span:not(.svg-icon) {
    color: var(--tg-common-color-white);
}
.section__title.white-title p {
    color: #acaacc;
}
.section__title .desc {
    margin: 10px 0 0;
}
.section__content p {
    margin-bottom: 0;
    color: #bcbad8;
}
.search__popup {
    padding-top: 70px;
    padding-bottom: 100px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: var(--tg-common-color-white);
    backdrop-filter: blur(10px);
    z-index: 99;
    -webkit-transform: translateY(calc(-100% - 80px));
    -moz-transform: translateY(calc(-100% - 80px));
    -ms-transform: translateY(calc(-100% - 80px));
    -o-transform: translateY(calc(-100% - 80px));
    transform: translateY(calc(-100% - 80px));
    -webkit-transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    -moz-transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    transition: transform 0.3s ease-in-out 0.5s, opacity 0.3s ease-in-out 0.5s;
}
.search__input,
.search__input::after {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.search__popup.search-opened {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
    transition-delay: 0s;
    z-index: 99999999;
}
.search__popup.search-opened .search__input {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    transition-delay: 0.3s;
}
.search__popup.search-opened .search__input::after {
    width: 100%;
    transition-delay: 0.5s;
}
.search__input {
    position: relative;
    height: 80px;
    -webkit-transform: translateY(-40px);
    -moz-transform: translateY(-40px);
    -ms-transform: translateY(-40px);
    -o-transform: translateY(-40px);
    transform: translateY(-40px);
    transition: 0.3s ease-out 0.5s;
    opacity: 0;
}
.search__input button,
.tgmenu__search-form .select-grp::after {
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    top: 50%;
}
.search__input::after {
    position: absolute;
    content: "";
    left: 0;
    bottom: 0;
    width: 0%;
    height: 1px;
    background-color: var(--tg-theme-secondary);
    transition: 0.3s ease-out;
}
.search-popup-overlay,
.tgmenu__navbar-wrap ul li .sub-menu {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    opacity: 0;
    visibility: hidden;
}
.search__input input {
    width: 100%;
    height: 100%;
    background-color: transparent;
    border: 0;
    outline: 0;
    font-size: 24px;
    color: var(--tg-heading-color);
    border-bottom: 1px solid transparent;
    padding: 0 30px 0 0;
}
.search__input input::placeholder {
    font-size: 24px;
}
.search__input button {
    position: absolute;
    right: 0;
    transform: translateY(-50%);
    font-size: 18px;
    color: var(--tg-theme-secondary);
    border: none;
    padding: 0;
    background: 0 0;
}
.tg-header__phone,
.tg-header__top-info li,
.tg-header__top-social li {
    font-size: 14px;
    font-weight: var(--tg-fw-medium);
}
.search__input-two::after {
    background: var(--tg-theme-primary-two);
}
.search-close-btn-two,
.search__input-two button {
    color: var(--tg-theme-primary-two);
}
.search__close {
    position: absolute;
    top: 10%;
    right: 2%;
    z-index: 2;
}
.search-close-btn {
    margin: 0;
    padding: 0;
    border: none;
    color: var(--tg-theme-secondary);
    cursor: pointer;
    background: 0 0;
}
.search-popup-overlay {
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 4;
    transition: 0.3s ease-out 0.5s;
    background: #000d25;
}
.search-popup-overlay.search-popup-overlay-open {
    opacity: 0.55;
    visibility: visible;
    transition-delay: 0s;
}
.search-input-field ~ .search-focus-border {
    position: absolute;
    bottom: 0;
    left: auto;
    right: 0;
    width: 0;
    height: 1px;
    background-color: var(--tg-theme-primary);
    transition: 0.5s;
}
.search-input-field:focus ~ .search-focus-border {
    width: 100%;
    left: 0;
    right: auto;
    transition: 0.5s;
}
.transparent-header {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 9;
    height: auto;
}
.tg-header__top {
    background-color: var(--tg-common-color-black-2);
    padding: 11px 0;
}
.tg-header__top-info {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 10px 27px;
}
.tg-header__top-info li,
.tg-header__top-right,
.tg-header__top-social {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.tg-header__top-info li {
    display: flex;
    align-items: center;
    color: #e6eaef;
    gap: 7px;
}
.tg-header__phone a:hover,
.tg-header__top-info li a:hover,
.tg-header__top-info li i,
.tg-header__top-social li,
.tg-header__top-social li a:hover {
    color: var(--tg-common-color-white);
}
.tg-header__top-info li i {
    font-size: 20px;
}
.tg-header__top-info li a {
    color: #e6eaef;
}
.tg-header__top-right {
    display: flex;
    align-items: center;
    gap: 10px 27px;
    justify-content: flex-end;
}
.tg-header__top-social {
    display: flex;
    align-items: center;
    gap: 10px 12px;
    justify-content: flex-end;
}
.tg-header__top-social li:first-child {
    opacity: 0.5;
}
.tg-header__top-social li a {
    color: #e7effc;
}
.tg-header__top-btn .btn {
    border-radius: 0;
    box-shadow: none;
    padding: 14px 22px;
}
.tg-header__area.sticky-menu,
.tg-header__top-two {
    padding: 0;
}
.tg-header__phone {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #e6eaef;
}
.tg-header__phone a {
    color: #e6eaef;
}
.tg-header__area {
    padding: 5px 0;
}
.tg-header__area .mobile-nav-toggler {
    position: relative;
    font-size: 30px;
    cursor: pointer;
    line-height: 1;
    color: var(--tg-theme-primary);
    display: none;
}
.tg-header__style-two .tgmenu__search-form {
    width: 460px;
}
@media (max-width: 1500px) {
    .tg-header__area {
        padding: 10px 0;
    }
    .tg-header__style-two .tgmenu__search-form {
        width: 330px;
    }
}
@media (max-width: 1199.98px) {
    .tg-header__area {
        padding: 20px 0;
    }
    .tg-header__area .mobile-nav-toggler {
        display: block;
    }
    .tg-header__area.sticky-menu {
        padding: 20px 0;
    }
    .tg-header__style-two .tgmenu__search-form {
        width: 400px;
    }
    .tg-header__style-four {
        padding: 25px 0;
    }
}
.tg-header__style-two .tgmenu__navbar-wrap ul {
    margin: 0 auto 0 50px;
}
.tg-header__style-two .tgmenu__navbar-wrap ul li .mega-menu {
    left: -192px;
}
.tg-header__style-three {
    border-bottom: 1px solid #e0e0e0;
}
.tg-header__style-three .tgmenu__navbar-wrap ul {
    margin: 0 auto 0 170px;
}
.tg-header__style-three .tgmenu__navbar-wrap ul li .mega-menu {
    left: -318px;
}
.related-product-area .shop-thumb img,
.tg-header__style-eight .tgmenu__navbar-wrap ul,
.tg-header__style-four .tgmenu__navbar-wrap ul,
.tg-header__style-six .tgmenu__navbar-wrap ul {
    margin: 0 auto;
}
.tg-header__style-four .tgmenu__navbar-wrap ul li .mega-menu {
    left: -345px;
}
.tg-header__style-four .tgmenu__navbar-wrap ul li a {
    padding: 37px 15px;
}
.tg-header__style-five .tgmenu__navbar-wrap ul {
    margin: 0 auto 0 30px;
}
.tg-header__style-five .tgmenu__navbar-wrap ul li .mega-menu {
    left: -180px;
}
.tg-header__style-six .tgmenu__navbar-wrap ul li .mega-menu {
    left: -390px;
}
.tg-header__style-seven .tgmenu__navbar-wrap ul {
    margin: 0 auto 0 100px;
}
.tg-header__style-eight .tgmenu__navbar-wrap ul a {
    padding: 32px 10px;
}
.tg-header__style-eight .tgmenu__navbar-wrap ul li .mega-menu {
    left: -515px;
}
.tg-header__logo-area {
    margin-top: 32px;
}
.tg-header__contact {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 6px;
}
.tg-header__contact .icon,
.tgmenu__nav,
.tgmenu__navbar-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.tg-header__contact .icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 auto;
    background: #f3f3f3;
    color: var(--tg-heading-color);
    font-size: 18px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.tg-header__contact .content span {
    display: block;
    font-weight: 500;
    font-size: 12px;
    color: #868686;
    line-height: 1;
}
.tg-header__contact .content a {
    display: inline-block;
    font-weight: 600;
    font-size: 16px;
    line-height: 1;
    color: var(--tg-heading-color);
}
.tgmenu__nav {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: flex-start;
}
.tgmenu__navbar-wrap {
    display: flex;
    flex-grow: 1;
}
.tgmenu__navbar-wrap ul,
.tgmenu__navbar-wrap ul li a,
.tgmenu__search-form,
.tgmenu__search-form .select-grp {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.tgmenu__navbar-wrap ul {
    display: flex;
    padding: 0;
    flex-direction: row;
    flex-wrap: wrap;
    margin: 0 auto 0 73px;
}
.tgmenu__navbar-wrap ul li {
    display: block;
    position: relative;
    list-style: none;
}
.tgmenu__navbar-wrap ul li a {
    font-size: 16px;
    font-weight: var(--tg-fw-medium);
    text-transform: capitalize;
    color: var(--tg-heading-color);
    font-family: var(--tg-heading-font-family);
    padding: 37px 10px;
    display: flex;
    align-items: center;
    line-height: 1;
    position: relative;
    z-index: 1;
}
.tgmenu__navbar-wrap ul li .sub-menu {
    position: absolute;
    left: 0;
    top: 100%;
    min-width: 230px;
    border: 1px solid #f5f5f5;
    background: var(--tg-common-color-white);
    margin: 0;
    -webkit-transform: scale(1, 0);
    -moz-transform: scale(1, 0);
    -ms-transform: scale(1, 0);
    -o-transform: scale(1, 0);
    transform: scale(1, 0);
    transform-origin: 0 0;
    transition: 0.3s ease-out;
    -webkit-box-shadow: 0 30px 70px 0 rgba(137, 139, 142, 0.15);
    -moz-box-shadow: 0 30px 70px 0 rgba(137, 139, 142, 0.15);
    box-shadow: 0 30px 70px 0 rgba(137, 139, 142, 0.15);
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    padding: 18px 0;
    display: block;
    z-index: 9;
}
.tgmenu__navbar-wrap ul li .sub-menu .sub-menu {
    right: auto;
    left: 100%;
    top: 0;
}
.tgmenu__navbar-wrap ul li .sub-menu li {
    margin-left: 0;
    text-align: left;
    display: block;
}
.tgmenu__navbar-wrap ul li .sub-menu li a {
    padding: 8px 15px 8px 25px;
    line-height: 1.4;
    display: block;
    color: var(--tg-heading-color);
    text-transform: capitalize;
    font-size: 15px;
}
.tgmenu__navbar-wrap ul li:hover > .sub-menu {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}
.tgmenu__navbar-wrap ul li .mega-menu {
    min-width: 1400px;
    display: flex;
    align-items: flex-start;
    left: -100px;
    padding: 60px 60px 60px 50px;
}
.tgmenu__navbar-wrap ul li .mega-menu > li {
    border-right: 1px solid #d6d6d6;
}
.tgmenu__navbar-wrap ul li .mega-menu > li:nth-child(2) {
    border: none;
}
.tgmenu__navbar-wrap ul li .mega-menu > li:nth-child(3) {
    border: none;
    margin-left: auto;
}
.tgmenu__navbar-wrap ul li .mega-menu > li > .mega-sub-menu {
    display: flex;
    flex-direction: column;
    min-width: 260px;
    margin-left: 10px !important;
}
.tgmenu__main-menu li.menu-item-has-children > a::after {
    content: "\f105";
    display: block;
    font-family: flaticon_skill_grow;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
    font-size: 14px;
    font-weight: var(--tg-fw-bold);
    margin-left: 6px;
}
.tgmenu__main-menu
    li.menu-item-has-children
    > .sub-menu
    .menu-item-has-children {
    position: relative;
}
.tgmenu__main-menu
    li.menu-item-has-children
    > .sub-menu
    .menu-item-has-children
    > a {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tgmenu__main-menu
    li.menu-item-has-children
    > .sub-menu
    .menu-item-has-children
    > a::after {
    content: "\f105";
    font-family: flaticon_skill_grow;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
    font-size: 14px;
    font-weight: var(--tg-fw-bold);
    margin-left: 6px;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    transition: 0.3s ease-out;
}
.tgmenu__main-menu
    li.menu-item-has-children
    > .sub-menu
    .menu-item-has-children.active
    a::after,
.tgmenu__main-menu
    li.menu-item-has-children
    > .sub-menu
    .menu-item-has-children:hover
    a::after {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
}
.tgmenu__search {
    margin-inline-end: 25px;
}
.tgmenu__search-form {
    display: flex;
    width: 535px;
    border-radius: 100px;
    border: 1px solid #d3d2df;
}
@media (max-width: 1800px) {
    .tgmenu__search-form {
        width: 485px;
    }
}
@media (max-width: 1500px) {
    .tg-header__style-two .tgmenu__navbar-wrap ul {
        margin: 0 auto 0 25px;
    }
    .tg-header__style-two .tgmenu__navbar-wrap ul li .mega-menu {
        left: -100px;
    }
    .tg-header__style-three .tgmenu__navbar-wrap ul {
        margin: 0 auto 0 100px;
    }
    .tg-header__style-four .tgmenu__navbar-wrap ul li .mega-menu,
    .tg-header__style-three .tgmenu__navbar-wrap ul li .mega-menu {
        left: -185px;
    }
    .tg-header__style-five .tgmenu__navbar-wrap ul {
        margin: 0 auto 0 20px;
    }
    .tg-header__style-five .tgmenu__navbar-wrap ul li .mega-menu {
        left: -300px;
    }
    .tg-header__style-six .tgmenu__navbar-wrap ul li .mega-menu {
        left: -250px;
    }
    .tg-header__style-seven .tgmenu__navbar-wrap ul li .mega-menu {
        left: -195px;
    }
    .tg-header__style-eight .tgmenu__navbar-wrap ul a {
        padding: 25px 10px;
    }
    .tg-header__style-eight .tgmenu__navbar-wrap ul li .mega-menu {
        left: -360px;
    }
    .tg-header__logo-area {
        margin-top: 25px;
    }
    .tgmenu__navbar-wrap ul {
        margin: 0 auto 0 20px;
    }
    .tgmenu__navbar-wrap ul li a {
        padding: 37px 7px;
    }
    .tgmenu__navbar-wrap ul li .mega-menu {
        min-width: 1100px;
        padding: 60px 40px 60px 20px;
    }
    .tgmenu__main-menu li.menu-item-has-children > a::after {
        margin-left: 4px;
    }
    .tgmenu__search {
        margin-inline-end: 15px;
    }
    .tgmenu__search-form {
        width: 355px;
    }
}
@media (max-width: 1199.98px) {
    .tg-header__style-eight .logo.d-none {
        display: block !important;
    }
    .tg-header__contact,
    .tg-header__logo-area {
        display: none;
    }
    .tgmenu__search {
        margin-left: auto;
    }
    .tgmenu__search-form {
        width: 410px;
    }
}
.tgmenu__search-form .select-grp {
    display: flex;
    align-items: center;
    background: 0 0;
    border: none;
    padding: 12px 0 12px 17px;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -o-border-radius: 0;
    -ms-border-radius: 0;
    border-radius: 0;
    width: 153px;
    flex: 0 0 auto;
    position: relative;
}
.tgmenu__search-form .select-grp::after {
    content: "";
    position: absolute;
    right: 0;
    transform: translateY(-50%);
    width: 1px;
    height: 20px;
    background: #bdbabb;
}
.tgmenu__action > ul li .cart-count span,
.tgmenu__search-form [type="submit"]:hover {
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
}
.tgmenu__search-form .select-grp svg {
    width: 16px;
    height: 16px;
    flex: 0 0 auto;
}
.tgmenu__search-form .select-grp path {
    fill: var(--tg-theme-primary);
}
.tgmenu__search-form .form-select {
    outline: 0;
    box-shadow: none;
    border: none;
    padding-left: 8px;
    color: var(--tg-heading-color);
    font-family: var(--tg-heading-font-family);
    font-size: 14px;
}
.tgmenu__search-form .input-grp {
    position: relative;
    flex-grow: 1;
    margin-left: -1px;
}
.tgmenu__search-form input {
    display: block;
    width: 100%;
    background: 0 0;
    border: none;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -o-border-radius: 0;
    -ms-border-radius: 0;
    border-radius: 0;
    padding: 12px 50px 10px 20px;
    height: 50px;
    font-size: 15px;
    font-family: var(--tg-heading-font-family);
}
.tgmenu__search-form input::placeholder {
    color: #8d9db5;
    font-size: 14px;
}
.tgmenu__search-form [type="submit"] {
    position: absolute;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 4px;
    width: 44px;
    height: 44px;
    border: none;
    padding: 0;
    background: var(--tg-theme-primary);
    font-size: 20px;
    color: var(--tg-common-color-white);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.tgmenu__action > ul li.header-contact .content a:hover,
.tgmenu__search-bar form [type="submit"]:hover {
    color: var(--tg-theme-primary);
}
.tgmenu__search-form-two {
    width: 348px;
    background: var(--tg-common-color-gray-7);
    border-color: var(--tg-border-6);
}
@media (max-width: 1800px) {
    .tgmenu__search-form-two {
        width: 300px;
    }
}
.tgmenu__search-form-two input {
    padding: 12px 50px 10px 20px;
    height: 44px;
    font-size: 13px;
    color: var(--tg-heading-color);
    font-weight: 500;
}
.tgmenu__search-form-two input::placeholder {
    font-size: 13px;
    color: var(--tg-body-color);
    font-weight: 500;
}
.tgmenu__search-form-two [type="submit"] {
    width: auto;
    height: auto;
    background: 0 0;
    font-size: 24px;
    color: var(--tg-theme-primary);
    right: 12px;
}
.tgmenu__search-form-two [type="submit"]:hover {
    color: var(--tg-theme-secondary);
    background: 0 0;
}
.tgmenu__search-bar {
    flex-grow: 1;
}
.tgmenu__search-bar form {
    position: relative;
}
.tgmenu__search-bar form input {
    display: block;
    width: 100%;
    background: var(--tg-common-color-white);
    border: 1px solid var(--tg-border-2);
    -webkit-border-radius: 50px;
    -moz-border-radius: 50px;
    -o-border-radius: 50px;
    -ms-border-radius: 50px;
    border-radius: 50px;
    padding: 12px 50px 10px 20px;
    height: 45px;
    font-size: 15px;
    font-family: var(--tg-heading-font-family);
}
.tgmenu__search-bar form input::placeholder {
    color: #8d9db5;
    font-size: 14px;
}
.tgmenu__search-bar form [type="submit"] {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%) rotate(-90deg);
    -moz-transform: translateY(-50%) rotate(-90deg);
    -ms-transform: translateY(-50%) rotate(-90deg);
    -o-transform: translateY(-50%) rotate(-90deg);
    transform: translateY(-50%) rotate(-90deg);
    right: 10px;
    border: none;
    padding: 0;
    background: 0 0;
    font-size: 24px;
    color: #8d9db5;
}
.tgmenu__search-two {
    margin-inline-end: 0;
}
.banner__contact,
.tgmenu__action > ul {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 8px;
}
.tg-badge,
.tg-badge-two {
    display: inline-block;
    line-height: 1;
    font-size: 13px;
    padding: 3px 8px;
    font-family: var(--tg-heading-font-family);
}
.tgmenu__action > ul li {
    position: relative;
    padding: 0 6px 0 0;
    flex: 0 0 auto;
}
.tgmenu__action > ul li .cart-count,
.tgmenu__action > ul li .cart-count span {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.tgmenu__action > ul li .cart-count {
    display: flex;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid #7f7e97;
    color: #7f7e97;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.tgmenu__action > ul li .cart-count span {
    position: absolute;
    top: -9px;
    right: 0;
    width: 22px;
    height: 22px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    z-index: 1;
}
.tgmenu__action > ul li .cart-count:hover {
    background: var(--tg-theme-primary);
    border-color: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.tgmenu__action > ul li:first-child {
    margin-left: 0;
}
.tgmenu__action > ul li.header-search a {
    font-size: 24px;
    line-height: 0;
    color: var(--tg-body-color);
}
.tgmenu__action > ul li.header-search a:hover {
    color: var(--tg-theme-secondary);
}
.mobile-login-btn a:hover,
.tgmenu__action-eight .list-wrap li.header-user a:hover,
.tgmenu__action-eight .list-wrap li.mini-cart-icon a:hover,
.tgmenu__action > ul li.header-user a:hover,
.tgmenu__action > ul li.login-btn a:hover {
    color: var(--tg-common-color-white);
    background: var(--tg-theme-primary);
}
.tgmenu__action > ul li.login-btn {
    padding: 0;
    margin-inline-start: 15px;
}
.tgmenu__action > ul li.login-btn a {
    display: block;
    border-radius: 50px;
    padding: 12px 26px;
    border: 1px solid rgba(6, 35, 91, 0.19);
    background: var(--tg-theme-secondary);
    font-size: 15px;
    color: var(--tg-heading-color);
    font-weight: 600;
    line-height: 18px;
}
.tgmenu__action > ul li.header-contact .icon,
.tgmenu__action > ul li.header-user a {
    width: 46px;
    height: 46px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.tgmenu__action > ul li.header-user a {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-common-color-gray-7);
    border-radius: 50%;
    color: var(--tg-body-color);
    line-height: 0;
    font-size: 18px;
}
.tgmenu__action > ul li.header-contact {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 5px;
}
.tgmenu__action > ul li.header-contact .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
    border-radius: 50%;
    font-size: 20px;
    line-height: 0;
}
.tgmenu__action > ul li.header-contact .icon svg {
    width: 20px;
    height: 20px;
}
.tgmenu__action > ul li.header-contact .content span {
    display: block;
    line-height: 1;
    font-weight: 500;
    font-size: 13px;
    color: #868686;
    margin-bottom: 6px;
}
.tgmenu__action > ul li.header-contact .content a {
    font-weight: 600;
    font-size: 18px;
    display: block;
    line-height: 1;
    color: var(--tg-heading-color);
}
.tgmenu__action-two .mini-cart-icon {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: flex-end;
    gap: 8px;
}
.tgmenu__action-two .mini-cart-icon .cart-count-two {
    position: relative;
    font-size: 30px;
    line-height: 0;
    color: var(--tg-common-color-black-2);
}
.tgmenu__action-two .mini-cart-icon .cart-count-two .mini-cart-count {
    position: absolute;
    width: 23px;
    height: 23px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    top: -10px;
    right: -13px;
}
.tgmenu__action-four > ul li.header-search a,
.tgmenu__action-four > ul li.offCanvas-menu .menu-tigger {
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    background: var(--tg-common-color-white);
}
.tgmenu__action-two .mini-cart-icon .cart-count-two:hover {
    color: var(--tg-theme-secondary);
}
.tgmenu__action-two .mini-cart-icon strong {
    font-size: 14px;
    font-weight: 500;
    color: var(--tg-common-color-black-2);
    line-height: 1.2;
}
.progress-item .title span,
.tgmenu__action-five,
.tgmenu__action-seven,
.tgmenu__action-six,
.tgmenu__action-three {
    margin-left: auto;
}
.tgmenu__action-three > ul {
    gap: 14px;
}
.tgmenu__action-three > ul li.header-btn .btn {
    font-size: 14px;
    font-weight: 600;
    padding: 16px 26px;
}
.tgmenu__action-four {
    margin-left: 80px;
}
.tgmenu__action-four > ul li.header-search a {
    width: 40px;
    height: 40px;
    border: 1px solid #d9d9d9;
    display: flex;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    font-size: 20px;
    line-height: 0;
}
.tgmenu__action-four > ul li.header-search a:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    border-color: var(--tg-theme-primary);
}
.tgmenu__action-four > ul li .cart-count {
    width: 40px;
    height: 40px;
    background: var(--tg-common-color-white);
    border: 1px solid #d9d9d9;
}
.contact-form-wrap .form-grp input:focus,
.contact-form-wrap .form-grp textarea:focus,
.tgmenu__action-four > ul li .cart-count:hover {
    border-color: var(--tg-theme-primary);
}
.tgmenu__action-four > ul li.header-btn {
    margin-left: 5px;
}
.tgmenu__action-four > ul li.header-btn .btn {
    font-size: 14px;
    font-weight: 600;
    padding: 13px 23px;
    box-shadow: none;
}
.tgmenu__action-four > ul li.offCanvas-menu {
    padding-right: 0;
}
.tgmenu__action-four > ul li.offCanvas-menu .menu-tigger {
    width: 44px;
    height: 44px;
    display: flex;
    justify-content: center;
    border: 1px solid #d9d9d9;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    color: var(--tg-body-color);
}
.tgmenu__action-four > ul li.offCanvas-menu .menu-tigger:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    border-color: var(--tg-theme-primary);
}
.tgmenu__action-five > ul li .cart-count {
    width: 46px;
    height: 46px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: var(--tg-common-color-gray-7);
    color: var(--tg-body-color);
    font-size: 20px;
    line-height: 0;
}
.tgmenu__action-five > ul li .cart-count span {
    background: var(--tg-heading-color);
    color: var(--tg-common-color-white);
}
.tgmenu__action-six .list-wrap {
    gap: 10px;
}
.brand-area .container-fluid,
.tgmenu__action-six .list-wrap li {
    padding: 0;
}
.tgmenu__action-six .list-wrap li.header-search a,
.tgmenu__action-six .list-wrap li.header-user a,
.tgmenu__action-six .list-wrap li.mini-cart-icon a {
    width: 42px;
    height: 42px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #c5c5c5;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    background: var(--tg-common-color-white);
    font-size: 20px;
    color: var(--tg-body-color);
}
.post-tag-two:hover,
.tgmenu__action-six .list-wrap li.header-search a:hover,
.tgmenu__action-six .list-wrap li.header-user a:hover,
.tgmenu__action-six .list-wrap li.mini-cart-icon a:hover {
    background: var(--tg-theme-primary);
    border-color: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.tgmenu__action-six .list-wrap li.header-btn .btn {
    box-shadow: none;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -o-border-radius: 8px;
    -ms-border-radius: 8px;
    border-radius: 8px;
    padding: 16px 20px;
}
.tgmenu__action-six .list-wrap li.mini-cart-icon {
    padding: 0 6px 0 0;
}
.tgmenu__action-seven .list-wrap li .select-grp-two {
    margin-left: 0;
    background: var(--tg-common-color-white);
}
.tgmenu__action-seven
    .list-wrap
    li
    .select-grp-two
    .select2-container
    .select2-selection--single
    .select2-selection__rendered {
    color: var(--tg-heading-color);
    font-weight: 500;
}
.tgmenu__action-seven .list-wrap li.header-search a,
.tgmenu__action-seven .list-wrap li.mini-cart-icon a {
    width: 46px;
    height: 46px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-common-color-white);
    border: 1px solid #cfcfcf;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    font-size: 20px;
    color: var(--tg-body-color);
}
.discover-courses-btn-two .btn:hover,
.shop-details-qty .wishlist-btn:hover,
.tgmenu__action-seven .list-wrap li.header-search a:hover,
.tgmenu__action-seven .list-wrap li.mini-cart-icon a:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    border-color: var(--tg-theme-primary);
}
.tgmobile__menu .navigation li,
.tgmobile__menu .navigation > li > ul > li:first-child {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.tgmenu__action-seven .list-wrap li.header-btn .btn {
    box-shadow: none;
}
.tgmenu__action-eight .list-wrap li.header-user a,
.tgmenu__action-eight .list-wrap li.mini-cart-icon a {
    width: 40px;
    height: 40px;
    color: var(--tg-heading-color);
}
.tgmenu__action-eight .list-wrap li.header-user a svg,
.tgmenu__action-eight .list-wrap li.mini-cart-icon a svg {
    width: 17px;
    height: auto;
}
.tgmenu__categories {
    margin-right: 12px;
}
.tgmenu__categories .dropdown-toggle {
    font-family: var(--tg-heading-font-family);
    border: 1px solid var(--tg-border-2);
    background-color: var(--tg-common-color-white);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -o-border-radius: 4px;
    -ms-border-radius: 4px;
    border-radius: 4px;
    color: var(--tg-body-color);
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    padding: 9px 17px;
    gap: 11px;
}
.tgmenu__categories .dropdown-toggle svg {
    color: var(--tg-theme-primary);
}
.tgmenu__categories .dropdown-toggle::after {
    content: "";
    display: block;
    margin: 0;
    border: none;
    background-image: url(../img/icons/down_arrow.svg);
    width: 12px;
    height: 6px;
}
.tgmenu__categories .dropdown-menu {
    width: 100%;
    min-width: 100%;
    background-color: var(--tg-common-color-white);
    border: 1px solid var(--tg-border-2);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -o-border-radius: 4px;
    -ms-border-radius: 4px;
    border-radius: 4px;
}
.tgmenu__categories .dropdown-item {
    color: var(--tg-body-color);
}
.tgmenu__categories .dropdown-item:focus,
.tgmenu__categories .dropdown-item:hover {
    background-color: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.mega-sub-menu li a {
    display: flex !important;
    align-items: center;
    gap: 8px;
}
.tg-badge {
    background: linear-gradient(90deg, #ff1515 0, #ed0fbc 100%);
    color: var(--tg-common-color-white);
    font-weight: 600;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -o-border-radius: 3px;
    -ms-border-radius: 3px;
    border-radius: 3px;
}
.tg-badge-two {
    color: var(--tg-common-color-white);
    font-weight: 600;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -o-border-radius: 3px;
    -ms-border-radius: 3px;
    border-radius: 3px;
    background: #39cabd;
}
.mega-menu-img a {
    padding: 0 !important;
}
.mega-menu-img a img {
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
}
.sticky-menu {
    position: fixed;
    left: 0;
    margin: auto;
    top: 0;
    width: 100%;
    z-index: 99;
    background: var(--tg-common-color-white);
    -webkit-animation: 1s ease-in-out fadeInDown;
    animation: 1s ease-in-out fadeInDown;
    -webkit-box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
    box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
    border-radius: 0;
}
.sticky-menu ul li .sub-menu {
    -webkit-border-radius: 0 0 6px 6px;
    -moz-border-radius: 0 0 6px 6px;
    -o-border-radius: 0 0 6px 6px;
    -ms-border-radius: 0 0 6px 6px;
    border-radius: 0 0 6px 6px;
}
#header-fixed-height.active-height {
    display: block;
    height: 90px;
}
.mobile-login-btn {
    margin-left: auto;
    display: none;
}
.mobile-login-btn a {
    margin-right: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    background: #f4f4f4;
    justify-content: center;
    border-radius: 50%;
    line-height: 0;
    color: var(--tg-body-color);
}
.mobile-login-btn-two a {
    background: var(--tg-common-color-white);
    border: 1px solid #d9d9d9;
    color: var(--tg-body-color);
}
.select-grp-two i,
.tgmobile__menu .close-btn,
.tgmobile__menu .navigation li.active > a {
    color: var(--tg-theme-primary);
}
.select-grp-two {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    border: 1px solid var(--tg-border-6);
    border-radius: 100px;
    background: #f6f6f6;
    position: relative;
    padding: 10px 15px;
    margin-left: 55px;
}
.select-grp-two .select2-container .select2-selection--single {
    height: auto;
}
.select-grp-two
    .select2-container
    .select2-selection--single
    .select2-selection__rendered {
    padding-left: 25px;
    padding-right: 18px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.1rem center;
    background-size: 16px 12px;
    font-size: 14px;
    font-family: var(--tg-heading-font-family);
    color: var(--tg-body-color);
    font-weight: 400;
}
.select-grp-two i {
    position: absolute;
    left: 17px;
    top: 50%;
    transform: translateY(-50%);
}
.tgmobile__search {
    padding: 0 20px 25px 25px;
}
.tgmobile__search form {
    position: relative;
}
.tgmobile__search input {
    display: block;
    width: 100%;
    border: none;
    padding: 10px 45px 10px 20px;
    font-size: 15px;
    height: 45px;
    background: var(--tg-common-color-gray);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -o-border-radius: 4px;
    -ms-border-radius: 4px;
    border-radius: 4px;
}
.tgmobile__search input::placeholder {
    font-size: 15px;
    color: var(--tg-body-color);
}
.tgmobile__search button {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    border: none;
    padding: 0;
    right: 20px;
    line-height: 1;
    background: 0 0;
    color: var(--tg-heading-color);
}
.tgmobile__menu {
    position: fixed;
    right: 0;
    top: 0;
    width: 300px;
    padding-right: 30px;
    max-width: 100%;
    height: 100%;
    z-index: 99;
    border-radius: 0;
    transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -moz-transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -webkit-transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -o-transition: 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    -webkit-transform: translateX(101%);
    -moz-transform: translateX(101%);
    -ms-transform: translateX(101%);
    -o-transform: translateX(101%);
    transform: translateX(101%);
}
.tgmobile__menu .navbar-collapse {
    display: block !important;
}
.tgmobile__menu .nav-logo {
    position: relative;
    padding: 30px 25px;
    text-align: left;
}
.tgmobile__menu .nav-logo img {
    width: 150px;
}
.tgmobile__menu .navigation {
    position: relative;
    display: block;
    width: 100%;
    float: none;
    margin: 0;
    padding: 0;
}
.courses__details-meta .list-wrap li:last-child::before,
.courses__item-bottom-two .list-wrap li:last-child::before,
.courses__item-content-bottom-two .list-wrap li:last-child::before,
.tgmobile__menu .mega-menu > li:nth-child(3),
.tgmobile__menu
    .navigation
    li.menu-item-has-children
    .dropdown-btn.open
    .plus-line::after,
.tgmobile__menu .navigation li > ul,
.tgmobile__menu .navigation li > ul > li > ul {
    display: none;
}
.tgmobile__menu .navigation li {
    position: relative;
    display: block;
}
.tgmobile__menu .navigation li.menu-item-has-children .dropdown-btn {
    position: absolute;
    right: 20px;
    top: 6px;
    width: 32px;
    height: 32px;
    text-align: center;
    font-size: 16px;
    line-height: 32px;
    color: var(--tg-heading-color);
    background: var(--tg-common-color-gray);
    cursor: pointer;
    border-radius: 2px;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
    z-index: 5;
}
.tgmobile__menu .navigation li.menu-item-has-children .dropdown-btn .plus-line {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%) rotate(0);
    -ms-transform: translate(-50%, -50%) rotate(0);
    transform: translate(-50%, -50%) rotate(0);
    border-radius: 10px;
    width: 12px;
    height: 2px;
    background-color: var(--tg-common-color-black);
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
}
.tgmobile__menu
    .navigation
    li.menu-item-has-children
    .dropdown-btn
    .plus-line::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%) rotate(0);
    -ms-transform: translate(-50%, -50%) rotate(0);
    transform: translate(-50%, -50%) rotate(0);
    border-radius: 10px;
    width: 2px;
    height: 12px;
    background-color: var(--tg-common-color-black);
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
}
.courses-widget .form-check-input:checked,
.tgmobile__menu .navigation li.menu-item-has-children .dropdown-btn.open {
    background-color: var(--tg-theme-primary);
}
.tgmobile__menu
    .navigation
    li.menu-item-has-children
    .dropdown-btn.open
    .plus-line {
    background-color: var(--tg-common-color-white);
}
.tgmobile__menu .navigation ul li a,
.tgmobile__menu .tgmenu__action > ul .header-btn {
    display: block;
}
.tgmobile__menu .navigation li > a {
    position: relative;
    display: block;
    line-height: 1.5;
    padding: 10px 60px 10px 25px;
    font-size: 16px;
    font-weight: 500;
    color: var(--tg-heading-color);
    text-transform: capitalize;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
    border: none;
}
.tgmobile__menu .navigation li > a::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 0;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
    width: 2px;
    background: var(--tg-theme-primary);
    pointer-events: none;
}
.tgmobile__menu .navigation li ul li > a {
    margin-left: 20px;
}
.tgmobile__menu .navigation li ul li ul li a {
    margin-left: 40px;
}
.tgmobile__menu .navigation li ul li ul li ul li a {
    margin-left: 60px;
}
.tgmobile__menu .navigation ul,
.tgmobile__menu .tgmenu__action {
    padding: 0;
    margin: 0;
}
.banner-bg,
.tgmobile__menu .social-links ul,
.tgmobile__menu .social-links ul li a {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.tgmobile__menu .navigation ul li ul li > a {
    font-size: 16px;
    margin-left: 20px;
    text-transform: capitalize;
}
.tgmobile__menu .navigation:last-child {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.tgmobile__menu .navigation > li.active > a::before {
    height: 100%;
}
.tgmobile__menu .close-btn {
    position: absolute;
    right: 15px;
    top: 28px;
    line-height: 30px;
    width: 35px;
    text-align: center;
    font-size: 14px;
    cursor: pointer;
    padding: 8px;
    z-index: 10;
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
}
.tgmobile__menu .close-btn i[class^="flaticon-"]:before {
    font-weight: var(--tg-fw-bold) !important;
}
.tgmobile__menu-backdrop {
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    transition: 0.7s;
    -moz-transition: 0.7s;
    -webkit-transition: 0.7s;
    -ms-transition: 0.7s;
    -o-transition: 0.7s;
    opacity: 0;
    visibility: hidden;
    background: rgba(0, 0, 0, 0.5);
}
.tgmobile__menu .social-links ul {
    display: flex;
    position: relative;
    text-align: center;
    padding: 30px 20px 20px;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}
.tgmobile__menu .social-links ul li {
    position: relative;
    display: inline-block;
    margin: 0 6px 10px;
}
.tgmobile__menu .social-links ul li a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    position: relative;
    line-height: 32px;
    font-size: 16px;
    color: var(--tg-body-color);
    -webkit-transition: 0.5s;
    -o-transition: 0.5s;
    transition: 0.5s;
    border: 1px solid #efefef;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
}
.tgmobile__menu .social-links ul li a:hover {
    border-color: var(--tg-theme-primary);
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.tgmobile__menu .tgmenu__action > ul {
    margin: 0;
    padding: 30px 20px 0;
    justify-content: center;
    gap: 0 15px;
}
.tgmobile__menu .tgmenu__action > ul li {
    margin: 0;
}
.tgmobile__menu-box {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    max-height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    background: var(--tg-common-color-white);
    padding: 0;
    z-index: 5;
    box-shadow: -9px 0 14px 0 rgba(0, 0, 0, 0.06);
}
.tgmobile__menu-outer .mobile-nav-toggler {
    position: relative;
    float: right;
    font-size: 40px;
    line-height: 50px;
    cursor: pointer;
    display: none;
    color: var(--tg-common-color-white);
    margin-right: 30px;
    top: 15px;
}
.mobile-menu-visible .tgmobile__menu {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
}
.mobile-menu-visible .tgmobile__menu-backdrop {
    opacity: 1;
    visibility: visible;
}
.mobile-menu-visible .tgmobile__menu .close-btn {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
}
.tgmobile__menu .mega-menu > li > .mega-sub-menu {
    display: flex !important;
    flex-direction: column;
    min-width: auto;
    margin-left: 0 !important;
}
.tgmobile__menu .mega-menu > li > .mega-sub-menu a {
    margin-left: 20px;
    display: flex;
    gap: 5px;
    align-items: center;
    padding: 10px 25px;
}
.tgmobile__menu .navigation li > .mega-menu > li .mega-sub-menu li:first-child {
    border: none;
}
.banner-bg {
    min-height: 520px;
    display: flex;
    align-items: flex-end;
    padding: 60px 0 0;
    position: relative;
    overflow: hidden;
}
.banner-bg .line-shape {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    pointer-events: none;
}
.banner-bg-two {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: flex-end;
    position: relative;
    overflow: hidden;
    padding: 80px 0 60px;
    background-size: cover;
    background-position: bottom;
    z-index: 1;
}
.banner-bg-five,
.banner-bg-five-shape,
.banner-bg-four,
.banner-bg-three {
    background-size: cover;
    background-position: center;
}
.banner-bg-two .line-shape-two {
    position: absolute;
    left: 0;
    top: 20%;
    z-index: -1;
    pointer-events: none;
}
.banner-bg-three {
    padding: 60px 0 0;
    position: relative;
    z-index: 1;
    overflow: hidden;
}
.banner-bg-four {
    position: relative;
    z-index: 1;
    overflow: hidden;
    padding: 140px 0 0;
}
.banner-bg-five {
    position: relative;
    z-index: 1;
    padding: 70px 0 0;
}
.banner-bg-five-shape {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 91px;
    z-index: 3;
}
.banner-bg-seven,
.banner-bg-six {
    background-size: cover;
    background-position: center;
    z-index: 1;
    position: relative;
}
.banner-bg-six {
    padding: 280px 0 150px;
}
@media (max-width: 1500px) {
    .tgmenu__search-form .select-grp {
        padding: 12px 0 12px 12px;
        width: 140px;
    }
    .tgmenu__search-form input {
        padding: 12px 50px 10px 10px;
    }
    .tgmenu__search-form-two {
        width: 220px;
    }
    .tgmenu__search-bar {
        max-width: 240px;
    }
    .tgmenu__action > ul li.free-btn,
    .tgmenu__action > ul li.header-contact {
        display: none;
    }
    .tgmenu__action > ul li.login-btn {
        margin-inline-start: 0;
    }
    .banner-bg {
        min-height: auto;
    }
    .banner-bg-two {
        padding: 60px 0;
    }
    .banner-bg-six {
        padding: 220px 0 130px;
    }
}
@media (max-width: 1199.98px) {
    .tgmenu__search-form input {
        padding: 12px 50px 10px 20px;
    }
    .tgmenu__search-bar {
        max-width: 100%;
    }
    .tgmenu__action {
        margin: 0 25px 0 0;
    }
    .tgmenu__action-four,
    .tgmenu__categories {
        margin-left: auto;
    }
    .tgmenu__action-seven {
        margin: 0 10px 0 auto;
    }
    .banner-bg-six {
        padding: 200px 0 110px;
    }
}
.banner-bg-seven {
    padding-top: 45px;
}
.banner__content {
    margin: 60px 0 100px;
    position: relative;
    z-index: 3;
}
@media (max-width: 1500px) {
    .banner__content {
        margin: 60px 0 70px;
    }
}
@media (max-width: 991.98px) {
    .tg-header__top-info,
    .tg-header__top-info li::after,
    .tg-header__top-info li:last-child {
        display: none;
    }
    .tg-header__top-right {
        justify-content: center;
    }
    .tg-header__style-two .tgmenu__search-form {
        width: auto;
    }
    .tgmenu__nav {
        justify-content: space-between;
    }
    .tgmenu__search-form {
        width: auto;
    }
    .tgmenu__search-bar,
    .tgmenu__search-form .input-grp,
    .tgmenu__search-form .select-grp::after,
    .tgmenu__search-form-two {
        display: none;
    }
    .banner-bg-two {
        padding: 60px 0 25px;
    }
    .banner-bg-five {
        padding: 90px 0 0;
    }
    .banner-bg-six {
        padding: 160px 0 110px;
    }
    .banner-bg-seven {
        padding-top: 60px;
    }
    .banner__content {
        text-align: center;
    }
}
.banner__content .sub-title {
    display: inline-block;
    line-height: 1;
    background: var(--tg-common-color-gray);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -o-border-radius: 4px;
    -ms-border-radius: 4px;
    border-radius: 4px;
    padding: 7px 15px;
    font-weight: var(--tg-fw-medium);
    color: var(--tg-theme-primary);
    margin: 0 0 16px;
}
.banner__content .title {
    font-size: 40px;
    line-height: 1.5;
    font-weight: var(--tg-fw-medium);
    margin: 0 0 12px;
}
@media (max-width: 1199.98px) {
    .banner__content {
        margin: 40px 0 60px;
    }
    .banner__content .title {
        font-size: 35px;
    }
}
@media (max-width: 767.98px) {
    .transparent-header {
        top: 0;
    }
    .tg-header__top-social {
        justify-content: center;
    }
    .banner-bg .line-shape,
    .banner-bg-two .line-shape-two,
    .select-grp-two,
    .tgmenu__action {
        display: none;
    }
    .mobile-login-btn {
        display: flex;
    }
    .banner-bg-four {
        padding: 130px 0 0;
    }
    .banner-bg-five {
        padding: 80px 0 0;
    }
    .banner-bg-six {
        padding: 130px 0 100px;
    }
    .banner__content {
        margin: 0 0 60px;
    }
    .banner__content .title {
        font-size: 30px;
    }
    .banner__content .title .svg-icon {
        display: none;
    }
}
.banner__content .title span:not(.svg-icon) {
    color: var(--tg-theme-primary);
    position: relative;
    color: var(--tg-common-color-white);
    padding: 0 13px 0 20px;
    font-weight: var(--tg-fw-bold);
    z-index: 1;
}
.banner__content .title span:not(.svg-icon) > svg {
    position: absolute;
    left: 0;
    top: 3px;
    width: 100%;
    height: 100%;
    z-index: -1;
    color: var(--tg-theme-secondary);
}
.banner__content .title .svg-icon {
    position: absolute;
    right: -50px;
    top: -50px;
    width: 61px;
    height: 68px;
}
.banner__content .title .svg-icon svg {
    display: block;
    width: 100%;
    height: 100%;
}
.banner__content p {
    max-width: 90%;
    margin: 0;
    font-size: 18px;
    line-height: 1.6;
}
.banner__content-two {
    margin: 120px 0 60px;
}
.banner__content-two .title {
    margin-bottom: 25px;
    font-size: 44px;
    font-weight: 700;
}
@media (max-width: 1199.98px) {
    .banner__content p {
        max-width: 100%;
    }
    .banner__content-two .title {
        font-size: 38px;
    }
}
@media (max-width: 767.98px) {
    .banner__content-two .title {
        font-size: 30px;
    }
}
.banner__content-two .title span {
    position: relative;
    color: var(--tg-common-color-white);
    padding: 0 13px 0 20px;
    font-weight: var(--tg-fw-bold);
    z-index: 1;
}
.banner__content-two .title span svg {
    position: absolute;
    left: 0;
    top: 3px;
    width: 100%;
    height: 100%;
    z-index: -1;
    color: var(--tg-theme-secondary);
}
.banner__content-three,
.related-product-area {
    position: relative;
    margin-top: 120px;
}
.banner__content-three .sub-title {
    font-size: 15px;
    text-transform: uppercase;
    display: block;
    line-height: 1;
    letter-spacing: 0.1em;
    font-weight: 600;
    margin-bottom: 10px;
}
.banner__content-three .title {
    margin-bottom: 8px;
    font-size: 60px;
    font-weight: 600;
    letter-spacing: -0.8px;
    text-transform: capitalize;
}
@media (max-width: 1500px) {
    .banner__content-two {
        margin: 80px 0 60px;
    }
    .banner__content-three .title {
        font-size: 54px;
    }
}
@media (max-width: 1199.98px) {
    .banner__content-three {
        margin-top: 50px;
    }
    .banner__content-three .title {
        font-size: 42px;
    }
}
@media (max-width: 991.98px) {
    .banner__content-two {
        margin: 0 0 60px;
        text-align: center;
    }
    .banner__content-three {
        text-align: center;
        margin-bottom: 60px;
    }
    .banner__content-three .title {
        font-size: 44px;
    }
}
@media (max-width: 767.98px) {
    .banner__content-two .title span {
        padding: 0 10px;
    }
    .banner__content-three {
        margin-top: 30px;
    }
    .banner__content-three .title {
        font-size: 38px;
    }
}
.banner__content-three .title span {
    position: relative;
    z-index: 1;
    display: inline-block;
}
.banner__content-three .title span::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 14px;
    width: 100%;
    height: 16px;
    background: var(--tg-theme-secondary);
    z-index: -1;
}
.banner__content-three p {
    margin-bottom: 0;
    font-size: 18px;
    font-weight: 500;
    width: 90%;
}
.banner__content-three .banner__btn-wrap {
    margin: 25px 0 0;
}
.banner__content-three .shape img {
    position: absolute;
    right: 45%;
    top: -24px;
    z-index: -1;
}
@media (max-width: 1199.98px) {
    .banner__content-three .title span::before {
        bottom: 8px;
    }
    .banner__content-three .shape img {
        right: 27%;
        top: -48px;
        width: 55px;
    }
}
.banner__content-four {
    margin-top: 150px;
}
.banner__content-four .sub-title {
    margin-bottom: 0;
    font-size: 24px;
    font-weight: 500;
    color: var(--tg-common-color-black-2);
    line-height: 1;
}
.banner__content-four .title {
    margin-bottom: 8px;
    font-size: 60px;
    font-weight: 700;
    text-transform: capitalize;
    color: var(--tg-common-color-black-2);
    position: relative;
    display: inline-block;
}
@media (max-width: 1199.98px) {
    .banner__content-four .title {
        font-size: 52px;
    }
}
@media (max-width: 767.98px) {
    .banner__content-four .title {
        font-size: 42px;
    }
    .banner__content-four .title .title__shape svg {
        width: 160px;
        height: 22px;
    }
}
.banner__content-four .title .title__shape {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    z-index: -1;
    line-height: 0;
    display: block;
}
.banner__content-four .title .title__shape svg {
    color: var(--tg-theme-secondary);
}
.banner__content-four .sub-title-two {
    display: block;
    font-size: 18px;
    font-weight: 500;
    text-transform: capitalize;
    color: var(--tg-common-color-black-2);
    line-height: 1;
    margin-bottom: 30px;
}
.banner__content-four p {
    margin-bottom: 35px;
    font-size: 18px;
    text-transform: capitalize;
    width: 90%;
}
.banner__content-five {
    margin-bottom: 40px;
}
@media (max-width: 1199.98px) {
    .banner__content-five {
        margin-bottom: 70px;
    }
}
.banner__content-five .sub-title {
    display: block;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: var(--tg-body-color);
    line-height: 1.2;
    margin-bottom: 5px;
}
.banner__content-five .title {
    margin-bottom: 15px;
    font-size: 60px;
    font-weight: 700;
    text-transform: capitalize;
    letter-spacing: -1.5px;
}
@media (max-width: 1500px) {
    .banner__content-four {
        margin-top: 70px;
    }
    .banner__content-four p {
        width: 100%;
    }
    .banner__content-five .title {
        font-size: 54px;
    }
}
@media (max-width: 1199.98px) {
    .banner__content-five .title {
        font-size: 42px;
        margin-bottom: 10px;
    }
}
@media (max-width: 991.98px) {
    .banner__content-three p {
        width: 100%;
    }
    .banner__content-three .shape img {
        right: 15%;
    }
    .banner__content-four {
        margin-top: 0;
        text-align: center;
        margin-bottom: 50px;
    }
    .banner__content-five {
        margin-bottom: 50px;
        text-align: center;
    }
    .banner__content-five .title {
        font-size: 45px;
    }
}
@media (max-width: 767.98px) {
    .banner__content-four .sub-title-two {
        margin-bottom: 20px;
    }
    .banner__content-five .title {
        font-size: 38px;
    }
}
.banner__content-five .title span {
    position: relative;
    display: inline-block;
}
.banner__content-five .title span::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 15px;
    width: 100%;
    height: 16px;
    background: var(--tg-theme-secondary);
    z-index: -1;
}
.banner__content-five p {
    margin-bottom: 25px;
    font-size: 17px;
}
.banner__content-five .btn {
    box-shadow: none;
}
.banner__content-six .title {
    margin-bottom: 15px;
    font-size: 50px;
    font-weight: 700;
    width: 95%;
    text-transform: capitalize;
}
@media (max-width: 1199.98px) {
    .banner__content-five .title span::before {
        height: 10px;
        bottom: 10px;
    }
    .banner__content-five p {
        margin-bottom: 15px;
        font-size: 16px;
    }
    .banner__content-six .title {
        font-size: 45px;
    }
}
@media (max-width: 991.98px) {
    .banner__content-six .title {
        font-size: 42px;
    }
}
@media (max-width: 767.98px) {
    .banner__content-six .title {
        font-size: 36px;
    }
}
.banner__content-six .title span {
    padding: 0 13px 0 15px;
    display: inline-block;
}
.banner__content-six .title span svg {
    position: absolute;
    left: 0;
    top: 2px;
    width: 100%;
    height: 100%;
    z-index: -1;
    color: var(--tg-theme-secondary);
}
.banner__content-six .sub-title {
    display: block;
    font-size: 18px;
    font-family: var(--tg-heading-font-family);
    font-weight: 500;
    text-transform: capitalize;
    color: var(--tg-heading-color);
    line-height: 1.2;
    margin-bottom: 30px;
}
.banner__content-six .about__info-list-item {
    margin-bottom: 12px;
}
.banner__content-six .about__info-list-item:last-child {
    margin-bottom: 0;
}
.banner__content-six .about__info-list-item i {
    width: 24px;
    height: 24px;
    flex: 0 0 auto;
}
.banner__content-six .about__info-list-item p {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.6;
}
.banner__content-six .banner__info-list {
    margin-bottom: 35px;
}
.banner__content-seven {
    margin-right: 35px;
}
@media (max-width: 1199.98px) {
    .banner__content-seven {
        margin-right: 20px;
    }
}
.banner__content-seven .title {
    font-size: 50px;
    font-weight: 700;
    text-transform: capitalize;
    margin-bottom: 15px;
    line-height: 1.1;
}
@media (max-width: 1199.98px) {
    .banner__content-seven .title {
        font-size: 42px;
    }
}
@media (max-width: 767.98px) {
    .banner__content-seven .title {
        font-size: 38px;
        line-height: 1.2;
    }
}
.banner__content-seven .title span {
    position: relative;
    display: inline-block;
}
.banner__content-seven .title span svg {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 21px;
    color: var(--tg-theme-secondary);
    z-index: -1;
}
.banner__content-seven p {
    margin-bottom: 30px;
    font-weight: 500;
    font-size: 18px;
    line-height: 1.55;
    width: 70%;
}
.banner__search {
    margin-bottom: 25px;
}
.banner__search .slider__search-form input {
    border: 1px solid var(--tg-border-6);
}
.banner__search .slider__search-form button {
    right: 6px;
}
.banner__btn-wrap {
    margin: 34px 0 0;
}
.banner__btn-wrap-three,
.banner__btn-wrap-two {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.banner__btn-wrap-two {
    display: flex;
    flex-wrap: wrap;
    gap: 20px 30px;
}
@media (max-width: 1199.98px) {
    .banner__content-seven p {
        width: 100%;
    }
    .banner__btn-wrap-two {
        gap: 20px 15px;
    }
}
.banner__btn-wrap-three {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}
.banner__btn-wrap-three .play-btn,
.banner__btn-wrap-three .play-btn i {
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.banner__btn-wrap-three .play-btn {
    display: flex;
    gap: 18px;
}
.banner__btn-wrap-three .play-btn i {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    flex: 0 0 auto;
    border: 1px solid #b6b6b6;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    font-size: 15px;
    color: var(--tg-heading-color);
    background: linear-gradient(180deg, #fff 0, #d9d9d9 100%);
}
.banner__btn-wrap-three .play-btn span {
    color: var(--tg-heading-color);
    font-size: 18px;
    font-weight: 500;
    text-decoration: underline;
    font-family: var(--tg-heading-font-family);
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    transition: 0.3s ease-out;
}
.about__images-three .popup-video svg,
.banner__btn-two .play-btn i {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.banner__btn-wrap-three .play-btn:hover span {
    color: var(--tg-theme-primary);
}
.banner__btn-two .play-btn:hover,
.banner__contact .content a:hover {
    color: var(--tg-theme-secondary);
}
.banner__btn-two {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 25px;
    flex-wrap: wrap;
}
.banner__btn-two .play-btn {
    font-size: 15px;
    font-weight: 500;
    font-family: var(--tg-heading-font-family);
    line-height: 1.3;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 15px;
    color: var(--tg-heading-color);
}
.banner__btn-two .play-btn i,
.banner__contact .icon {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    background: var(--tg-theme-secondary);
}
.banner__btn-two .play-btn i {
    width: 54px;
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    border: 1px solid var(--tg-heading-color);
    color: var(--tg-heading-color);
    font-size: 16px;
    -webkit-box-shadow: 4px 4px 0 0 #3d3d3d;
    -moz-box-shadow: 4px 4px 0 0 #3d3d3d;
    -ms-box-shadow: 4px 4px 0 0 #3d3d3d;
    -o-box-shadow: 4px 4px 0 0 #3d3d3d;
    box-shadow: 4px 4px 0 0 #3d3d3d;
    transition: 0.3s ease-out;
}
.banner__contact .content a,
.banner__contact .content span {
    text-transform: uppercase;
    color: var(--tg-common-color-black-3);
}
.banner__btn-two .play-btn:hover i {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}
.banner__contact .icon {
    width: 57px;
    height: 57px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--tg-common-color-black-2);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    line-height: 0;
}
.banner__contact .icon svg {
    width: 25px;
    height: 25px;
}
.banner__contact .content span {
    display: block;
    line-height: 1;
    font-size: 14px;
    letter-spacing: 0.1em;
    font-family: var(--tg-heading-font-family);
    margin-bottom: 5px;
}
.banner__contact .content a {
    font-size: 19px;
    font-weight: 600;
    line-height: 1;
}
.banner__images {
    position: relative;
    padding-right: 118px;
    text-align: right;
    z-index: 1;
}
@media (max-width: 1199.98px) {
    .banner__images {
        padding-right: 105px;
    }
}
@media (max-width: 991.98px) {
    .banner__content-seven {
        margin-right: 0;
        text-align: center;
        margin-bottom: 50px;
    }
    .banner__content-seven p {
        margin-bottom: 20px;
    }
    .banner__btn-wrap {
        margin: 28px 0 0;
    }
    .banner__btn-two,
    .banner__btn-wrap .tg-button-wrap {
        justify-content: center;
    }
    .banner__btn-wrap-two {
        gap: 20px 30px;
        justify-content: center;
    }
    .banner__images {
        margin: 0 auto;
        text-align: center;
        max-width: 80%;
        padding: 0;
    }
}
@media (max-width: 767.98px) {
    .banner__images {
        max-width: 100%;
    }
}
.banner__images .main-img {
    max-width: inherit;
}
.banner__images .shape {
    position: absolute;
    z-index: -1;
}
.banner__images .shape.big-shape {
    max-width: 618px;
    bottom: -85px;
    right: -22px;
}
.banner__images .shape.bg-dots {
    max-width: 495px;
    right: 30px;
    bottom: -33px;
    z-index: -2;
}
.banner__images .shape.small-shape {
    max-width: 136px;
    left: 35px;
    bottom: -24px;
    z-index: -2;
}
.banner__images-two {
    position: relative;
    text-align: center;
    margin-right: 60px;
}
.banner__images-two .big-shape {
    position: absolute;
    z-index: -1;
    left: 8%;
    top: 2%;
    color: #ffc738;
}
.banner__images-two .svg-icon {
    width: 42px;
    height: 42px;
    position: absolute;
    left: 35%;
    top: -5%;
}
.banner__images-two .svg-icon svg {
    width: 100%;
    height: 100%;
    color: #031333;
    display: block;
}
.banner__images-two .about__enrolled {
    left: 0;
    top: 41%;
    bottom: auto;
}
.banner__images-three {
    position: relative;
    z-index: 1;
    text-align: right;
}
.banner__images-three .big-shape {
    position: absolute;
    right: -15px;
    bottom: -55px;
    z-index: -1;
}
.banner__images-three .shape__wrap img {
    position: absolute;
    z-index: -1;
}
.banner__images-three .shape__wrap img:first-child {
    left: 0;
    top: 18%;
    max-width: 58px;
}
.banner__images-three .shape__wrap img:nth-child(2) {
    right: 0;
    top: 24%;
    max-width: 74px;
}
.banner__images-three .about__enrolled {
    left: -90px;
    bottom: auto;
    top: 35%;
}
.banner__images-four {
    position: relative;
    text-align: right;
}
.banner__images-four .shape {
    position: absolute;
    z-index: -1;
}
.banner__images-four .shape.big-shape {
    left: 43%;
    bottom: 6%;
}
@media (max-width: 1500px) {
    .banner__content-six .title {
        width: 100%;
    }
    .banner__images .main-img {
        max-width: 100%;
    }
    .banner__images-two img {
        width: 400px;
    }
    .banner__images-two .big-shape {
        top: 5%;
    }
    .banner__images-four {
        text-align: center;
    }
    .banner__images-four > img {
        max-width: 460px;
    }
    .banner__images-four .shape.big-shape {
        left: 31%;
        bottom: 3%;
    }
    .banner__images-four .shape.big-shape img {
        max-width: 310px;
    }
}
@media (max-width: 1199.98px) {
    .banner__images .shape.small-shape {
        left: 10px;
    }
    .banner__images-two {
        margin-right: 0;
    }
    .banner__images-two img {
        width: 350px;
    }
    .banner__images-two .big-shape {
        left: 0;
        width: 400px;
        top: 2%;
    }
    .banner__images-two .big-shape svg {
        width: 100%;
        height: 100%;
    }
    .banner__images-two .about__enrolled {
        display: none;
    }
    .banner__images-three .shape__wrap img:first-child {
        max-width: 50px;
    }
    .banner__images-three .shape__wrap img:nth-child(2) {
        max-width: 60px;
    }
    .banner__images-three .about__enrolled {
        left: -73px;
        top: 52%;
    }
    .banner__images-four > img {
        max-width: 420px;
    }
    .banner__images-four .shape.big-shape {
        left: 21%;
    }
}
@media (max-width: 991.98px) {
    .banner__images-two .svg-icon {
        left: 30%;
        top: -8%;
    }
    .banner__images-three {
        text-align: center;
        width: 80%;
        margin: 0 auto;
    }
    .banner__images-three .about__enrolled {
        display: block;
    }
    .banner__images-four .shape.big-shape {
        left: 24%;
    }
}
@media (max-width: 767.98px) {
    .banner__images .shape.bg-dots {
        max-width: 100%;
        right: 0;
        bottom: 18%;
    }
    .banner__images-two .big-shape {
        width: 100%;
        max-width: 400px;
    }
    .banner__images-three {
        width: 100%;
    }
    .banner__images-three .about__enrolled,
    .banner__images-three .shape__wrap img:nth-child(2) {
        display: none;
    }
    .banner__images-four > img {
        max-width: 100%;
    }
    .banner__images-four .shape.big-shape {
        left: 19%;
    }
}
@media (max-width: 1199.98px) {
    .banner__images-four .shape.big-shape img {
        max-width: 290px;
    }
}
@media (max-width: 767.98px) {
    .banner__images-four .shape.big-shape img {
        max-width: 250px;
    }
}
.banner__images-four .shape.big-shape-two {
    right: -13%;
    bottom: 18%;
    z-index: -2;
}
@media (max-width: 1500px) {
    .banner__images-four .shape.big-shape-two {
        right: 7%;
        bottom: 16%;
    }
    .banner__images-four .shape.big-shape-two img {
        max-width: 600px;
    }
}
@media (max-width: 1199.98px) {
    .banner__images-four .shape.big-shape-two {
        right: -9%;
        bottom: 20%;
    }
}
@media (max-width: 1199.98px) {
    .banner__images-four .shape.big-shape-two img {
        max-width: 520px;
    }
}
@media (max-width: 991.98px) {
    .banner__images-four .shape.big-shape-two {
        right: -8%;
        bottom: 14%;
    }
    .banner__images-four .shape.big-shape-two img {
        max-width: 570px;
    }
    .banner__images-six {
        margin-bottom: 60px;
    }
}
@media (max-width: 767.98px) {
    .banner__images-four .shape.big-shape-two {
        right: auto;
        bottom: 19%;
        left: 50%;
        transform: translateX(-50%);
    }
    .banner__images-four .shape.big-shape-two img {
        max-width: 400px;
    }
}
.banner__images-five {
    position: relative;
    text-align: right;
    margin-right: -50px;
}
.banner__images-five .shape-one {
    position: absolute;
    z-index: -1;
    left: -3%;
    bottom: 19%;
}
.banner__images-five .shape-two {
    position: absolute;
    z-index: -1;
    right: -50px;
    top: 60px;
}
@media (max-width: 1500px) {
    .banner__images-five {
        margin-right: 0;
    }
    .banner__images-five > img {
        max-width: 700px;
    }
    .banner__images-five .shape-one {
        bottom: 15%;
    }
    .banner__images-five .shape-two {
        right: 0;
        top: 52px;
        max-width: 80px;
    }
}
.banner__images-five .shape-three {
    position: absolute;
    z-index: -1;
    right: 11%;
    bottom: 18%;
}
.banner__images-seven,
.banner__images-six {
    text-align: center;
    position: relative;
}
.banner__images-six .main-img {
    position: relative;
    display: inline-block;
}
@media (max-width: 1199.98px) {
    .banner__images-five > img {
        max-width: 100%;
    }
    .banner__images-five .shape-one {
        bottom: 18%;
        max-width: 85px;
    }
    .banner__images-five .shape-two {
        right: 8px;
        top: 34px;
        max-width: 60px;
    }
    .banner__images-five .shape-three {
        right: 11%;
        bottom: 22%;
    }
    .banner__images-six .main-img {
        max-width: 400px;
    }
}
.banner__images-six .main-img img {
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    -o-border-radius: 100px;
    -ms-border-radius: 100px;
    border-radius: 100px;
}
.banner__images-six .main-img .svg-icon {
    width: 81px;
    height: 70px;
    position: absolute;
    left: -9%;
    top: -8%;
}
@media (max-width: 767.98px) {
    .banner__images-six {
        margin-bottom: 50px;
    }
    .banner__images-six .main-img {
        max-width: 100%;
    }
    .banner__images-six .main-img img {
        -webkit-border-radius: 40px;
        -moz-border-radius: 40px;
        -o-border-radius: 40px;
        -ms-border-radius: 40px;
        border-radius: 40px;
        width: 100%;
    }
    .banner__images-six .main-img .svg-icon {
        width: 60px;
        height: auto;
        left: -3%;
        top: -10%;
    }
    .banner__images-six .shape-wrap img:first-child {
        display: none;
    }
}
.banner__images-six .main-img .svg-icon svg {
    width: 100%;
    height: 100%;
    color: var(--tg-theme-secondary);
}
.banner__images-six .about__enrolled-two {
    box-shadow: -8px 8px 0 0 rgba(0, 0, 0, 0.25);
    background: #fff;
    border: 1px solid #6d6c80;
    border-radius: 15px 15px 0;
    padding: 16px 25px;
    left: 8%;
    bottom: auto;
    top: 19%;
}
.banner__images-seven .big_shape,
.banner__images-seven .shape {
    left: 50%;
    transform: translate(-50%, -50%);
    top: 50%;
    position: absolute;
}
.banner__images-six .about__enrolled-two img {
    margin-bottom: 10px;
}
.banner__images-six .about__enrolled-two p {
    margin-bottom: 0;
    font-weight: 600;
    color: var(--tg-heading-color);
    font-family: var(--tg-heading-font-family);
    line-height: 1;
}
.banner__images-six .shape-wrap img,
.banner__shape-wrap-two img {
    position: absolute;
    z-index: -1;
}
.banner__images-six .shape-wrap img:first-child {
    left: 0;
    top: 11%;
}
.banner__images-six .shape-wrap img:nth-child(2) {
    right: 17%;
    bottom: -5%;
}
@media (max-width: 1500px) {
    .banner__images-six .shape-wrap img:nth-child(2) {
        right: 13%;
    }
}
.banner__images-seven .big_shape {
    color: var(--tg-theme-secondary);
    z-index: -1;
    width: 506px;
    height: 506px;
}
.banner__all-recipe .count,
.banner__all-recipe span,
.banner__courses .icon,
.banner__courses .title,
.banner__student .icon {
    color: var(--tg-common-color-white);
}
.banner__images-seven .big_shape svg {
    width: 100%;
    height: 100%;
}
.banner__images-seven .shape {
    z-index: -2;
    width: 592px;
}
.banner__images-seven .about__enrolled {
    left: -24px;
    bottom: auto;
    top: 41%;
}
.banner__all-recipe {
    position: absolute;
    right: 1%;
    bottom: 33%;
    background: var(--tg-theme-primary);
    box-shadow: -8px 8px 0 0 rgba(0, 0, 0, 0.15);
    border: 1px solid #b2bbcc;
    border-radius: 10px;
    text-align: center;
    padding: 15px 28px;
}
.banner__author,
.banner__student,
.slider__search-form input {
    background: var(--tg-common-color-white);
}
.banner__all-recipe .count {
    font-weight: 700;
    font-size: 36px;
    line-height: 1;
    margin-bottom: 5px;
}
.banner__all-recipe span {
    display: block;
    line-height: 1;
    font-weight: 500;
    font-size: 14px;
    text-transform: capitalize;
}
.banner__student {
    display: inline-flex;
    align-items: center;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    border: 1px solid var(--tg-common-color-gray-5);
    -webkit-box-shadow: 8px 8px 0 0 rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 8px 8px 0 0 rgba(0, 0, 0, 0.15);
    -ms-box-shadow: 8px 8px 0 0 rgba(0, 0, 0, 0.15);
    -o-box-shadow: 8px 8px 0 0 rgba(0, 0, 0, 0.15);
    box-shadow: 8px 8px 0 0 rgba(0, 0, 0, 0.15);
    position: absolute;
    right: 18%;
    top: 16%;
    padding: 15px 18px;
    gap: 10px;
}
.banner__author,
.banner__student .icon {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.banner__student .icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-common-color-green);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    -webkit-box-shadow: 4px 4px 0 rgba(33, 150, 83, 0.5);
    -moz-box-shadow: 4px 4px 0 rgba(33, 150, 83, 0.5);
    -ms-box-shadow: 4px 4px 0 rgba(33, 150, 83, 0.5);
    -o-box-shadow: 4px 4px 0 rgba(33, 150, 83, 0.5);
    box-shadow: 4px 4px 0 rgba(33, 150, 83, 0.5);
    border: 1px solid #149959;
}
.about__content-bottom .btn,
.about__info-list-item:hover i {
    box-shadow: none;
}
.banner__student .content {
    text-align: left;
}
.banner__student .content span {
    display: block;
    line-height: 1;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}
.banner__student .content .title {
    margin-bottom: 0;
    font-size: 24px;
    font-weight: 800;
    line-height: 1;
}
.banner__author {
    position: absolute;
    top: 30px;
    left: 23px;
    width: 175px;
    padding: 11px 30px 11px 14px;
    border-radius: 6px;
    box-shadow: 9px 10px 0 0 rgba(0, 0, 0, 0.2);
    text-align: left;
    display: flex;
    flex-direction: column;
    gap: 6px;
}
@media (max-width: 1199.98px) {
    .banner__images-six .about__enrolled-two {
        left: 0;
    }
    .banner__images-six .shape-wrap img:nth-child(2) {
        right: 7%;
    }
    .banner__images-seven .main-img {
        max-width: 330px;
    }
    .banner__images-seven .big_shape {
        width: 420px;
        height: 420px;
    }
    .banner__images-seven .shape {
        width: 490px;
    }
    .banner__student {
        right: 0;
        top: 13%;
    }
    .banner__author {
        top: -30px;
        left: -10px;
        width: 155px;
        padding: 10px;
    }
}
.banner__author-item {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 8px;
}
.banner__author-item .image {
    flex: 0 0 auto;
}
.banner__author-item .image img {
    max-width: 30px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.banner__author-item .name {
    flex-grow: 1;
    margin-bottom: 0;
    font-size: 14px;
}
.banner__review,
.banner__review .icon {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.banner__author .arrow-shape {
    position: absolute;
    left: calc(100% - 11px);
    top: 9px;
    z-index: -1;
}
.banner__review {
    display: flex;
    box-shadow: -8px 8px 0 0 rgba(0, 0, 0, 0.25);
    background: #fff;
    gap: 8px;
    border: 1px solid var(--tg-body-color);
    border-radius: 15px 15px 15px 0;
    position: absolute;
    right: 3%;
    top: 25%;
    padding: 15px;
}
.banner__review .icon {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    background: var(--tg-theme-secondary);
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -o-border-radius: 8px;
    -ms-border-radius: 8px;
    border-radius: 8px;
}
.banner__courses,
.banner__courses .icon {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.banner__review .title {
    margin-bottom: 0;
    text-align: left;
    font-weight: 700;
    font-size: 22px;
    text-transform: capitalize;
    line-height: 1;
}
.banner__review .title span {
    display: block;
    font-weight: 500;
    font-size: 16px;
    color: var(--tg-body-color);
    margin-top: 2px;
}
.banner__courses {
    background: #27ae60;
    display: flex;
    border-radius: 20px 20px 20px 0;
    gap: 8px;
    padding: 16px 20px;
    position: absolute;
    right: 11%;
    bottom: 15%;
}
.about-area-five,
.about__bg,
.about__images,
.about__images-five,
.about__images-four,
.about__images-six,
.about__images-three,
.brand-area-two,
.courses-area-seven,
.marquee_mode,
.slider__bg,
.slider__search-form {
    position: relative;
}
.banner__courses .icon {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    font-size: 24px;
}
.banner__courses .title {
    margin-bottom: 0;
    font-weight: 700;
    font-size: 26px;
    line-height: 1;
    text-align: left;
}
.banner__courses .title span {
    display: block;
    font-weight: 500;
    font-size: 18px;
}
.banner__shape-wrap img {
    position: absolute;
    z-index: -2;
}
.banner__shape-wrap img:first-child {
    left: 0;
    top: 0;
    max-width: 276px;
}
.banner__shape-wrap img:nth-child(2) {
    right: 0;
    bottom: 0;
    max-width: 330px;
}
.banner__shape-wrap img:nth-child(3) {
    left: 70px;
    bottom: -15px;
    max-width: 157px;
}
.banner__shape-wrap-two img:first-child {
    left: 0;
    bottom: 39%;
    max-width: 185px;
}
.banner__shape-wrap-two img:nth-child(2) {
    left: 14%;
    top: 50px;
    max-width: 84px;
}
.banner__shape-wrap-two img:nth-child(3) {
    left: 35%;
    top: 43px;
    max-width: 183px;
}
@media (max-width: 1199.98px) {
    .banner__review {
        right: 0;
    }
    .banner__courses {
        right: 4%;
    }
    .banner__shape-wrap img:nth-child(2) {
        max-width: 270px;
    }
    .banner__shape-wrap img:nth-child(3) {
        max-width: 120px;
    }
    .banner__shape-wrap-two img:first-child {
        display: none;
    }
    .banner__shape-wrap-two img:nth-child(2) {
        left: 10%;
        top: 35px;
        max-width: 60px;
    }
    .banner__shape-wrap-two img:nth-child(3) {
        left: 38%;
        top: 25px;
        max-width: 120px;
    }
}
.slider__bg {
    background-size: cover;
    background-position: center;
    z-index: 1;
    min-height: 795px;
    display: flex;
    align-items: center;
    padding: 100px 0 140px;
}
.slider__bg::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(
        90deg,
        rgba(0, 0, 0, 0.85) 0,
        rgba(0, 0, 2, 0) 100%
    );
}
.slider__content {
    margin-right: 90px;
    margin-top: -40px;
}
@media (max-width: 1500px) {
    .banner__shape-wrap-two img:first-child {
        bottom: auto;
        max-width: 160px;
        top: 21%;
    }
    .slider__bg {
        min-height: 700px;
    }
    .slider__content {
        margin-right: 50px;
    }
}
@media (max-width: 991.98px) {
    .banner__images-seven .about__enrolled {
        display: block;
    }
    .banner__author {
        top: 0;
        left: 10px;
    }
    .banner__shape-wrap-two img:nth-child(3) {
        left: auto;
        top: 20px;
        max-width: 100px;
        right: 10%;
    }
    .slider__content {
        text-align: center;
    }
}
.slider__content .sub-title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--tg-theme-secondary);
    letter-spacing: 0.1em;
    margin-bottom: 15px;
}
.brand__item,
.brand__item a,
.marquee_mode .js-marquee {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.slider__content .title {
    font-weight: 700;
    font-size: 50px;
    color: var(--tg-common-color-white);
    margin-bottom: 10px;
}
@media (max-width: 1199.98px) {
    .slider__content {
        margin-right: 0;
    }
    .slider__content .title {
        font-size: 45px;
    }
}
@media (max-width: 767.98px) {
    .banner__author,
    .banner__courses,
    .banner__images-seven .about__enrolled,
    .banner__images-six .shape-wrap img:nth-child(2),
    .banner__review,
    .banner__shape-wrap img:nth-child(2),
    .banner__shape-wrap img:nth-child(3) {
        display: none;
    }
    .banner__images-seven .big_shape {
        width: 90%;
        height: 90%;
    }
    .banner__images-seven .shape {
        width: 100%;
    }
    .banner__shape-wrap img:first-child {
        max-width: 200px;
    }
    .banner__shape-wrap-two img:nth-child(2) {
        left: 8%;
        top: 15px;
        max-width: 50px;
    }
    .slider__content .title {
        font-size: 38px;
    }
}
.slider__content .title span {
    position: relative;
    display: inline-block;
}
.slider__content .title span svg {
    position: absolute;
    z-index: -1;
    width: 100%;
    left: 0;
    bottom: 0;
    color: var(--tg-theme-secondary);
}
.slider__content p {
    margin-bottom: 30px;
    font-size: 18px;
    font-weight: 500;
    color: var(--tg-common-color-gray-6);
    width: 75%;
}
.slider__search-form input {
    width: 100%;
    border: none;
    font-size: 14px;
    font-weight: 500;
    color: var(--tg-heading-color);
    padding: 18px 185px 18px 25px;
    border-radius: 100px;
    height: 60px;
}
.about__images .popup-video:hover,
.about__images-three .popup-video:hover svg,
.slider__search-form button {
    color: var(--tg-common-color-white);
}
.slider__search-form button {
    border: none;
    background: var(--tg-theme-primary);
    padding: 11px 32px;
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    font-weight: 600;
    border-radius: 100px;
}
.about__info-list-item p,
.about__video a {
    font-size: 18px;
    font-family: var(--tg-heading-font-family);
}
.slider__search-form button:hover {
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
}
.slider__single.swiper-slide-active .slider__content .sub-title {
    -webkit-animation-delay: 0.3s;
    animation-delay: 0.3s;
    -webkit-animation-duration: 0.8s;
    animation-duration: 0.8s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: fadeInUp2;
    animation-name: fadeInUp2;
}
.slider__single.swiper-slide-active .slider__content .title {
    -webkit-animation-delay: 0.6s;
    animation-delay: 0.6s;
    -webkit-animation-duration: 0.8s;
    animation-duration: 0.8s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: fadeInUp2;
    animation-name: fadeInUp2;
}
.slider__single.swiper-slide-active .slider__content p {
    -webkit-animation-delay: 0.9s;
    animation-delay: 0.9s;
    -webkit-animation-duration: 0.8s;
    animation-duration: 0.8s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: fadeInUp2;
    animation-name: fadeInUp2;
}
.slider__single.swiper-slide-active .slider__content .slider__search {
    -webkit-animation-delay: 1.2s;
    animation-delay: 1.2s;
    -webkit-animation-duration: 0.8s;
    animation-duration: 0.8s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: fadeInUp2;
    animation-name: fadeInUp2;
}
.brand-area {
    background: var(--tg-common-color-black);
    padding: 20px 0;
}
.brand-area-two {
    transform: rotate(-3.5deg);
    z-index: 2;
    margin-top: -60px;
}
.brand__item,
.marquee_mode .js-marquee {
    display: flex;
    gap: 0 35px;
}
.brand__item a {
    display: flex;
    height: 42px;
    justify-content: center;
}
.about__images .popup-video,
.brand__item-two {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.brand__item a:hover img {
    opacity: 0.7;
}
.brand__item-two {
    min-height: 46px;
    display: flex;
    justify-content: center;
}
.brand__item-two img {
    cursor: pointer;
}
.marquee_mode {
    overflow: hidden;
}
.about-area-five {
    z-index: 1;
}
.about__bg {
    background-size: cover;
    background-position: center;
    z-index: 1;
    padding: 140px 0 230px;
}
.about__images {
    margin-left: 196px;
    margin-right: -25px;
}
@media (max-width: 1199.98px) {
    .slider__content p {
        width: 100%;
    }
    .about__images {
        margin-left: 50px;
        margin-right: 0;
    }
}
.about__images .shape {
    position: absolute;
    top: 36px;
    right: 132px;
    z-index: 1;
}
@media (max-width: 1199.98px) {
    .about__images .shape {
        right: 95px;
    }
}
.about__images .popup-video {
    position: absolute;
    width: 76px;
    height: 76px;
    display: flex;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: var(--tg-common-color-white);
    color: var(--tg-common-color-red);
    z-index: 1;
}
.about__images-three .popup-video,
.about__success {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
    background: var(--tg-common-color-white);
}
.about__images-four .shape img,
.about__images-four::before,
.about__shape-wrap img,
.courses__shape-wrap img,
.courses__shape-wrap-four img,
.courses__shape-wrap-three img,
.courses__shape-wrap-two img {
    z-index: -1;
    position: absolute;
}
.about__images .popup-video:hover {
    background: var(--tg-common-color-red);
}
.about__images-three img {
    -webkit-border-radius: 400px;
    -moz-border-radius: 400px;
    -o-border-radius: 400px;
    -ms-border-radius: 400px;
    border-radius: 400px;
}
.about__images-three .svg-icon {
    width: 55px;
    height: 55px;
    position: absolute;
    right: 5%;
    top: 2%;
}
.about__images-three .svg-icon svg {
    width: 100%;
    height: 100%;
    color: var(--tg-theme-primary);
}
.about__images-three .popup-video {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.about__images-three .popup-video svg {
    width: 16px;
    color: var(--tg-theme-primary);
    transition: 0.3s ease-out;
}
.about__info-list-item i,
.about__video a i {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.about__images-three .popup-video:hover {
    background: var(--tg-theme-primary);
}
.about__images-four {
    padding-bottom: 60px;
    margin-left: 22px;
}
.about__images-four::before {
    content: "";
    right: 10%;
    bottom: 0;
    height: 135px;
    width: 80%;
    background: var(--tg-theme-secondary);
    -webkit-border-radius: 0 0 0 24px;
    -moz-border-radius: 0 0 0 24px;
    -o-border-radius: 0 0 0 24px;
    -ms-border-radius: 0 0 0 24px;
    border-radius: 0 0 0 24px;
}
.about__images-four > img {
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    -o-border-radius: 20px;
    -ms-border-radius: 20px;
    border-radius: 20px;
}
.about__images-four .shape img:first-child {
    left: -65px;
    bottom: -10px;
    z-index: -2;
}
.about__images-four .shape img:nth-child(2) {
    right: 4%;
    top: 25%;
}
.about__images-four .about__enrolled {
    left: -35px;
    bottom: 25px;
    z-index: 2;
}
.about__images-five {
    text-align: right;
    margin-right: 20px;
}
@media (max-width: 991.98px) {
    .about__images {
        margin-left: 0;
        margin-bottom: 50px;
    }
    .about__images .shape {
        right: 135px;
    }
    .about__images-five,
    .about__images-four,
    .about__images-six,
    .about__images-three {
        margin-bottom: 50px;
    }
}
.about__images-five .shape img {
    position: absolute;
}
.about__images-five .shape img:first-child {
    right: -1%;
    top: -3%;
    max-width: 72px;
}
@media (max-width: 767.98px) {
    .brand-area-two {
        transform: rotate(0);
    }
    .about__bg {
        padding: 100px 0 190px;
    }
    .about__images .shape {
        right: 80px;
    }
    .about__images-five .shape img:first-child {
        right: -3%;
        top: -4%;
        max-width: 65px;
    }
}
.about__images-five .shape img:nth-child(2) {
    left: 8%;
    bottom: -3%;
    max-width: 109px;
}
@media (max-width: 1199.98px) {
    .about__images-three .svg-icon {
        right: 0;
        top: 0;
    }
    .about__images-four {
        margin-left: 0;
    }
    .about__images-four .shape img:first-child {
        left: -35px;
    }
    .about__images-four .about__enrolled {
        display: none;
    }
    .about__images-five {
        margin-right: 0;
    }
    .about__images-five .shape img:nth-child(2) {
        left: 2%;
        bottom: -2%;
        max-width: 80px;
    }
}
.about__images-five .shape img:nth-child(3) {
    right: 1%;
    bottom: -5%;
    -webkit-animation-duration: 30s;
    animation-duration: 30s;
    max-width: 149px;
}
@media (max-width: 767.98px) {
    .about__images-five .shape img:nth-child(3) {
        bottom: -7%;
        max-width: 95px;
    }
}
.about__images-six {
    margin-left: 85px;
    padding-bottom: 115px;
}
@media (max-width: 1500px) {
    .about__images-four .about__enrolled {
        left: -45px;
    }
    .about__images-six {
        margin-left: 20px;
    }
}
.about__images-six > img:first-child {
    border: 8px solid var(--tg-common-color-white);
    border-radius: 20px 80px;
}
.about__images-six > img:nth-child(2) {
    border: 8px solid var(--tg-common-color-white);
    border-radius: 10px 40px 40px;
    position: absolute;
    right: 40px;
    bottom: 0;
}
.about__images-six .shape img {
    position: absolute;
    z-index: -1;
    left: 0;
    bottom: -5%;
}
.about__mask-img-one {
    -webkit-mask-image: url(../img/others/h5_about_mask_img01.png);
    mask-image: url(../img/others/h5_about_mask_img01.png);
    -webkit-mask-size: 100%;
    mask-size: 100%;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    height: 505px;
    width: 544px;
    margin-left: auto;
}
.about__mask-img-two,
.testimonial__mask-img {
    -webkit-mask-size: 100%;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
}
@media (max-width: 1199.98px) {
    .about__images-six {
        margin-left: 0;
    }
    .about__images-six > img:nth-child(2) {
        right: 0;
    }
    .about__mask-img-one {
        height: 412px;
        width: 440px;
    }
}
@media (max-width: 767.98px) {
    .about__images-six {
        padding-bottom: 0;
    }
    .about__images-six > img:first-child {
        width: 100%;
    }
    .about__images-six > img:nth-child(2),
    .about__mask-img-two {
        display: none;
    }
    .about__mask-img-one {
        width: 345px;
        height: 325px;
        margin: 0 auto;
    }
}
.about__mask-img-one img {
    max-width: unset;
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.about__mask-img-two {
    -webkit-mask-image: url(../img/others/h5_about_mask_img02.png);
    mask-image: url(../img/others/h5_about_mask_img02.png);
    mask-size: 100%;
    mask-repeat: no-repeat;
    mask-position: center;
    height: 251px;
    width: 271px;
    position: absolute;
    left: 80px;
    bottom: 0;
}
.about__mask-img-two img {
    max-width: unset;
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.about__success {
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.1);
    border-radius: 10px 10px 10px 30px;
    display: flex;
    padding: 12px 30px 12px 12px;
    gap: 13px;
    position: absolute;
    left: 35px;
    bottom: 3px;
}
.about__content-bottom,
.about__success .icon {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.about__success .icon {
    width: 68px;
    height: 68px;
    display: flex;
    justify-content: center;
    flex: 0 0 auto;
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
    border-radius: 8px 15px 20px 20px;
    font-size: 35px;
}
.about__contact .content a:hover,
.about__enrolled .title {
    color: var(--tg-theme-primary);
}
.about__success .content .title {
    font-weight: 700;
    font-size: 30px;
    margin-bottom: 0;
    line-height: 1;
}
.about__success .content span {
    display: block;
    font-weight: 500;
    font-size: 16px;
    font-family: var(--tg-heading-font-family);
    line-height: 1;
    text-transform: capitalize;
    color: var(--tg-heading-color);
}
.about__enrolled {
    position: absolute;
    left: -90px;
    bottom: 35px;
    background: var(--tg-common-color-white);
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    border: 1px solid #b2bbcc;
    box-shadow: -8px 8px 0 0 rgba(0, 0, 0, 0.15);
    text-align: center;
    padding: 12px 20px;
}
.about__enrolled .title {
    font-size: 16px;
    font-weight: var(--tg-fw-medium);
    margin: 0 0 2px;
}
.about__enrolled .title span {
    font-weight: var(--tg-fw-bold);
    color: var(--tg-heading-color);
}
.about__enrolled img {
    max-width: 155px;
}
.about__content {
    margin-left: 65px;
    margin-right: 110px;
}
.about__content .section__title {
    margin: 0 0 12px;
}
.about__content .desc {
    margin: 0 0 22px;
}
.about__content-five p,
.about__content-three .desc {
    margin-bottom: 22px;
}
.about__content .tg-button-wrap {
    margin-top: 40px;
}
.about__content-three {
    margin-left: 40px;
    width: 90%;
}
.about__content-three .about__info-list,
.courses-top-wrap,
.courses__details-thumb {
    margin-bottom: 30px;
}
.about__content-four {
    margin-left: 40px;
    position: relative;
}
.about__content-four .info-bold {
    margin-bottom: 25px;
    font-weight: 500;
    color: var(--tg-heading-color);
    width: 90%;
}
.about__content-five .about__info-list,
.about__content-six .about__info-list {
    margin-bottom: 40px;
}
.about__content-four .shape {
    position: absolute;
    z-index: -1;
    right: 8%;
    top: 6%;
}
.about__content-five {
    margin-left: 60px;
    margin-right: 105px;
    position: relative;
}
.about__content-five .shape img {
    position: absolute;
    right: -30%;
    top: 5%;
    z-index: -1;
}
.about__contact .content a,
.about__contact .content span {
    display: block;
    line-height: 1;
}
.about__content-six {
    width: 78%;
}
.about__content-six > p {
    margin-bottom: 20px;
}
.about__content-inner {
    margin-bottom: 35px;
}
.about__content-inner p {
    margin-bottom: 0;
}
.about__content-bottom {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}
.about__contact .icon,
.about__info-list-item i {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.about__contact .icon {
    position: relative;
    width: 60px;
    height: 55px;
    display: flex;
    justify-content: center;
    font-size: 24px;
    color: var(--tg-heading-color);
}
.about__contact .icon svg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    color: var(--tg-theme-secondary);
}
.about__contact .content a {
    font-weight: 600;
    font-size: 20px;
    font-family: var(--tg-heading-font-family);
    color: var(--tg-heading-color);
    margin-bottom: 8px;
}
.about__info-list-item {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: flex-start;
    gap: 13px;
    margin: 0 0 15px;
}
.about__info-list-item:last-child {
    margin: 0;
}
.about__info-list-item i {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    flex: 0 0 30px;
    background: var(--tg-theme-secondary);
    border: 1px solid #282568;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    box-shadow: 4px 3px 0 0 rgba(0, 0, 0, 0.25);
    transition: 0.3s ease-out;
    color: var(--tg-heading-color);
}
.about__video a,
.about__video a i {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.about__info-list-item p {
    color: var(--tg-heading-color);
    font-weight: var(--tg-fw-semi-bold);
    margin: 0;
}
.about__video a,
.about__video a:hover i,
.about__year-wrap .count,
.about__year-wrap .title {
    color: var(--tg-common-color-white);
}
.about__video {
    background: var(--tg-theme-secondary);
    padding: 40px 31px;
    border-radius: 24px 0 0 24px;
    position: absolute;
    right: 105px;
    bottom: 0;
}
.about__video a {
    display: flex;
    gap: 20px;
    font-weight: 700;
    text-transform: capitalize;
    line-height: 1.2;
}
.about__video a i {
    width: 55px;
    height: 55px;
    display: flex;
    justify-content: center;
    color: var(--tg-common-color-black-2);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    background: var(--tg-common-color-white);
    flex: 0 0 auto;
    transition: 0.3s ease-out;
}
.about__year-wrap,
.courses__nav .nav {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__nav .courses-button-prev,
.courses__nav .nav .nav-item .nav-link::after {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.about__video a:hover i {
    background: var(--tg-common-color-black-2);
}
.about__year-wrap {
    display: flex;
    background: var(--tg-body-color);
    position: absolute;
    right: -105px;
    bottom: 105px;
    transform: rotate(-90deg);
    line-height: 1;
    gap: 10px;
    padding: 22px 27px;
    border-radius: 0 0 15px 24px;
}
.courses-area,
.courses__bg,
.courses__bg-three,
.courses__bg-two {
    background-size: cover;
    background-position: center;
    position: relative;
    z-index: 1;
}
.about__year-wrap .count {
    font-size: 70px;
    font-weight: 800;
    margin-bottom: 0;
    line-height: 0.8;
}
.about__year-wrap .title {
    font-weight: 600;
    font-size: 24px;
    letter-spacing: 0.1em;
    text-transform: capitalize;
    margin-bottom: 0;
}
.about__shape img {
    position: absolute;
    z-index: -1;
    left: 0;
    top: 0;
    max-width: 370px;
}
@media (max-width: 1800px) {
    .select-grp-two {
        margin-left: 30px;
    }
    .about__shape img {
        max-width: 250px;
    }
}
.about__shape-wrap img:first-child {
    left: 8%;
    top: 21%;
    max-width: 96px;
}
@media (max-width: 1800px) {
    .about__shape-wrap img:first-child {
        left: 3%;
        top: 10%;
    }
}
.about__shape-wrap img:nth-child(2) {
    right: 5%;
    bottom: 13%;
    max-width: 162px;
}
@media (max-width: 1500px) {
    .about__content-four {
        margin-left: 15px;
    }
    .about__content-five {
        margin-left: 25px;
    }
    .about__content-five .shape img {
        right: -20%;
    }
    .about__video {
        right: 90px;
    }
    .about__shape img {
        max-width: 210px;
        top: -4%;
        left: -4%;
    }
    .about__shape-wrap img:first-child {
        left: 3%;
        top: 2%;
        max-width: 65px;
    }
    .about__shape-wrap img:nth-child(2) {
        max-width: 120px;
    }
}
.mfp-iframe-holder .mfp-content {
    max-width: 1200px;
}
@media (max-width: 1199.98px) {
    .about__mask-img-two {
        height: 205px;
        width: 220px;
        left: 30px;
    }
    .about__enrolled {
        left: -50px;
        bottom: 30px;
    }
    .about__content {
        margin: 0;
    }
    .about__content-three {
        margin-left: 0;
        width: 100%;
    }
    .about__content-four {
        margin-left: 0;
    }
    .about__content-four .info-bold,
    .about__content-six {
        width: 100%;
    }
    .about__content-four .shape {
        right: 2%;
        top: -3%;
    }
    .about__content-five {
        margin-left: 0;
        margin-right: 0;
    }
    .about__content-five .shape img {
        right: -2%;
        top: -13%;
        max-width: 90px;
    }
    .about__video {
        padding: 30px 20px;
        right: 100px;
    }
    .about__video a {
        gap: 10px;
        font-size: 16px;
    }
    .about__year-wrap {
        right: -95px;
        bottom: 94px;
    }
    .about__year-wrap .count {
        font-size: 60px;
    }
    .about__year-wrap .title {
        font-size: 22px;
    }
    .section__title .title br {
        display: none;
    }
}
.section-pt-120 {
    padding-top: 120px;
}
.section-pb-90 {
    padding-bottom: 90px;
}
.section-pb-120 {
    padding-bottom: 120px;
}
.courses-area .section__title-wrap {
    margin: 0 0 50px;
}
@media (max-width: 767.98px) {
    .about__content-five .shape img,
    .about__content-four .shape,
    .about__shape img,
    .about__success {
        display: none;
    }
    .about__content-inner p {
        margin-bottom: 25px;
    }
    .about__video {
        padding: 20px 12px;
    }
    .about__shape-wrap img:nth-child(2) {
        max-width: 80px;
        bottom: 9%;
    }
    .section-pt-120 {
        padding-top: 100px;
    }
    .section-pb-90 {
        padding-bottom: 70px;
    }
    .section-pb-120 {
        padding-bottom: 100px;
    }
    .courses-area {
        padding: 100px 0;
    }
    .courses-area .section__title-wrap {
        margin: 0 0 40px;
    }
}
.courses-area-six {
    padding: 140px 0 225px;
}
.courses__bg {
    padding: 130px 0;
}
.courses__bg-two {
    overflow: hidden;
}
.courses__bg-three {
    padding: 140px 0 110px;
    margin: 22px 0;
}
.courses__bg-shape-one,
.courses__bg-shape-two {
    background-size: cover;
    background-position: center;
    position: absolute;
    height: 22px;
    width: 100%;
    left: 0;
}
.courses__bg-four,
.testimonial__bg {
    background-size: cover;
    background-position: center;
}
.courses__bg-shape-one {
    top: -26px;
    z-index: -1;
}
.courses__bg-shape-one svg {
    color: var(--tg-common-color-gray-10);
    width: 100%;
    height: 22px;
}
.courses__bg-shape-two {
    bottom: 4px;
    z-index: -1;
}
.courses__bg-shape-two svg {
    width: 100%;
    height: 22px;
    color: var(--tg-common-color-white);
}
.courses__nav .nav {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin: 0;
    padding: 0 0 18px;
    border: none;
    gap: 5px 35px;
    position: relative;
    z-index: 1;
}
.courses__item-thumb-eight .courses__wishlist,
.courses__nav-two .nav {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__nav .nav::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 3px;
    background: #d9d9f3;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
    z-index: -1;
}
@media (max-width: 767.98px) {
    .courses-area-six {
        padding: 100px 0 185px;
    }
    .courses__bg {
        padding: 100px 0;
    }
    .courses__bg-three {
        padding: 100px 0 70px;
    }
    .courses__nav .nav {
        gap: 15px 20px;
        padding: 0 0 7px;
    }
}
.courses__nav .nav .nav-item .nav-link {
    font-weight: var(--tg-fw-medium);
    color: #7f7e97;
    padding: 0;
    border: none;
    position: relative;
    background: 0 0;
}
.choose__img-four .left__side img,
.courses__nav .nav .nav-item .nav-link::after {
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
}
.courses__nav .nav .nav-item .nav-link::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -18px;
    right: 0;
    width: 0;
    height: 6px;
    background: var(--tg-theme-primary);
    border-radius: 30px;
    margin: 0 auto;
    z-index: 1;
    transition: 0.3s ease-out;
}
@media (max-width: 767.98px) {
    .courses__nav .nav .nav-item .nav-link::after {
        bottom: -7px;
    }
    .courses__item-three {
        flex-direction: column;
    }
}
.courses__nav .nav .nav-item .nav-link.active {
    color: var(--tg-heading-color);
}
.courses__nav .nav .nav-item .nav-link.active::after {
    width: 60px;
}
.courses__nav-two .nav {
    display: flex;
    justify-content: center;
    gap: 10px;
    border: none;
}
.courses__nav-two .nav-link {
    background: #efeff3;
    border: none;
    padding: 12px 20px;
    color: var(--tg-heading-color);
    font-weight: 500;
    font-size: 16px;
    text-transform: capitalize;
    border-radius: 6px;
    line-height: 1;
}
.courses__nav-two .nav-link.active {
    box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.15);
    background: #fff;
}
.courses__item-bottom .button a:hover,
.courses__nav .courses-button-next:hover,
.courses__nav .courses-button-prev:hover {
    box-shadow: none;
}
.courses__item {
    margin: 0 0 30px;
    background: var(--tg-common-color-white);
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    padding: 25px 25px 32px;
    border: 1px solid #b5b5c3;
    -webkit-transition: 0.2s ease-out;
    -moz-transition: 0.2s ease-out;
    -ms-transition: 0.2s ease-out;
    -o-transition: 0.2s ease-out;
    transition: 0.2s ease-out;
}
.courses__item-eight,
.courses__item-five,
.courses__item-four,
.courses__item-nine,
.courses__item-seven,
.courses__item-six {
    background: var(--tg-common-color-white);
    margin-bottom: 30px;
}
.courses__item:hover {
    filter: drop-shadow(10px 10px 0px #cac9d6);
}
.courses__item-two {
    padding: 0;
}
.courses__item-three {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    gap: 30px;
    padding: 25px 30px 25px 25px;
}
.courses__item-three .courses__item-thumb {
    margin: 0;
    flex: 0 0 auto;
}
.courses__item-three .courses__item-thumb a {
    height: 100%;
}
.courses__item-three .courses__item-thumb img {
    width: 280px;
    object-fit: cover;
    height: 100%;
    min-height: 210px;
}
.course-rate__details,
.courses__item-three .courses__item-content {
    flex-grow: 1;
}
.courses__item-three .courses__item-content .info {
    margin-bottom: 0;
    margin-top: 8px;
}
.courses__item-three .courses__item-tag {
    gap: 15px;
}
.courses__item-three .courses__item-meta {
    margin: 0 0 8px;
}
.courses__item-three .courses__item-meta .price {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    font-size: 20px;
    font-weight: 700;
    color: var(--tg-theme-primary);
    gap: 8px;
}
.courses__item-three .courses__item-meta .price del {
    font-size: 14px;
    font-weight: 500;
    color: #b2bbcc;
}
.courses__item-four {
    border: 1px solid #dfdfdf;
    border-radius: 15px;
    padding: 25px 25px 35px;
}
.courses__item-five {
    border: 1px solid #dfdfdf;
    padding: 30px;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
}
.courses__item-six {
    border: 1px solid var(--tg-border-6);
    border-radius: 20px;
    padding: 25px 30px;
}
.courses__item-seven {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    overflow: hidden;
}
.courses__item-eight,
.courses__item-nine {
    border: 1px solid #e7e7e7;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    overflow: hidden;
}
.courses__item-thumb {
    position: relative;
    margin: 0 0 20px;
}
.courses__item-thumb-two {
    padding: 25px 25px 0;
}
.courses__item-thumb img {
    width: 100%;
    height: 190px;
    object-fit: cover;
    border-radius: 6px;
}
.courses__details-video,
.courses__item-thumb-five,
.courses__item-thumb-three {
    position: relative;
    margin-bottom: 20px;
}
.courses__item-thumb-three img {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    width: 100%;
    height: 200px;
    object-fit: cover;
}
.courses__item-thumb-four {
    margin-bottom: 24px;
}
.courses__item-thumb-four img {
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    -o-border-radius: 12px;
    -ms-border-radius: 12px;
    border-radius: 12px;
    width: 100%;
    height: 248px;
    object-fit: cover;
}
.courses__item-thumb-five img {
    border-radius: 10px;
    width: 100%;
    object-fit: cover;
    height: 200px;
}
.courses__item-thumb-five .courses__wishlist-two {
    width: 36px;
    height: 36px;
    color: var(--tg-heading-color);
}
.choose__area,
.choose__inner-wrap,
.courses__item-thumb-eight,
.courses__item-thumb-seven,
.courses__item-thumb-six,
.tab-content {
    position: relative;
}
.courses__item-thumb-six::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.31);
    pointer-events: none;
}
.courses__item-thumb-six img {
    width: 100%;
    height: 220px;
    object-fit: cover;
}
.courses__item-thumb-six .courses__wishlist-two {
    z-index: 1;
    width: 36px;
    height: 36px;
    font-size: 20px;
    color: var(--tg-heading-color);
    right: 24px;
    top: 24px;
}
.courses__item-thumb-six .courses__review {
    position: absolute;
    left: 30px;
    bottom: 25px;
    margin-bottom: 0;
    z-index: 2;
    line-height: 1;
}
.courses__item-thumb-eight::after,
.courses__item-thumb-seven::after {
    left: 0;
    top: 0;
    height: 100%;
    background: rgba(0, 0, 0, 0.31);
    opacity: 0.2;
    width: 100%;
    position: absolute;
    content: "";
    pointer-events: none;
}
.courses__item-thumb-six .courses__review span,
.discover-courses-btn-two .btn:hover svg,
.features__content-seven .title {
    color: var(--tg-common-color-white);
}
.courses__item-thumb-six .courses__review .rating {
    color: var(--tg-common-color-yellow);
}
.courses__item-thumb-seven img {
    width: 100%;
    height: 220px;
    object-fit: cover;
}
.courses__item-thumb-eight img {
    width: 100%;
    height: 220px;
    object-fit: cover;
}
.courses__item-thumb-eight .courses__wishlist {
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    position: absolute;
    right: 25px;
    top: 25px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    background: var(--tg-common-color-white);
    z-index: 1;
}
.courses__item-content-bottom,
.courses__item-content-bottom > .author-two a {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
    gap: 10px;
}
.courses__item-thumb-eight .courses__wishlist svg {
    width: 20px;
    height: auto;
}
.courses__item-thumb-eight .courses__wishlist:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.courses__item-thumb-eight .courses__item-tag-three {
    background: var(--tg-theme-secondary);
}
.courses__item-content .title {
    font-size: 18px;
    font-weight: var(--tg-fw-semi-bold);
    line-height: 1.4;
    margin: 0 0 15px;
}
.courses__item-content .title a {
    display: inline;
    background-image: linear-gradient(
            var(--tg-heading-color),
            var(--tg-heading-color)
        ),
        linear-gradient(var(--tg-heading-color), var(--tg-heading-color));
    background-size: 0 2px, 0 2px;
    background-position: 100% 100%, 0 100%;
    background-repeat: no-repeat;
    transition: background-size 0.4s linear;
}
.courses__item-content .title a:hover {
    color: inherit;
    background-size: 0 2px, 100% 2px;
}
.courses__item-content-bottom > .author-two a:hover,
.courses__item-meta-two li svg path {
    color: var(--tg-theme-secondary);
}
.courses__item-content-two {
    padding: 0 25px 20px;
}
.courses__item-content-two .price {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: var(--tg-theme-primary);
}
.course-review-head .review-author-content .author-name .name span,
.courses__item-content-two .price del {
    font-size: 14px;
    font-weight: 400;
    color: var(--tg-common-color-gray-3);
}
.courses__item-content-bottom {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}
.courses__item-content-bottom > .author-two a {
    display: flex;
    font-size: 15px;
    color: var(--tg-body-color);
}
.courses__item-content-bottom-two .list-wrap,
.courses__item-content-bottom-two .list-wrap li {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__item-content-bottom > .author-two a img {
    border-radius: 50%;
    width: 32px;
    height: 32px;
}
.courses__item-content-bottom-two .list-wrap {
    display: flex;
    gap: 20px 48px;
}
.courses__item-content-bottom-two .list-wrap li {
    display: flex;
    gap: 9px;
    position: relative;
}
.courses__item-content-five .title a,
.courses__item-content-four .title a,
.courses__item-content-three .title a {
    display: inline;
    background-image: linear-gradient(
            var(--tg-heading-color),
            var(--tg-heading-color)
        ),
        linear-gradient(var(--tg-heading-color), var(--tg-heading-color));
    background-size: 0 2px, 0 2px;
    background-position: 100% 100%, 0 100%;
    background-repeat: no-repeat;
    transition: background-size 0.4s linear;
}
.courses__item-content-bottom-two .list-wrap li .icon,
.courses__item-content-six-top {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__item-content-bottom-two .list-wrap li::before {
    content: "";
    position: absolute;
    right: -26px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 30px;
    background: #ccc;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -o-border-radius: 3px;
    -ms-border-radius: 3px;
    border-radius: 3px;
}
.courses__item-content-bottom-two .list-wrap li .icon {
    width: 34px;
    height: 34px;
    display: flex;
    justify-content: center;
    flex: 0 0 auto;
    background: #f3f3f4;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    font-size: 20px;
    font-weight: 700;
    color: var(--tg-body-color);
}
.courses__item-content-five .title a:hover,
.courses__item-content-four .title a:hover,
.courses__item-content-three .title a:hover {
    color: inherit;
    background-size: 0 2px, 100% 2px;
}
.courses__item-content-bottom-two .list-wrap li p {
    font-size: 14px;
    line-height: 1.28;
    margin-bottom: 0;
}
.courses__item-content-bottom-two .list-wrap li p span {
    display: block;
    font-weight: 500;
    color: var(--tg-heading-color);
}
.courses__item-content-three .title,
.features__content-three .title {
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: 600;
    text-transform: capitalize;
}
.courses__item .avg-rating,
.courses__item-content-four .avg-rating {
    color: #7f7e97;
    font-size: 14px;
    line-height: 1;
}
.courses__item-content-four .avg-rating i {
    color: var(--tg-common-color-yellow);
    letter-spacing: 0;
    margin-right: 5px;
}
.courses__item-content-four .title {
    margin-bottom: 18px;
    font-weight: 600;
    font-size: 20px;
    text-transform: capitalize;
}
.courses__item-content-five .courses__item-meta .price {
    font-size: 22px;
    font-weight: 700;
    line-height: 1;
    font-family: var(--tg-heading-font-family);
    color: var(--tg-theme-primary);
}
.courses__item-content-five .title {
    margin-bottom: 8px;
    font-size: 22px;
    font-weight: 600;
}
.courses__item-content-five > p {
    margin-bottom: 22px;
}
.courses__item-content-six {
    padding: 25px 22px 20px;
}
.courses__item-content-six-top {
    display: flex;
    gap: 20px;
    justify-content: space-between;
    margin-bottom: 5px;
}
.courses__item-content-seven .courses__wishlist a,
.courses__item-meta {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__item-content-six-top .title {
    margin-bottom: 0;
    font-size: 22px;
    text-transform: capitalize;
}
.courses__item-content-six-top .title a:hover {
    color: var(--tg-theme-primary);
}
.courses__item-content-six-top .price {
    margin-bottom: 0;
    color: var(--tg-theme-primary);
    font-size: 22px;
    font-weight: 700;
}
.courses__item-content-six > span {
    font-size: 15px;
    text-transform: capitalize;
    font-weight: 400;
    display: block;
    color: var(--tg-body-color);
    line-height: 1.4;
    margin-bottom: 15px;
}
.courses__item-content-six > p {
    margin-bottom: 0;
    line-height: 1.5;
}
.courses__item-content-seven {
    padding: 20px 30px 25px;
}
.courses__item-content-seven .price {
    font-size: 22px;
    font-weight: 700;
    font-family: var(--tg-heading-font-family);
    color: var(--tg-theme-primary);
    line-height: 1;
}
.courses__item-content-seven .courses__wishlist a {
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    background: #ebebf0;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    color: var(--tg-body-color);
}
.courses__item-content-eight .title a,
.courses__item-content-seven .title a {
    display: inline;
    background-image: linear-gradient(
            var(--tg-heading-color),
            var(--tg-heading-color)
        ),
        linear-gradient(var(--tg-heading-color), var(--tg-heading-color));
    background-size: 0 2px, 0 2px;
    background-position: 100% 100%, 0 100%;
    background-repeat: no-repeat;
    transition: background-size 0.4s linear;
}
.courses__item-content-seven .courses__wishlist a svg {
    width: 18px;
    height: auto;
}
.courses__item-content-seven .courses__wishlist a:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.courses__item-content-eight .title a:hover,
.courses__item-content-seven .title a:hover {
    color: inherit;
    background-size: 0 2px, 100% 2px;
}
.courses__item-content-seven .title {
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: 600;
    text-transform: capitalize;
}
.courses-widget .form-check .rating ul li,
.courses-widget ul li:last-child,
.courses-widget:last-child,
.courses__item-content-seven .courses__review {
    margin-bottom: 0;
}
.courses__item-content-eight {
    padding: 25px 30px;
}
.courses__item-content-eight .title {
    margin-bottom: 20px;
    font-size: 20px;
    text-transform: capitalize;
}
.courses__item-content-eight .price {
    margin-bottom: 0;
    font-weight: 700;
    font-size: 22px;
    color: var(--tg-theme-primary);
    line-height: 1;
}
.courses__item-meta {
    display: flex;
    justify-content: space-between;
    gap: 10px 20px;
    margin: 0 0 15px;
    flex-wrap: wrap;
}
.courses__item-meta li,
.courses__item-meta-two {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__item-meta li {
    display: flex;
}
.courses__item-meta li i {
    margin-right: 7px;
}
.courses__item-meta-two {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 20px;
    margin-bottom: 14px;
}
.courses__item .avg-rating-two .rating,
.courses__item-meta-two li {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__item-meta-two li {
    color: var(--tg-heading-color);
    font-weight: 500;
    font-size: 14px;
    text-transform: capitalize;
    display: flex;
    line-height: 1;
    gap: 8px;
}
.courses__item-meta-two li svg {
    transform: translateY(-3px);
}
.courses__item-meta-two li svg path:nth-child(5),
.courses__item-meta-two li:nth-child(2) svg path:nth-child(4),
.courses__item-meta-two li:nth-child(2) svg path:nth-child(5) {
    color: #b9b8c0;
}
.courses__item-tag a:hover,
.courses__item-tag-three:hover {
    color: var(--tg-common-color-white);
    background: var(--tg-theme-primary);
}
.courses__item-tag a {
    font-size: 13px;
    font-weight: var(--tg-fw-medium);
    color: var(--tg-heading-color);
    background: #efeff2;
    display: block;
    line-height: 1;
    -webkit-border-radius: 50px;
    -moz-border-radius: 50px;
    -o-border-radius: 50px;
    -ms-border-radius: 50px;
    border-radius: 50px;
    padding: 7px 13px;
}
.courses__item-tag-two a {
    background: #f1f0ff;
    color: var(--tg-theme-primary);
}
.courses__item-tag-three {
    font-weight: 500;
    font-size: 14px;
    position: absolute;
    left: 30px;
    top: 27px;
    padding: 8px 12px;
    color: var(--tg-heading-color);
    background: var(--tg-common-color-white);
    z-index: 1;
    border-radius: 4px;
    line-height: 1;
}
.courses__item .author a:hover,
.courses__item-bottom .price {
    color: var(--tg-theme-primary);
}
.courses__item .avg-rating i {
    color: var(--tg-common-color-yellow);
    letter-spacing: 0;
    margin-right: 5px;
}
.courses__item .avg-rating-two .rating {
    display: flex;
}
.courses__item .author,
.courses__item-bottom {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__item .author {
    display: flex;
    font-size: 15px;
    margin: 0;
    line-height: 1;
    gap: 5px;
}
.courses__item .author a {
    flex: 0 0 auto;
    color: var(--tg-heading-color);
}
.courses__item-bottom {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 23px 0 0;
}
.courses__price,
.courses__price-two {
    background: var(--tg-theme-secondary);
    display: block;
    position: absolute;
    top: 20px;
}
.courses__item-bottom .button a,
.courses__item-bottom-two .list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__item-bottom .button a {
    display: flex;
    padding: 10px 19px;
    gap: 6px;
    border-radius: 50px;
    border: 1px solid #000;
    background: var(--tg-theme-secondary);
    box-shadow: 4px 4px 0 0 #3d3d3d;
    font-family: var(--tg-heading-font-family);
    font-size: 14px;
    line-height: 18px;
    font-weight: var(--tg-fw-semi-bold);
    color: var(--tg-heading-color);
}
.courses__item-bottom .price {
    font-size: 20px;
    line-height: 1;
    font-weight: var(--tg-fw-bold);
    margin: 0;
}
.courses__price,
.courses__price-two {
    font-weight: 700;
    color: var(--tg-heading-color);
}
.courses__item-bottom .price del {
    font-size: 16px;
    color: #8d9db5;
    margin-right: 4px;
}
.courses__review .rating i,
.courses__wishlist a i {
    margin: 0;
}
.courses__item-bottom-two {
    border-top: 1px solid #b5b5c3;
    padding: 13px 25px;
}
.courses__item-bottom-two .list-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 30px;
}
.courses__item-bottom-three .list-wrap,
.courses__item-bottom-two .list-wrap li {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__item-bottom-two .list-wrap li {
    display: flex;
    position: relative;
    font-size: 16px;
    color: var(--tg-common-color-dark);
    gap: 7px;
}
.courses__item-bottom-two .list-wrap li i {
    color: var(--tg-common-color-gray-3);
    font-size: 20px;
    line-height: 0;
}
.courses__item-bottom-two .list-wrap li::before {
    content: "";
    position: absolute;
    right: -17px;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 5px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    background: #8c9ab4;
}
.courses__item-bottom-three .list-wrap {
    display: flex;
    gap: 10px 20px;
    flex-wrap: wrap;
}
.courses__item-bottom-three .list-wrap li,
.courses__wishlist-two {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
    line-height: 1;
}
.courses__item-bottom-three .list-wrap li {
    display: flex;
    gap: 8px;
    font-size: 15px;
}
.courses__item-bottom-three .list-wrap li i {
    font-size: 20px;
    line-height: 0;
}
.courses__item-bottom-four {
    border-top: 1px solid #e7e7e7;
    padding: 18px 22px 19px;
}
.courses__item-bottom-four .list-wrap li {
    color: var(--tg-heading-color);
    font-size: 15px;
}
.courses__item-bottom-four .list-wrap li i {
    color: var(--tg-body-color);
}
.courses__item-bottom-five,
.courses__item-bottom-six {
    border-top: 1px solid #e7e7e7;
    padding: 17px 30px;
}
.courses__item-bottom-six .list-wrap {
    justify-content: space-between;
}
.courses__price {
    right: 0;
    font-size: 16px;
    line-height: 1;
    border-radius: 6px 0 0 6px;
    padding: 9px;
}
.courses__price-two {
    font-size: 15px;
    left: 20px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    line-height: 1;
    padding: 8px 10px;
}
.courses__nav .courses-button-prev,
.courses__wishlist-two {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    position: absolute;
}
.courses__wishlist a {
    font-size: 20px;
    line-height: 0;
    color: #babaca;
}
.courses__wishlist a:hover {
    color: var(--tg-theme-primary);
}
.courses__wishlist-two {
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    background: var(--tg-common-color-white);
    right: 20px;
    top: 20px;
    border-radius: 50%;
    color: var(--tg-body-color);
}
.courses__nav .courses-button-prev,
.courses__review {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__wishlist-two svg {
    width: 18px;
    height: auto;
}
.courses-slider-active .slick-arrow:hover,
.courses__wishlist-two:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.courses__review {
    display: flex;
    gap: 6px;
    margin-bottom: 12px;
}
.courses__review .rating {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    font-size: 14px;
    gap: 4px;
    color: var(--tg-theme-secondary);
}
.courses__nav .courses-button-next,
.courses__nav .courses-button-prev {
    width: 60px;
    height: 60px;
    line-height: 0;
    font-size: 24px;
    top: 50%;
    z-index: 1;
}
.courses__nav .courses-button-next,
.courses__nav .courses-button-prev,
.courses__nav-tabs .nav-item .nav-link.active {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.courses__review span {
    font-size: 14px;
    color: var(--tg-common-color-gray-3);
}
.courses-top-left p,
.courses-top-right-select select {
    color: var(--tg-common-color-dark);
    text-transform: capitalize;
}
.courses__review-two {
    margin-bottom: 0;
    line-height: 1;
}
.courses__nav .courses-button-prev {
    display: flex;
    justify-content: center;
    border-radius: 50%;
    transition: 0.3s ease-out;
    border: 1.2px solid var(--tg-common-color-black-3);
    box-shadow: -3.6px 2.4px 0 0 #23232b;
    left: -90px;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
}
.courses-top-right,
.courses__nav .courses-button-next,
.instructor__details-nav {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__details-content .nav-tabs .nav-link,
.courses__nav .courses-button-next {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.courses__nav .courses-button-prev i {
    -webkit-transform: rotate(-180deg);
    -moz-transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    -o-transform: rotate(-180deg);
    transform: rotate(-180deg);
}
.courses__nav .courses-button-next {
    display: flex;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    transition: 0.3s ease-out;
    border: 1.2px solid var(--tg-common-color-black-3);
    box-shadow: 3.6px 2.4px 0 0 #23232b;
    position: absolute;
    right: -90px;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
}
.courses-top-left p,
.courses-top-right .sort-by {
    font-size: 16px;
    font-family: var(--tg-heading-font-family);
}
.categories__nav button,
.courses-top-right-select::after {
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
}
.courses-top-left p {
    margin-bottom: 0;
}
.courses-top-right,
.instructor__details-nav {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
.courses-top-right .sort-by {
    display: block;
    color: var(--tg-common-color-gray-3);
}
.courses-top-right-select {
    width: 145px;
    position: relative;
}
.courses-top-right-select select {
    background-color: transparent;
    border: 1px solid #dcdbe5;
    font-weight: 500;
    font-size: 15px;
    border-radius: 4px;
    outline: 0;
    padding: 10px 32px 10px 15px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 100%;
    line-height: 1.2;
    height: 40px;
}
.courses-top-right-select::after {
    content: "\f078";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-family: "Font Awesome 5 Free";
    font-weight: 700;
    right: 12px;
    font-size: 12px;
    color: var(--tg-theme-primary);
}
.categories__item .name,
.choose__list-item .content span,
.courses-widget .show-more {
    font-family: var(--tg-heading-font-family);
}
.courses__sidebar {
    margin-right: 20px;
}
@media (max-width: 1199.98px) {
    .courses-area .section__title-wrap {
        margin: 0 0 30px;
    }
    .courses__item {
        padding: 25px 20px 32px;
    }
    .courses__item-three {
        gap: 20px;
        padding: 20px;
    }
    .courses__item-bottom-two .list-wrap {
        gap: 10px 20px;
    }
    .courses__item-bottom-two .list-wrap li::before {
        right: -12px;
    }
    .courses__sidebar {
        margin: 0;
    }
}
.courses-widget {
    background: var(--tg-common-color-gray);
    padding: 30px;
    margin-bottom: 30px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
}
.courses-widget ul li {
    margin-bottom: 15px;
}
.courses-widget .form-check,
.shop-widget .form-check {
    display: flex;
    align-items: center;
    line-height: 1;
    margin-bottom: 0;
    padding: 0;
    min-height: inherit;
}
.courses-slider-active .slick-arrow,
.courses__nav-tabs .nav-item .nav-link {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.courses-widget .form-check-input {
    border-radius: 4px;
    border: 1px solid #9d9cae;
    margin: 0 10px 0 0;
    width: 16px;
    height: 16px;
}
.courses-widget .form-check-input:focus,
.shop-widget .form-check-input:focus {
    box-shadow: none;
    border-color: var(--tg-theme-primary);
}
.courses-widget .form-check-label,
.shop-widget .form-check-label {
    flex-grow: 1;
    font-size: 16px;
    font-weight: 400;
    color: var(--tg-body-color);
    user-select: none;
}
.courses-widget .form-check .rating,
.shop-widget .form-check .rating {
    display: flex;
    align-items: center;
}
.courses-widget .form-check .rating ul,
.shop-widget .form-check .rating ul {
    display: flex;
    align-items: center;
    line-height: 1;
    color: #f8bc24;
    gap: 4px;
}
.courses-widget .form-check .rating ul li.delete,
.shop-widget .form-check .rating ul li.delete {
    color: #d7d7d7;
}
.courses-widget .form-check .rating span,
.shop-widget .form-check .rating span {
    color: #5a7093;
    margin-left: 5px;
}
.courses-widget .show-more {
    font-size: 15px;
    font-weight: 500;
    line-height: 1;
    margin-top: 20px;
}
.courses-slider-active .slick-arrow {
    position: absolute;
    left: -10px;
    top: 50%;
    -webkit-transform: translateY(-50%) translateX(-30px);
    -moz-transform: translateY(-50%) translateX(-30px);
    -ms-transform: translateY(-50%) translateX(-30px);
    -o-transform: translateY(-50%) translateX(-30px);
    transform: translateY(-50%) translateX(-30px);
    width: 50px;
    height: 50px;
    background: var(--tg-common-color-gray);
    border: 1px solid #fff;
    box-shadow: 0 10px 20px #dfdfdf;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    padding: 0;
    z-index: 2;
}
.courses-slider-active .slick-arrow.slick-next {
    left: auto !important;
    right: -10px;
    -webkit-transform: translateY(-50%) translateX(30px);
    -moz-transform: translateY(-50%) translateX(30px);
    -ms-transform: translateY(-50%) translateX(30px);
    -o-transform: translateY(-50%) translateX(30px);
    transform: translateY(-50%) translateX(30px);
}
.courses-slider-active:hover .slick-arrow {
    -webkit-transform: translateY(-50%) translateX(0);
    -moz-transform: translateY(-50%) translateX(0);
    -ms-transform: translateY(-50%) translateX(0);
    -o-transform: translateY(-50%) translateX(0);
    transform: translateY(-50%) translateX(0);
    opacity: 1;
    visibility: visible;
}
.courses-slider-active .col {
    padding-left: 15px;
    padding-right: 15px;
}
.courses-slider-active .courses__item-two {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    box-shadow: none;
    padding: 15px;
}
.courses-slider-active .courses__item-two-thumb .author {
    position: absolute;
    right: 15px;
    bottom: -25px;
    z-index: 2;
}
.courses-slider-active .courses__item-two-thumb .author img {
    max-width: 50px;
    max-height: 50px;
    width: auto;
    height: auto;
    border: 3px solid #fff;
    border-radius: 50px;
}
.courses-slider-active .courses__item-two-content {
    padding: 16px 7px 5px;
}
.courses-slider-active .courses__item-bottom .price {
    font-size: 20px;
    margin: 0 20px 0 0;
}
.courses-slider-active .courses__item-bottom .price del {
    font-size: 15px;
    margin-right: 6px;
}
.courses__nav-tabs {
    gap: 10px;
    border: none;
    margin-left: 10px;
}
.courses__nav-tabs .nav-item {
    margin: 0;
}
.courses__nav-tabs .nav-item .nav-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid #6196ea;
    border-radius: 4px;
    color: var(--tg-theme-primary);
}
.courses__details-meta .list-wrap,
.courses__list-wrap .courses__item-two,
.courses__list-wrap .courses__item-two-content .courses__item-rating {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.courses__nav-tabs .nav-item .nav-link svg {
    flex: 0 0 auto;
}
.courses__nav-tabs .nav-item .nav-link.active {
    border-color: var(--tg-theme-primary);
}
.courses__list-wrap .courses__item-two {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    box-shadow: none;
    padding: 15px;
    display: flex;
    gap: 25px;
}
.courses__list-wrap .courses__item-two-thumb {
    flex: 0 0 auto;
}
.courses__list-wrap .courses__item-two-thumb a {
    height: 100%;
}
.courses__list-wrap .courses__item-two-thumb img {
    width: 270px;
    height: 100%;
    object-fit: cover;
}
@media (max-width: 1199.98px) {
    .courses__list-wrap .courses__item-two-thumb img {
        width: 235px;
    }
}
.courses__list-wrap .courses__item-two-content {
    padding: 0 15px 0 0;
}
.courses__list-wrap .courses__item-two-content .title {
    font-size: 20px;
    margin: 0 0 10px;
}
.courses__list-wrap .courses__item-two-content p {
    line-height: 1.65;
}
.courses__list-wrap .courses__item-two-content .courses__item-rating {
    display: flex;
    align-items: center;
    gap: 6px;
}
.courses__list-wrap .courses__item-two-content .courses__item-rating i {
    font-size: 15px;
    margin: 0;
}
.courses__list-wrap
    .courses__item-two-content
    .courses__item-rating
    .rating-count {
    margin: 0;
}
.courses__list-wrap .courses__item-two-content .courses__item-tag {
    margin: 0 0 8px;
    font-size: 14px;
}
.courses__list-wrap .courses__item-two-content .courses__item-meta {
    margin: 0 0 14px;
}
.course-review-head .review-author-content p,
.courses__curriculum-wrap .course-item:last-child,
.courses__overview-wrap > p.last-info {
    margin-bottom: 0;
}
.courses__list-wrap .courses__item-bottom {
    padding: 12px 0 0;
    margin: 0;
}
.courses__list-wrap .courses__item-bottom .author img {
    max-width: 35px;
}
.courses__shape-wrap img:first-child {
    left: 10%;
    top: 23%;
}
@media (max-width: 1199.98px) {
    .courses__shape-wrap img:first-child {
        top: 13%;
    }
}
.courses__shape-wrap img:nth-child(2) {
    left: 8%;
    bottom: 14%;
}
.courses__shape-wrap img:nth-child(3) {
    right: 11%;
    top: 28%;
}
.courses__shape-wrap img:nth-child(4) {
    right: 5%;
    top: 18%;
}
@media (max-width: 1800px) {
    .courses__shape-wrap img:first-child {
        left: 4%;
    }
    .courses__shape-wrap img:nth-child(2) {
        left: 3%;
    }
    .courses__shape-wrap img:nth-child(3) {
        right: 2%;
        top: 20%;
    }
    .courses__shape-wrap img:nth-child(4) {
        right: 4%;
    }
}
.courses__shape-wrap-two img:first-child {
    right: -126px;
    top: -126px;
    max-width: 333px;
    -webkit-animation-duration: 50s;
    animation-duration: 50s;
}
.courses__shape-wrap-two img:nth-child(2) {
    bottom: -126px;
    left: -126px;
    max-width: 333px;
    -webkit-animation-duration: 50s;
    animation-duration: 50s;
}
@media (max-width: 1500px) {
    .courses__item-five {
        padding: 25px 22px;
    }
    .courses__item-six {
        padding: 25px;
    }
    .courses__item-thumb-two {
        padding: 20px 20px 0;
    }
    .courses__item-content-two {
        padding: 0 20px 20px;
    }
    .courses__item-content-bottom-two .list-wrap {
        gap: 20px 30px;
    }
    .courses__item-content-bottom-two .list-wrap li::before {
        right: -16px;
    }
    .courses__item-content-seven {
        padding: 20px 20px 25px;
    }
    .courses__item-content-eight {
        padding: 25px 20px;
    }
    .courses__item-bottom-two {
        padding: 13px 20px;
    }
    .courses__item-bottom-five,
    .courses__item-bottom-six {
        padding: 17px 20px;
    }
    .courses__nav .courses-button-prev {
        left: -15px;
    }
    .courses__nav .courses-button-next {
        right: -15px;
    }
    .courses__shape-wrap img:nth-child(4) {
        right: 1%;
        top: 25%;
        width: 250px;
    }
    .courses__shape-wrap-two img:first-child,
    .courses__shape-wrap-two img:nth-child(2) {
        max-width: 300px;
    }
}
.courses__shape-wrap-three img:first-child {
    left: 0;
    top: 18%;
    max-width: 234px;
}
@media (max-width: 1199.98px) {
    .courses__shape-wrap img:nth-child(4) {
        top: 15%;
    }
    .courses__shape-wrap-three img:first-child {
        top: 6%;
    }
}
.courses__shape-wrap-three img:nth-child(2) {
    left: 4%;
    bottom: 12%;
    max-width: 99px;
}
@media (max-width: 1800px) {
    .courses__shape-wrap-three img:nth-child(2) {
        left: 1%;
        bottom: 6%;
    }
}
.courses__shape-wrap-three img:nth-child(3) {
    right: 8%;
    top: 17%;
    max-width: 52px;
}
.courses__shape-wrap-three img:nth-child(4) {
    right: 4%;
    bottom: 13%;
    max-width: 94px;
}
@media (max-width: 1800px) {
    .courses__shape-wrap-three img:nth-child(4) {
        right: 2%;
        bottom: 7%;
        max-width: 80px;
    }
}
.courses__shape-wrap-four img:first-child {
    left: 5%;
    top: 7%;
    max-width: 123px;
}
.courses__shape-wrap-four img:nth-child(2) {
    right: -82px;
    bottom: 14%;
    max-width: 170px;
    -webkit-animation-duration: 30s;
    animation-duration: 30s;
}
.courses__details-thumb img {
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
    min-height: 250px;
    object-fit: cover;
}
.courses__details-content .courses__item-meta {
    justify-content: flex-start;
    gap: 15px;
}
.courses__details-content .courses__item-meta .avg-rating {
    color: #7f7e97;
    font-size: 14px;
    line-height: 1;
}
.courses__details-content .courses__item-meta .avg-rating i {
    color: var(--tg-common-color-yellow);
}
.courses__details-content .title {
    margin-bottom: 15px;
    font-size: 30px;
}
.courses__details-content .nav-tabs {
    border: none;
    gap: 12px;
    margin-bottom: 40px;
}
.courses__details-content .nav-tabs .nav-link {
    background: #e6e9ef;
    font-size: 16px;
    font-weight: 600;
    font-family: var(--tg-heading-font-family);
    border: none;
    color: var(--tg-body-color);
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
    padding: 14px 30px;
    line-height: 1;
    display: block;
    transition: 0.3s ease-out;
}
.courses__details-social .list-wrap li a:hover,
.courses__details-video .popup-video:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.categories__item .icon,
.categories__item-two a .content .name strong {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.courses__details-content .nav-tabs .nav-link.active,
.courses__details-content .nav-tabs .nav-link:hover {
    color: var(--tg-common-color-white);
    background: var(--tg-theme-primary);
    -webkit-box-shadow: 4px 6px 0 0 #050071;
    -moz-box-shadow: 4px 6px 0 0 #050071;
    -ms-box-shadow: 4px 6px 0 0 #050071;
    -o-box-shadow: 4px 6px 0 0 #050071;
    box-shadow: 4px 6px 0 0 #050071;
}
.courses__details-meta .list-wrap {
    display: flex;
    align-items: center;
    gap: 10px 30px;
    flex-wrap: wrap;
    margin-bottom: 45px;
}
.courses__details-meta .list-wrap .author-two,
.courses__details-meta .list-wrap li,
.review__wrap {
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.courses__details-meta .list-wrap li {
    color: #7f7e97;
    position: relative;
    display: flex;
    gap: 8px;
}
.courses__details-meta .list-wrap li::before {
    content: "";
    position: absolute;
    right: -17px;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -o-border-radius: 5px;
    -ms-border-radius: 5px;
    border-radius: 5px;
    background: #8c9ab4;
}
.courses__details-meta .list-wrap .author-two img,
.courses__details-video .popup-video {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
}
.courses__details-meta .list-wrap li i {
    font-size: 20px;
}
.courses__details-meta .list-wrap .author-two,
.review__wrap {
    display: flex;
    gap: 5px;
}
.courses__details-meta .list-wrap .author-two img {
    border-radius: 50%;
    margin-right: 5px;
}
.courses__details-meta .list-wrap .author-two a {
    color: var(--tg-heading-color);
}
.courses__details-sidebar {
    background: var(--tg-common-color-white);
    border: 1px solid #dfdfdf;
    -webkit-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -moz-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -ms-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -o-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    margin-left: 20px;
    padding: 30px 30px 40px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
}
.courses__curriculum-wrap,
.courses__instructors-wrap,
.courses__overview-wrap {
    -webkit-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    -moz-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    -ms-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    -o-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    background: var(--tg-common-color-white);
}
@media (max-width: 1500px) {
    .courses__shape-wrap-four img:nth-child(2) {
        right: -62px;
        bottom: 6%;
        max-width: 140px;
    }
    .courses__details-sidebar {
        margin-left: 0;
        padding: 30px 20px 40px;
    }
}
.courses__details-video img {
    width: 100%;
    min-height: 200px;
    object-fit: cover;
    border-radius: 8px;
}
.courses__details-video .popup-video {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--tg-common-color-white);
    z-index: 3;
}
.courses__curriculum-wrap .course-item .course-item-link,
.courses__details-social .list-wrap,
.courses__details-social .list-wrap li a {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.courses__details-social {
    border-bottom: 1px solid #dfdfdf;
    margin-bottom: 30px;
    padding-bottom: 30px;
}
.courses__details-social .title {
    margin-bottom: 12px;
    font-weight: 500;
    font-size: 16px;
}
.courses__details-social .list-wrap {
    display: flex;
    align-items: center;
    gap: 10px;
}
.courses__details-social .list-wrap li a {
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #e6e9ef;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    color: var(--tg-body-color);
}
.courses__details-enroll .tg-button-wrap,
.error-content .tg-button-wrap {
    justify-content: center;
}
.courses__curriculum-wrap,
.courses__overview-wrap {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    border: 1px solid #dfdfdf;
    box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    padding: 35px 40px;
}
.courses__overview-wrap .title {
    margin-bottom: 12px;
    font-size: 24px;
}
.courses__overview-wrap > p {
    margin-bottom: 20px;
}
.courses__instructors-content > p,
.courses__overview-wrap .about__info-list {
    margin-bottom: 15px;
}
.courses__curriculum-wrap .title {
    margin-bottom: 12px;
    font-size: 24px;
}
.courses__curriculum-wrap > p {
    margin-bottom: 20px;
}
.courses__curriculum-wrap .accordion-item {
    margin: 0;
    border: none;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -o-border-radius: 0;
    -ms-border-radius: 0;
    border-radius: 0;
    overflow: hidden;
}
.courses__curriculum-wrap .accordion-item:last-child {
    margin: 0;
}
.courses__curriculum-wrap
    .accordion-item:last-of-type
    .accordion-button.collapsed {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.courses__curriculum-wrap .accordion-button {
    border: none;
    box-shadow: none;
    font-size: 20px;
    color: var(--tg-heading-color);
    padding: 16px 0;
    font-weight: 500;
    border-bottom: 1px solid #dcdceb;
}
.courses__curriculum-wrap .accordion-button::after {
    content: "\f067";
    font-family: var(--tg-icon-font-family);
    font-weight: 700;
    background-image: none;
    width: auto;
    height: auto;
}
.courses__curriculum-wrap .accordion-button:not(.collapsed) {
    background: 0 0;
    color: var(--tg-theme-primary);
}
.courses__curriculum-wrap .accordion-button:not(.collapsed)::after {
    content: "\f068";
}
.courses__curriculum-wrap .accordion-body,
.lesson__content .accordion-body,
.widget_search {
    padding: 0;
}
.courses__curriculum-wrap .course-item {
    padding: 13px 15px;
    background: #f8f8ff;
    margin-bottom: 2px;
}
.courses__curriculum-wrap .course-item .course-item-link {
    display: flex;
    align-items: center;
    color: var(--tg-common-color-dark);
    cursor: no-drop;
}
.courses__curriculum-wrap .course-item .course-item-link:hover {
    color: var(--tg-theme-primary);
}
.courses__curriculum-wrap .course-item .item-name {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-right: 20px;
}
.courses__curriculum-wrap .course-item .item-name::before {
    content: "";
    display: block;
    background-image: url(../img/icons/play.svg);
    width: 30px;
    height: 30px;
    flex: 0 0 auto;
}
.courses__curriculum-wrap .course-item-meta {
    margin-left: auto;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    line-height: 1;
    gap: 20px;
    flex: 0 0 auto;
}
.courses__instructors-content .avg-rating,
.courses__instructors-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.courses__curriculum-wrap .course-item-meta .duration {
    margin: 2px 0 0;
}
.courses__curriculum-wrap .course-item.open-item .course-item-link {
    cursor: pointer;
}
.courses__instructors-wrap {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    border: 1px solid #dfdfdf;
    box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    padding: 35px 40px;
    display: flex;
    gap: 30px;
}
.courses__rating-wrap,
.instructor__details-biography {
    -webkit-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    -moz-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    -ms-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    -o-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
}
.courses__instructors-thumb {
    width: 225px;
    flex: 0 0 auto;
}
.course-review-head .review-author-thumb img,
.courses__instructors-thumb img {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.courses__instructors-content .title {
    margin-bottom: 5px;
    font-size: 20px;
}
.courses__instructors-content .designation {
    display: block;
    color: var(--tg-theme-primary);
    line-height: 1;
    margin-bottom: 6px;
}
.courses__instructors-content .avg-rating {
    display: flex;
    color: var(--tg-common-color-gray-3);
    font-size: 15px;
    gap: 8px;
    margin-bottom: 8px;
}
.courses__cost-wrap .title,
.courses__information-wrap .list-wrap li {
    align-items: center;
    line-height: 1;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.courses__instructors-content .avg-rating i {
    color: var(--tg-common-color-yellow);
}
.courses__rating-wrap {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    border: 1px solid #dfdfdf;
    background: var(--tg-common-color-white);
    box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    padding: 35px 40px 70px;
}
.courses__rating-wrap .title {
    margin-bottom: 30px;
    font-size: 24px;
}
.courses__cost-wrap {
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -o-border-radius: 8px;
    -ms-border-radius: 8px;
    border-radius: 8px;
    background: var(--tg-theme-primary);
    -webkit-box-shadow: 0 3px 15px 0 rgba(13, 9, 99, 0.26);
    -moz-box-shadow: 0 3px 15px 0 rgba(13, 9, 99, 0.26);
    -ms-box-shadow: 0 3px 15px 0 rgba(13, 9, 99, 0.26);
    -o-box-shadow: 0 3px 15px 0 rgba(13, 9, 99, 0.26);
    box-shadow: 0 3px 15px 0 rgba(13, 9, 99, 0.26);
    padding: 20px;
    margin-bottom: 20px;
}
.courses__cost-wrap span {
    display: block;
    font-weight: 500;
    color: var(--tg-common-color-white);
    line-height: 1;
    margin-bottom: 10px;
}
.courses__cost-wrap .title {
    margin-bottom: 0;
    font-size: 30px;
    color: var(--tg-common-color-white);
    display: flex;
    gap: 5px;
}
.courses__cost-wrap .title del {
    font-size: 20px;
    font-weight: 500;
    color: #9490fa;
}
.courses__information-wrap .title {
    font-size: 16px;
    margin-bottom: 15px;
}
.courses__information-wrap .list-wrap li {
    display: flex;
    color: var(--tg-common-color-dark);
    gap: 15px;
    border-bottom: 1px solid #d9d9d9;
    margin-bottom: 15px;
    padding-bottom: 15px;
}
.courses__information-wrap .list-wrap li span {
    display: block;
    margin-left: auto;
    color: var(--tg-common-color-gray-3);
}
.courses__information-wrap .list-wrap li svg {
    color: var(--tg-common-color-gray-3);
}
.courses__payment {
    border-bottom: 1px solid #d9d9d9;
    margin-bottom: 25px;
    padding-bottom: 30px;
}
.courses__payment .title {
    margin-bottom: 12px;
    font-weight: 500;
    font-size: 16px;
}
.course-rate {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 36px;
}
.course-rate__summary {
    background: var(--tg-common-color-white);
    border: 1px solid #e6eaef;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.09);
    border-radius: 8px;
    width: 180px;
    text-align: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 33px 20px;
    gap: 3px;
}
.course-rate__summary-value {
    color: #082a5e;
    font-weight: 600;
    font-size: 64px;
    line-height: 0.8;
}
.course-rate__summary-stars {
    font-size: 15px;
    color: #f8bc24;
}
.course-rate__summary-text,
.shop-details-content .product-review span {
    color: #5a7093;
}
.course-rate__details-row {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    line-height: 1;
    gap: 10px;
    margin: 0 0 17px;
}
.course-rate__details-row:last-child,
.testimonial__content-four {
    margin: 0;
}
.course-rate__details-row-star {
    font-size: 20px;
}
.course-rate__details-row-star i {
    font-size: 17px;
    color: #f8bc24;
    margin-left: 3px;
}
.course-rate__details-row-value {
    position: relative;
    margin-right: 20px;
    flex-grow: 1;
}
.course-review-head,
.course-review-head .review-author-content .author-name {
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.course-rate__details-row-value .rating-gray {
    background: #e0e3eb;
    border-radius: 50px;
    height: 7px;
}
.course-rate__details-row-value .rating {
    position: absolute;
    left: 0;
    top: 0;
    height: 7px;
    background: #f8bc24;
    border-radius: 50px;
}
.course-rate__details-row-value .rating-count {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: -20px;
    font-size: 20px;
}
.course-review-head {
    display: flex;
    border-top: 1px solid #dfdfdf;
    padding-top: 55px;
    margin-top: 60px;
    gap: 20px;
}
.course-review-head .review-author-thumb {
    width: 80px;
    flex: 0 0 auto;
}
.course-review-head .review-author-content .author-name {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 20px;
    justify-content: space-between;
    margin-bottom: 12px;
}
.course-review-head .review-author-content .author-name .name {
    margin-bottom: 0;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 10px 20px;
    font-size: 18px;
    font-weight: 500;
    color: var(--tg-body-color);
}
.course-review-head .review-author-content .author-name .author-rating {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 3px;
    color: var(--tg-common-color-yellow);
}
.event__meta .list-wrap li:last-child::before,
.fact__inner-wrap .row [class*="col-"]:last-child .fact__item::before,
.fact__item-wrap .fact__item:last-child::before,
.instructor__details-content > .list-wrap > li:last-child::before {
    display: none;
}
.course-review-head .review-author-content .title {
    margin-bottom: 12px;
    font-size: 18px;
}
.shine__animate-link {
    display: block;
    overflow: hidden;
    position: relative;
    z-index: 1;
}
.shine__animate-link::before {
    position: absolute;
    top: 0;
    left: -100%;
    display: block;
    content: "";
    width: 50%;
    height: 100%;
    background: -o-linear-gradient(
        left,
        rgba(255, 255, 255, 0) 0,
        rgba(255, 255, 255, 0.3) 100%
    );
    background: -webkit-gradient(
        linear,
        left top,
        right top,
        from(rgba(255, 255, 255, 0)),
        to(rgba(255, 255, 255, 0.3))
    );
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0,
        rgba(255, 255, 255, 0.3) 100%
    );
    -webkit-transform: skewX(-25deg);
    -ms-transform: skewX(-25deg);
    transform: skewX(-25deg);
}
.shine__animate-item:hover .shine__animate-link::before {
    -webkit-animation: 1.2s hoverShine;
    animation: 1.2s hoverShine;
}
.choose__img-four .right__side .popup-video::after,
.choose__img-three .play-btn::after {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    animation: 1.5s ease-out infinite pulse-border;
    content: "";
    height: 100%;
}
.gutter-24 {
    --bs-gutter-x: 24px;
}
.view-all-btn {
    text-align: center !important;
    margin-bottom: 0 !important;
    margin-top: 30px !important;
}
.discover-courses-btn-two .btn {
    background: 0 0;
    border: 1px solid #7f7e97;
    color: #7f7e97;
}
.discover-courses-btn-two .btn svg {
    color: #7f7e97;
}
.categories__bg,
.choose__bg,
.features__bg {
    background-size: cover;
    background-position: center;
    position: relative;
    z-index: 1;
}
.choose__content {
    background: var(--tg-common-color-white);
    box-shadow: 0 0 30px 0 #e8e8e8;
    border: 1px solid #e3e3e3;
    border-radius: 15px;
    padding: 70px 240px 80px 80px;
    position: relative;
    z-index: 1;
}
@media (max-width: 1199.98px) {
    .courses__curriculum-wrap,
    .courses__instructors-wrap,
    .courses__overview-wrap {
        padding: 30px;
    }
    .courses__rating-wrap {
        padding: 30px 30px 50px;
    }
    .choose__content {
        padding: 50px 120px 60px 25px;
    }
    .choose__content .shape img {
        display: none;
    }
}
@media (max-width: 991.98px) {
    .about__enrolled,
    .courses__shape-wrap img:nth-child(4) {
        display: none;
    }
    .courses__item-five {
        padding: 25px;
    }
    .courses__item-bottom .price {
        font-size: 18px;
    }
    .courses__sidebar {
        margin: 80px 0 0;
    }
    .courses__list-wrap .courses__item-two-thumb img {
        width: 270px;
    }
    .courses__shape-wrap img:first-child {
        top: 8%;
    }
    .courses__shape-wrap img:nth-child(2) {
        bottom: 8%;
    }
    .courses__shape-wrap-three img:first-child {
        top: 3%;
    }
    .courses__shape-wrap-three img:nth-child(2) {
        left: 2%;
        bottom: 1%;
        max-width: 70px;
    }
    .courses__shape-wrap-three img:nth-child(3) {
        top: 7%;
    }
    .courses__shape-wrap-three img:nth-child(4) {
        right: 2%;
        bottom: 5%;
    }
    .courses__shape-wrap-four img:first-child {
        left: 3%;
        top: 4%;
        max-width: 105px;
    }
    .courses__details-sidebar {
        padding: 30px 30px 40px;
        margin: 80px 0 0;
    }
    .choose__content {
        padding: 50px 60px 60px;
    }
}
.choose__content > p,
.testimonial__content-two p {
    margin-bottom: 15px;
}
.choose__content .about__info-list {
    margin-bottom: 30px;
}
.choose__content .shape img {
    position: absolute;
    z-index: -1;
    right: 30%;
    top: 15%;
}
.categories-area-four,
.choose__content-two {
    position: relative;
}
.choose__content-two .title-two {
    margin-bottom: 0;
    font-size: 48px;
    font-weight: 700;
    text-transform: capitalize;
    width: 90%;
}
@media (max-width: 1199.98px) {
    .choose__content-two .title-two {
        font-size: 40px;
        width: 100%;
    }
}
@media (max-width: 767.98px) {
    .courses__item-three .courses__item-thumb img {
        width: 100%;
    }
    .courses__item-thumb img,
    .courses__item-thumb-eight img,
    .courses__item-thumb-five img,
    .courses__item-thumb-seven img,
    .courses__item-thumb-six img {
        height: auto;
    }
    .courses__nav {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        justify-content: center;
        gap: 15px;
    }
    .courses__nav .courses-button-prev {
        position: relative;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0);
        left: 0;
    }
    .courses__nav .courses-button-next {
        position: relative;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0);
        right: 0;
    }
    .courses-top-left p {
        margin-bottom: 15px;
        text-align: center;
    }
    .courses__list-wrap .courses__item-two {
        gap: 20px;
        flex-direction: column;
    }
    .course-rate__details,
    .courses__list-wrap .courses__item-two-thumb img {
        width: 100%;
    }
    .courses__list-wrap .courses__item-two-content {
        padding: 0 7px 5px;
    }
    .courses__shape-wrap img,
    .courses__shape-wrap-four img:first-child,
    .courses__shape-wrap-four img:nth-child(2),
    .courses__shape-wrap-three img:nth-child(4) {
        display: none;
    }
    .courses__shape-wrap-two img:first-child,
    .courses__shape-wrap-two img:nth-child(2) {
        max-width: 250px;
    }
    .courses__shape-wrap-three img:first-child {
        top: 1%;
        max-width: 140px;
    }
    .courses__shape-wrap-three img:nth-child(3) {
        top: 2%;
        right: 5%;
    }
    .courses__details-content .title {
        font-size: 26px;
    }
    .courses__curriculum-wrap .course-item {
        padding: 17px 20px;
    }
    .courses__curriculum-wrap .course-item .item-name {
        line-height: 1.4;
    }
    .course-rate,
    .courses__instructors-wrap {
        flex-direction: column;
    }
    .course-review-head {
        flex-wrap: wrap;
    }
    .choose__content {
        padding: 40px 25px;
    }
    .choose__content-two .title-two {
        font-size: 36px;
    }
}
.choose__content-two .title-two span {
    font-weight: 300;
}
.choose__content-two .title-two strong {
    width: 60px;
    height: 60px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-theme-secondary);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    color: var(--tg-common-color-white);
    margin: 0 5px;
}
.choose__content-two > p {
    margin-bottom: 20px;
    width: 75%;
}
.choose__content-two .about__info-list {
    margin-bottom: 40px;
}
.choose__content-two .shape img {
    position: absolute;
    z-index: -1;
    right: 0;
    bottom: 40%;
    -webkit-animation-duration: 15s;
    animation-duration: 15s;
}
@media (max-width: 991.98px) {
    .choose__content-two .shape img {
        bottom: 37%;
    }
}
@media (max-width: 767.98px) {
    .choose__content-two .shape img {
        bottom: 49%;
    }
    .choose__content-inner-img {
        margin-bottom: 30px;
        text-align: center;
    }
}
.choose__content-three {
    margin-right: 150px;
    margin-left: 10px;
}
.choose__content-three p {
    margin-bottom: 20px;
}
.choose__content-four {
    margin-left: 40px;
    margin-right: 90px;
}
.choose__content-four > p {
    margin-bottom: 25px;
    width: 90%;
}
.choose__content-four .about__info-list,
.choose__list-wrap {
    margin-bottom: 40px;
}
.choose__content-inner-img img {
    border-radius: 449px 449px 449px 0;
    border: 8px solid var(--tg-common-color-white);
}
.choose__list-item {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 17px;
    margin-bottom: 24px;
}
.choose__img .popup-video,
.choose__list-item .icon {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.choose__list-item:last-child {
    margin-bottom: 0;
}
.choose__list-item .icon {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    flex: 0 0 auto;
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    font-size: 35px;
}
.choose__img .popup-video,
.choose__img-three .play-btn {
    background: var(--tg-common-color-white);
    transform: translate(-50%, -50%);
    position: absolute;
}
.choose__list-item .content .title {
    margin-bottom: 8px;
    font-size: 18px;
    font-weight: 600;
    text-transform: capitalize;
}
.choose__list-item .content span {
    display: block;
    color: var(--tg-body-color);
    text-transform: capitalize;
    line-height: 1.3;
}
.choose__img {
    position: absolute;
    right: 0;
    top: -40px;
    z-index: 2;
    max-width: 765px;
}
@media (max-width: 1500px) {
    .choose__content-two > p {
        width: 85%;
    }
    .choose__content-three {
        margin-right: 130px;
    }
    .choose__content-four {
        margin-right: 60px;
    }
    .choose__img {
        max-width: 740px;
    }
}
@media (max-width: 1199.98px) {
    .choose__content-two > p {
        width: 100%;
    }
    .choose__content-three {
        margin-right: 0;
    }
    .choose__content-four {
        margin: 0;
    }
    .choose__content-four > p {
        width: 100%;
    }
    .choose__img {
        max-width: 500px;
    }
}
.choose__img img {
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
    min-height: 300px;
    object-fit: cover;
    width: 100%;
}
.choose__img .popup-video {
    width: 75px;
    height: 75px;
    display: flex;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    left: 50%;
    top: 50%;
    font-size: 20px;
    color: var(--tg-theme-secondary);
}
.choose__img-four .right__side .popup-video,
.choose__img-three .play-btn {
    align-items: center;
    left: 50%;
    top: 50%;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.choose__img .popup-video:hover,
.shop-action a:hover,
.shop-active .slick-arrow:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.choose__img-two {
    text-align: center;
    position: relative;
}
.choose__img-two img {
    border-radius: 1000px 1000px 0;
}
.choose__img-three {
    margin-right: 45px;
    position: relative;
}
.choose__img-three img {
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    -o-border-radius: 100px;
    -ms-border-radius: 100px;
    border-radius: 100px;
}
.choose__img-three .play-btn {
    width: 76px;
    height: 76px;
    display: flex;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    color: var(--tg-heading-color);
    font-size: 22px;
}
.choose__img-three .play-btn::after {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 100%;
    border: 1px solid var(--tg-common-color-white);
    border-radius: 50%;
}
.choose__img-four {
    position: relative;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: flex-end;
    gap: 25px;
    margin-bottom: 30px;
}
@media (max-width: 1199.98px) {
    .choose__img-three {
        margin-right: 0;
    }
    .choose__img-four {
        gap: 15px;
    }
}
.choose__img-four .left__side {
    width: 320px;
    flex: 0 0 auto;
}
@media (max-width: 1500px) {
    .choose__img-four .left__side {
        width: 290px;
    }
}
@media (max-width: 1199.98px) {
    .choose__img-four .left__side {
        width: 220px;
    }
}
.choose__img-four .left__side img {
    border-radius: 30px;
    margin-bottom: 25px;
    width: 100%;
}
.choose__img-four .left__side img:nth-child(2),
.testimonial__content p {
    margin-bottom: 0;
}
.choose__img-four .right__side {
    position: relative;
    flex-grow: 1;
    margin-bottom: -30px;
}
.choose__img-four .right__side img {
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
    width: 100%;
}
.choose__img-four .right__side .popup-video {
    width: 76px;
    height: 76px;
    display: flex;
    justify-content: center;
    background: var(--tg-common-color-white);
    color: var(--tg-heading-color);
    position: absolute;
    transform: translate(-50%, -50%);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    font-size: 22px;
}
.circle__text-wrap,
.circle__text-wrap .icon {
    align-items: center;
    background: var(--tg-common-color-white);
}
.choose__img-four .right__side .popup-video::after {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 100%;
    border: 1px solid var(--tg-common-color-white);
    border-radius: 50%;
}
.categories__shape-wrap img,
.categories__shape-wrap-two img,
.choose__img-four .shape,
.choose__shape img,
.event__shape-wrap img,
.fact__shape-wrap img,
.features__shape-wrap img,
.features__shape-wrap-two img,
.instructor__shape-two img,
.testimonial__shape-wrap img,
.testimonial__shape-wrap-two img {
    position: absolute;
    z-index: -1;
}
.choose__img-four .shape.shape-one {
    right: 34%;
    top: 8px;
    max-width: 106px;
}
.choose__img-four .shape.shape-two {
    left: -5%;
    bottom: -6%;
    max-width: 142px;
}
.choose__img-four .shape.shape-three {
    left: 25%;
    bottom: -13%;
    max-width: 335px;
}
.choose__shape img:first-child {
    right: 10%;
    bottom: 24%;
}
@media (max-width: 1800px) {
    .choose__shape img:first-child {
        right: 3%;
    }
}
@media (max-width: 1199.98px) {
    .choose__img-four .shape.shape-one {
        right: 30%;
        top: 37px;
        max-width: 80px;
    }
    .choose__img-four .shape.shape-three {
        left: 18%;
        bottom: -22%;
        max-width: 280px;
    }
    .choose__shape img:first-child {
        right: 2%;
        bottom: 45%;
    }
}
@media (max-width: 991.98px) {
    .choose__content-three {
        margin: 0;
    }
    .choose__img {
        position: relative;
        top: 0;
        margin-bottom: 50px;
        max-width: 100%;
    }
    .choose__img-three,
    .choose__img-two {
        margin-bottom: 50px;
    }
    .choose__img-four {
        gap: 20px;
        margin-bottom: 80px;
    }
    .choose__img-four .left__side {
        width: 270px;
    }
    .choose__img-four .shape.shape-three {
        display: none;
    }
    .choose__shape img:first-child {
        right: 3%;
        bottom: 8%;
        max-width: 120px;
    }
}
.choose__shape img:nth-child(2) {
    right: 9%;
    bottom: 19%;
    z-index: -2;
}
@media (max-width: 1800px) {
    .choose__shape img:nth-child(2) {
        right: 1%;
    }
}
@media (max-width: 1199.98px) {
    .choose__shape img:nth-child(2) {
        right: 0;
        bottom: 40%;
    }
}
@media (max-width: 991.98px) {
    .choose__shape img:nth-child(2) {
        right: 0;
        bottom: 6%;
        width: 130px;
    }
}
@media (max-width: 767.98px) {
    .choose__img-three img {
        -webkit-border-radius: 40px;
        -moz-border-radius: 40px;
        -o-border-radius: 40px;
        -ms-border-radius: 40px;
        border-radius: 40px;
        width: 100%;
    }
    .choose__img-four .left__side,
    .choose__img-four .shape {
        display: none;
    }
    .choose__shape img:first-child {
        bottom: 6%;
        max-width: 100px;
    }
    .choose__shape img:nth-child(2) {
        right: -5%;
        bottom: 4%;
        width: 100px;
    }
}
.section-pt-140 {
    padding-top: 140px;
}
.section-pb-140 {
    padding-bottom: 140px;
}
.section-pb-110 {
    padding-bottom: 110px;
}
.section-py-140 {
    padding: 140px 0;
}
.circle__text-wrap {
    width: 160px;
    height: 160px;
    display: flex;
    justify-content: center;
    border: 1px solid #d2d2d2;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    position: absolute;
    right: 10%;
    top: 30px;
}
@media (max-width: 1500px) {
    .circle__text-wrap {
        right: 6%;
        top: 20px;
    }
}
@media (max-width: 767.98px) {
    .section-pt-140 {
        padding-top: 100px;
    }
    .section-pb-140 {
        padding-bottom: 100px;
    }
    .section-pb-110 {
        padding-bottom: 70px;
    }
    .section-py-140 {
        padding: 100px 0;
    }
    .circle__text-wrap {
        display: none;
    }
}
.circle__text-wrap .icon {
    width: 90px;
    height: 90px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    justify-content: center;
    color: var(--tg-theme-secondary);
    line-height: 0;
    font-size: 36px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    border: 1px solid #d2d2d2;
}
.categories__item .icon,
.categories__item-two a {
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.circle__text-wrap .content .circle {
    position: absolute;
    margin-bottom: 0;
    font-size: 16px;
    text-transform: uppercase;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    color: var(--tg-heading-color);
    -webkit-animation-duration: 20s;
    animation-duration: 20s;
}
.circle__text-wrap .content .circle span {
    position: absolute;
    top: -71px;
    display: inline-block;
    transform-origin: 0 71px;
}
.error-area,
.section-py-120 {
    padding: 120px 0;
}
.categories__wrap {
    background: var(--tg-common-color-gray);
    -webkit-border-radius: 500px;
    -moz-border-radius: 500px;
    -o-border-radius: 500px;
    -ms-border-radius: 500px;
    border-radius: 500px;
    padding: 52px 145px 45px;
    position: relative;
}
@media (max-width: 1199.98px) {
    .circle__text-wrap {
        right: 2%;
        top: 15px;
    }
    .categories__wrap {
        padding: 52px 100px 45px;
    }
}
.categories__item {
    text-align: center;
}
.categories__item .icon {
    width: 150px;
    height: 150px;
    display: flex;
    justify-content: center;
    border: 1px solid var(--tg-border-1);
    border-radius: 150px;
    background: linear-gradient(
        180deg,
        var(--tg-common-color-white) 0,
        #f1f1fa 100%
    );
    font-size: 55px;
    color: #7f7e97;
    transition: 0.3s ease-out;
    position: relative;
    margin: 0 0 12px;
}
.categories__item .icon::before {
    content: "";
    position: absolute;
    top: 26px;
    right: 26px;
    background-image: url(../img/icons/cat_star.svg);
    background-repeat: no-repeat;
    background-size: contain;
    width: 30px;
    height: 26px;
    pointer-events: none;
}
.categories__item .name {
    display: block;
    font-size: 18px;
    font-weight: var(--tg-fw-medium);
    line-height: 1.5;
    color: var(--tg-heading-color);
    margin: 0 0 7px;
}
.categories__item .courses,
.categories__item-two a .content .name {
    color: var(--tg-body-color);
    font-family: var(--tg-heading-font-family);
}
.categories__item .courses {
    display: block;
    line-height: 1.3;
}
.testimonial__item-two::before,
.testimonial__item::before {
    font-family: var(--tg-icon-font-family);
    font-weight: 700;
    content: "\f10e";
}
.categories__item:hover .icon {
    background: var(--tg-theme-primary);
    box-shadow: 6px 5px 0 0 rgba(0, 0, 0, 0.25);
    color: var(--tg-common-color-white);
}
.categories__item-three,
.categories__item-two {
    margin-bottom: 30px;
}
.categories__item-two a {
    background: #f9f9f9;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -o-border-radius: 8px;
    -ms-border-radius: 8px;
    border-radius: 8px;
    display: flex;
    padding: 15px 30px 15px 15px;
    gap: 15px;
    justify-content: space-between;
}
.categories__item-three a .icon,
.categories__item-two a .content img {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
}
.categories__item-two a .content {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 13px;
}
.categories__item-three a .icon,
.testimonial__img-icon {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.categories__item-two a .content img {
    width: 50px;
    height: 50px;
    flex: 0 0 auto;
    border-radius: 50%;
}
.categories__item-two a .content .name {
    font-size: 14px;
    line-height: 1.4;
}
.categories__item-two a .content .name strong {
    display: block;
    color: var(--tg-heading-color);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
    transition: 0.3s ease-out;
}
.categories__item-three a .icon,
.categories__item-two a .icon {
    color: var(--tg-body-color);
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.categories__item-two a .icon {
    font-size: 22px;
    font-weight: 700;
    transition: 0.3s ease-out;
}
.categories__item-two a:hover {
    transform: translateY(-3px);
}
.categories__item-two a:hover .icon,
.categories__item-two a:hover .name strong {
    color: var(--tg-theme-primary);
}
.categories__item-three a {
    background: var(--tg-common-color-white);
    box-shadow: 0 0 50px 0 rgba(0, 0, 0, 0.05);
    border-radius: 30px;
    display: block;
    text-align: center;
    padding: 65px 30px 70px;
}
.categories__item-three a .icon {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #efeff3;
    border-radius: 50%;
    font-size: 50px;
    margin: 0 auto 22px;
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: 0.3s ease-out;
}
.categories__item-four a .name,
.categories__item-three a .icon::before {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.categories__item-three a .icon::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    transform: scale(0);
    background: var(--tg-theme-secondary);
    z-index: -1;
    transition: 0.3s ease-out;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.categories__item-three a .name {
    display: block;
    font-weight: 600;
    font-size: 20px;
    text-transform: capitalize;
    color: var(--tg-heading-color);
    line-height: 1.2;
    font-family: var(--tg-heading-font-family);
}
.categories__item-three a .courses {
    font-weight: 500;
    font-size: 14px;
    color: var(--tg-body-color);
    font-family: var(--tg-heading-font-family);
    line-height: 1.2;
}
.categories__item-three a:hover .icon {
    color: var(--tg-heading-color);
}
.categories__item-three a:hover .icon::before {
    transform: scale(1);
}
.categories__item-four {
    margin-bottom: 30px;
    text-align: center;
}
.categories__item-four a {
    position: relative;
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    -o-border-radius: 12px;
    -ms-border-radius: 12px;
    border-radius: 12px;
    overflow: hidden;
    display: block;
}
.categories__item-four a img {
    width: 100%;
    height: 230px;
    object-fit: cover;
}
.categories__item-four a .name {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    border-radius: 10px 10px 0 0;
    background: var(--tg-common-color-white);
    font-weight: 600;
    font-size: 16px;
    font-family: var(--tg-heading-font-family);
    color: var(--tg-heading-color);
    text-transform: capitalize;
    display: block;
    line-height: 1;
    padding: 14px 19px;
    transition: 0.3s ease-out;
    white-space: nowrap;
}
.categories__item-four a .name strong {
    font-weight: 400;
    font-size: 14px;
    color: var(--tg-body-color);
}
.categories__item-four a:hover .name,
.view-all-categories a:hover {
    color: var(--tg-theme-primary);
}
.categories__nav button {
    position: absolute;
    left: 50px;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: var(--tg-theme-secondary);
    padding: 0;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    border: 1px solid #333;
    box-shadow: -3px 2px 0 0 #382900;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    transition: 0.3s ease-out;
}
.plyr--video .plyr__control.plyr__tab-focus,
.plyr--video .plyr__control:hover,
.plyr--video .plyr__control[aria-expanded="true"],
.testimonial__img-three .banner__review .icon,
.testimonial__nav button {
    background: var(--tg-theme-primary);
}
.categories__nav button.categories-button-next {
    left: auto;
    right: 50px;
    box-shadow: 2px 4px 0 0 #382900;
}
.categories__nav button:hover {
    box-shadow: none;
}
.categories__shape-wrap img:first-child {
    left: 9%;
    top: 33%;
    max-width: 153px;
    -webkit-animation-duration: 30s;
    animation-duration: 30s;
}
.categories__shape-wrap img:nth-child(2) {
    right: 0;
    top: 0;
    max-width: 436px;
}
.categories__shape-wrap img:nth-child(3) {
    right: 11%;
    top: 37%;
    max-width: 83px;
}
.categories__shape-wrap img:nth-child(4) {
    left: 0;
    bottom: 0;
    max-width: 456px;
}
.categories__shape-wrap-two img:first-child {
    left: 4%;
    top: 14%;
    max-width: 147px;
}
@media (max-width: 1800px) {
    .categories__shape-wrap-two img:first-child {
        left: 2%;
        top: 16%;
        max-width: 125px;
    }
}
.categories__shape-wrap-two img:nth-child(2) {
    right: 3%;
    bottom: 10%;
    max-width: 107px;
}
@media (max-width: 1800px) {
    .categories__shape-wrap-two img:nth-child(2) {
        right: 2%;
        bottom: 4%;
        max-width: 80px;
    }
}
.view-all-categories {
    text-align: right;
    margin-bottom: 30px;
}
.view-all-categories a {
    display: inline-flex;
    align-items: center;
    color: var(--tg-body-color);
    gap: 10px;
    font-size: 15px;
    font-family: var(--tg-heading-font-family);
    font-weight: 500;
}
.view-all-categories a svg {
    width: 15px;
}
.section-pb-57 {
    padding-bottom: 57px;
}
.testimonial__area-four {
    position: relative;
    z-index: 1;
    background: var(--tg-common-color-gray-8);
    padding: 70px 0 0;
    margin: 22px 0 0;
}
.testimonial__bg-three,
.testimonial__bg-two {
    background-size: cover;
    background-position: center;
    position: relative;
    z-index: 1;
    overflow: hidden;
}
.testimonial__bg-shape-one {
    position: absolute;
    left: 0;
    top: -21px;
    width: 100%;
    height: 22px;
    line-height: 1;
    z-index: 1;
}
.testimonial__bg-shape-one svg {
    width: 100%;
    color: var(--tg-common-color-gray-8);
    height: 22px;
}
.testimonial__bg-shape-two {
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 100%;
    height: 22px;
    line-height: 1;
    z-index: 1;
}
.testimonial__bg-shape-two svg {
    width: 100%;
    height: 22px;
    color: var(--tg-common-color-white);
}
.cta__img,
.newsletter__img-wrap,
.testimonial__img {
    position: relative;
    text-align: center;
}
.testimonial__img img {
    -webkit-border-radius: 1000px;
    -moz-border-radius: 1000px;
    -o-border-radius: 1000px;
    -ms-border-radius: 1000px;
    border-radius: 1000px;
}
.cta__bg-three,
.testimonial__img-three .banner__review {
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
}
.testimonial__img .shape img {
    position: absolute;
    left: -10px;
    top: 0;
    z-index: -1;
    -webkit-animation-duration: 80s;
    animation-duration: 80s;
    max-width: 413px;
}
.testimonial__img-two {
    text-align: center;
    position: relative;
}
.testimonial__img-two .shape img {
    position: absolute;
    z-index: 2;
}
.testimonial__img-two .shape img:first-child {
    left: 40px;
    top: 5%;
    max-width: 84px;
}
@media (max-width: 1199.98px) {
    .categories__item-three a {
        padding: 45px 30px 50px;
    }
    .categories__nav button {
        left: 20px;
    }
    .categories__nav button.categories-button-next {
        right: 20px;
    }
    .categories__shape-wrap img:nth-child(2),
    .categories__shape-wrap img:nth-child(4) {
        max-width: 300px;
    }
    .testimonial__img .shape img {
        left: -50px;
        max-width: 360px;
    }
    .testimonial__img-two .shape img:first-child {
        left: 0;
        max-width: 70px;
    }
}
.testimonial__img-two .shape img:nth-child(2) {
    left: 8%;
    top: 34%;
    max-width: 103px;
}
.testimonial__img-two .shape img:nth-child(3) {
    right: 10%;
    bottom: 13%;
    max-width: 129px;
}
.testimonial__img-three {
    position: relative;
    margin-top: 40px;
}
@media (max-width: 991.98px) {
    .categories__wrap {
        padding: 45px 90px;
    }
    .categories__shape-wrap-two img:first-child {
        top: 11%;
        max-width: 110px;
    }
    .categories__shape-wrap-two img:nth-child(2) {
        bottom: 2%;
        max-width: 70px;
    }
    .testimonial__img {
        margin-bottom: 50px;
    }
    .testimonial__img-two {
        margin-top: 50px;
    }
    .testimonial__img-two .shape img:first-child {
        left: 40px;
    }
    .testimonial__img-three {
        margin-bottom: 50px;
    }
    .testimonial__item-four {
        text-align: center;
    }
}
.testimonial__img-three .banner__review {
    border-radius: 15px;
    left: -6%;
    bottom: 11%;
    right: auto;
    top: auto;
}
.testimonial__img-four .banner__review .icon,
.testimonial__img-icon,
.testimonial__nav-two button:hover {
    background: var(--tg-theme-secondary);
}
.testimonial__img-three .svg-icon {
    position: absolute;
    right: 10%;
    top: -17%;
    width: 99px;
    height: auto;
}
@media (max-width: 1500px) {
    .categories__shape-wrap img:first-child {
        left: 1%;
    }
    .categories__shape-wrap img:nth-child(3) {
        right: 2%;
    }
    .testimonial__img-three .banner__review {
        left: -4%;
    }
    .testimonial__img-three .svg-icon {
        right: 2%;
        top: -16%;
    }
}
@media (max-width: 1199.98px) {
    .testimonial__img-two .shape img:nth-child(2) {
        left: 0;
        top: 35%;
        max-width: 76px;
    }
    .testimonial__img-two .shape img:nth-child(3) {
        right: 2%;
        max-width: 100px;
    }
    .testimonial__img-three .svg-icon {
        top: -19%;
    }
}
@media (max-width: 767.98px) {
    .section-py-120 {
        padding: 100px 0;
    }
    .categories__wrap {
        padding: 30px 20px;
        -webkit-border-radius: 30px;
        -moz-border-radius: 30px;
        -o-border-radius: 30px;
        -ms-border-radius: 30px;
        border-radius: 30px;
    }
    .categories__item .icon {
        width: 130px;
        height: 130px;
        font-size: 45px;
        margin: 0 auto 12px;
    }
    .categories__nav,
    .categories__shape-wrap img:first-child,
    .categories__shape-wrap img:nth-child(3),
    .categories__shape-wrap-two img:first-child,
    .categories__shape-wrap-two img:nth-child(2),
    .testimonial__img .shape img {
        display: none;
    }
    .categories__shape-wrap img:nth-child(2),
    .categories__shape-wrap img:nth-child(4) {
        max-width: 200px;
    }
    .view-all-categories {
        text-align: left;
        margin-bottom: 40px;
    }
    .mb-xs-30 {
        margin-bottom: 30px;
    }
    .mb-xs-20 {
        margin-bottom: 20px;
    }
    .testimonial__img-three .svg-icon {
        top: -27%;
    }
}
.error-img svg,
.testimonial__img-three .svg-icon svg {
    width: 100%;
    height: 100%;
    color: var(--tg-theme-secondary);
}
.testimonial__img-four .svg-icon {
    position: absolute;
    right: auto;
    left: 42%;
    top: -11%;
    width: 81px;
    height: auto;
}
@media (max-width: 1199.98px) {
    .testimonial__img-four .svg-icon {
        top: -16%;
    }
}
@media (max-width: 767.98px) {
    .testimonial__img-four .svg-icon {
        top: -20%;
    }
}
.testimonial__img-four .svg-icon svg {
    color: var(--tg-body-color);
}
.testimonial__img-four .testimonial__img-icon {
    background: var(--tg-common-color-white);
    border: 1px solid var(--tg-body-color);
    color: #d7d7df;
    position: absolute;
    right: 8%;
    bottom: auto;
    top: 16%;
}
.testimonial__img-four .banner__review {
    left: 4%;
    bottom: 14%;
}
.testimonial__img-icon {
    width: 82px;
    height: 82px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--tg-body-color);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    color: var(--tg-common-color-white);
    box-shadow: -8px 8px 0 0 rgba(0, 0, 0, 0.25);
    position: absolute;
    right: 5%;
    bottom: 29%;
}
.testimonial__item-three .rating,
.testimonial__item-top {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.testimonial__mask-img {
    -webkit-mask-image: url(../img/others/h6_testimonial_mask_img.png);
    mask-image: url(../img/others/h6_testimonial_mask_img.png);
    mask-size: 100%;
    mask-repeat: no-repeat;
    mask-position: center;
    width: 530px;
    height: 420px;
}
@media (max-width: 1500px) {
    .testimonial__img-four .testimonial__img-icon {
        right: 1%;
    }
    .testimonial__img-icon {
        right: 0;
    }
    .testimonial__mask-img {
        width: 516px;
        height: 410px;
    }
}
@media (max-width: 1199.98px) {
    .testimonial__img-four .testimonial__img-icon {
        right: -1%;
    }
    .testimonial__mask-img {
        width: 450px;
        height: 360px;
    }
}
@media (max-width: 767.98px) {
    .testimonial__mask-img {
        width: 340px;
        height: 272px;
        margin: 0 auto;
    }
}
.testimonial__mask-img img {
    max-width: unset;
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.testimonial__item {
    background: #f6f5fe;
    padding: 40px 45px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    position: relative;
    z-index: 1;
}
@media (max-width: 1199.98px) {
    .testimonial__item {
        padding: 30px 20px;
    }
}
.testimonial__item-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}
.testimonial__item-wrap {
    position: relative;
}
.testimonial__item::before {
    font-size: 48px;
    line-height: 1;
    color: #d9d6f5;
    position: absolute;
    right: 43px;
    top: 45px;
    z-index: -1;
}
.testimonial__item-two {
    background: #f9f9f9;
    padding: 50px 45px;
    border-radius: 10px;
    position: relative;
    z-index: 1;
}
.testimonial__item-two::before {
    font-size: 48px;
    line-height: 1;
    color: #e6e6e6;
    position: absolute;
    right: 45px;
    top: 43px;
    z-index: -1;
}
.cta__content p,
.instructor__content .designation,
.instructor__content-three .designation,
.instructor__details-content .designation,
.testimonial__item-four p,
.testimonial__item-three p {
    font-family: var(--tg-heading-font-family);
}
.cta__content,
.instructor__content-five,
.shop-content,
.testimonial__item-three {
    text-align: center;
}
.testimonial__item-three .icon {
    margin-bottom: 20px;
}
.testimonial__item-three .icon svg {
    color: #d9d5d0;
}
.testimonial__item-three .rating {
    display: flex;
    justify-content: center;
    gap: 5px;
    color: var(--tg-common-color-yellow-3);
    font-size: 20px;
    margin-bottom: 17px;
}
.testimonial__item-three p {
    font-size: 21px;
    line-height: 1.8;
    font-style: italic;
    color: #343c4d;
    margin-bottom: 20px;
}
.testimonial__item-four {
    position: relative;
    width: 83%;
}
.testimonial__item-four::before {
    content: "";
    position: absolute;
    background-image: url(../img/icons/quote.svg);
    width: 205px;
    height: 160px;
    z-index: -1;
    right: 0;
    top: 10%;
}
.testimonial__item-four .rating {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    font-size: 22px;
    gap: 7px;
    color: var(--tg-common-color-yellow-3);
    margin-bottom: 22px;
}
.testimonial__author,
.testimonial__author-content .rating {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.testimonial__item-four p {
    margin-bottom: 15px;
    font-style: italic;
    font-weight: 500;
    font-size: 21px;
    color: #343c4d;
    line-height: 1.7;
}
.testimonial__bottom span,
.testimonial__bottom-two span {
    line-height: 1.2;
    display: block;
    font-weight: 500;
}
.testimonial__item-five {
    background: var(--tg-common-color-white);
}
.testimonial__item-five .testimonial__content-two .title {
    margin-bottom: 25px;
}
.testimonial__item-five .testimonial__content-two .rating {
    margin-bottom: 22px;
}
.testimonial__bottom .title {
    margin-bottom: 8px;
    font-size: 18px;
    font-weight: 600;
    color: var(--tg-theme-primary);
}
.testimonial__bottom-two .title {
    margin-bottom: 5px;
    font-size: 20px;
    color: var(--tg-theme-primary);
}
.testimonial__bottom-two span {
    color: #7f838c;
}
.testimonial__author {
    display: flex;
    gap: 20px;
}
.testimonial__author-thumb img {
    border: 3px solid #fff;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.25);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.testimonial__author-thumb-two {
    width: 50px;
    height: 50px;
    flex: 0 0 auto;
}
.testimonial__author-thumb-two img {
    border: 2px solid var(--tg-common-color-white);
}
.testimonial__author-content .rating {
    display: flex;
    gap: 3px;
    font-size: 15px;
    color: var(--tg-common-color-yellow);
    margin-bottom: 10px;
}
.latest-comments .comments-text .avatar-name .name,
.testimonial__author-content .title {
    font-size: 20px;
    margin-bottom: 0;
}
.testimonial__author-content-two .title {
    margin-bottom: 3px;
}
.testimonial__author-content-two span {
    display: block;
    font-size: 14px;
    font-weight: 500;
    line-height: 1;
}
.testimonial__content-two .rating,
.testimonial__nav button,
.testimonial__nav-two {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.testimonial__author-two {
    gap: 10px;
}
.testimonial__icon {
    font-size: 48px;
    line-height: 0;
    color: #d9d6f5;
}
.testimonial__content-two .title {
    margin-bottom: 32px;
    font-size: 18px;
    text-transform: capitalize;
    font-weight: 600;
    color: var(--tg-theme-primary);
}
.testimonial__content-two .rating {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--tg-common-color-yellow);
    margin-bottom: 15px;
}
.testimonial__content-three {
    margin-left: 55px;
    margin-right: 80px;
}
.testimonial__content-three .testimonial__item-four {
    width: 100%;
}
.testimonial__content-three .testimonial__item-four::before {
    opacity: 0.5;
}
.testimonial__nav button {
    padding: 0;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    border: 2px solid var(--tg-common-color-dark);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--tg-common-color-white);
    font-size: 20px;
    line-height: 0;
    -webkit-box-shadow: 3.6px 2.4px 0 0 #23232b;
    -moz-box-shadow: 3.6px 2.4px 0 0 #23232b;
    -ms-box-shadow: 3.6px 2.4px 0 0 #23232b;
    -o-box-shadow: 3.6px 2.4px 0 0 #23232b;
    box-shadow: 3.6px 2.4px 0 0 #23232b;
    position: absolute;
    right: -75px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 3;
}
.testimonial__nav button.testimonial-button-prev {
    right: auto;
    left: -75px;
    -webkit-box-shadow: -3.6px 2.4px 0 0 #23232b;
    -moz-box-shadow: -3.6px 2.4px 0 0 #23232b;
    -ms-box-shadow: -3.6px 2.4px 0 0 #23232b;
    -o-box-shadow: -3.6px 2.4px 0 0 #23232b;
    box-shadow: -3.6px 2.4px 0 0 #23232b;
}
.instructor__nav-two .instructor-button-next i,
.instructor__nav-two .instructor-button-prev i,
.testimonial__nav button.testimonial-button-prev i {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}
.instructor__nav-two .instructor-button-next:hover,
.instructor__nav-two .instructor-button-prev:hover,
.testimonial__nav button:hover {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}
.testimonial__nav-two {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 6px;
    margin-bottom: 30px;
}
.testimonial-pagination,
.testimonial__nav-two button {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.testimonial__nav-two button {
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: var(--tg-common-color-white);
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    color: var(--tg-body-color);
    font-size: 25px;
    padding: 0;
}
.testimonial__nav-two button.testimonial-button-prev {
    transform: rotate(-180deg);
}
.testimonial__nav-two button:hover {
    color: var(--tg-heading-color);
}
.testimonial__shape img {
    position: absolute;
    right: 9%;
    top: -230px;
    z-index: -1;
    -webkit-animation-duration: 60s;
    animation-duration: 60s;
    max-width: 380px;
}
.testimonial__shape-wrap img:first-child {
    right: 12%;
    bottom: 15%;
    max-width: 86px;
}
.testimonial__shape-wrap img:nth-child(2) {
    right: 10%;
    top: 7%;
    max-width: 95px;
}
.testimonial__shape-wrap-two img:first-child {
    right: 5%;
    top: 10%;
    max-width: 110px;
}
.testimonial__shape-wrap-two img:nth-child(2) {
    left: 3%;
    bottom: -27px;
    max-width: 99px;
}
.testimonial-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    gap: 10px;
    margin-top: 25px;
}
.testimonial-pagination .swiper-pagination-bullet {
    width: 15px;
    height: 15px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    margin: 0 !important;
    background: #d9d9d9;
    transition: 0.3s ease-in-out;
    display: block;
    opacity: 1;
}
.instructor__social-two .list-wrap,
.instructor__thumb-two .shape-one {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    position: absolute;
    bottom: 0;
}
.testimonial-pagination .swiper-pagination-bullet-active {
    background: #161439;
}
.testimonial-pagination-two {
    justify-content: flex-start;
    margin-top: 34px;
}
.instructor__item,
.instructor__item-two {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.section-pb-130 {
    padding-bottom: 130px;
}
.instructor__area {
    padding: 120px 0 85px;
}
.instructor__area-two {
    background: var(--tg-common-color-blue-2);
}
.instructor__area-four {
    background: var(--tg-common-color-gray);
}
.instructor__area-six {
    position: relative;
    z-index: 1;
}
.instructor__bg {
    background-size: cover;
    background-position: center;
    padding: 265px 0 120px;
    margin-top: -145px;
    position: relative;
    z-index: 1;
}
.instructor__content-wrap {
    width: 87%;
}
.instructor__content-two p,
.instructor__content-wrap p,
.progress-item {
    margin-bottom: 20px;
}
.instructor__item {
    display: flex;
    gap: 15px;
    margin-bottom: 35px;
}
.instructor__item-two {
    display: flex;
    gap: 30px;
    border: 1px solid #bdbdbd;
    background: var(--tg-common-color-white);
    -webkit-border-radius: 16px;
    -moz-border-radius: 16px;
    -o-border-radius: 16px;
    -ms-border-radius: 16px;
    border-radius: 16px;
    padding: 0 60px 0 25px;
    position: relative;
    z-index: 3;
    overflow: hidden;
    min-height: 260px;
    margin-bottom: 30px;
}
.instructor__item-two:hover .instructor__thumb-two .shape-one {
    color: var(--tg-theme-primary);
}
.instructor__item-three {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    margin-bottom: 40px;
}
.instructor__item-four {
    margin-bottom: 30px;
    text-align: center;
}
.instructor__item-four:hover .instructor__thumb-four {
    transform: translateY(-5px);
}
.event__content,
.event__widget,
.instructor__item-five,
.shop-top-wrap {
    margin-bottom: 30px;
}
.instructor__item-five:hover .instructor__social-two .list-wrap,
.shop-item:hover .shop-action li {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}
.instructor__thumb {
    position: relative;
    width: 186px;
    flex: 0 0 auto;
}
.instructor__thumb::before {
    content: "";
    position: absolute;
    width: 180px;
    height: 180px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    bottom: 0;
    background: linear-gradient(156deg, #f7f6f9 10.62%, #e9f5f5 90.16%);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    z-index: -1;
}
.instructor__thumb-two {
    width: auto;
    flex: 0 0 auto;
    margin-top: auto;
}
.instructor__content .avg-rating,
.instructor__thumb-three {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.instructor__thumb-two .shape-one {
    left: 0;
    z-index: -1;
    color: var(--tg-theme-secondary);
    transition: 0.3s ease-out;
}
.instructor__thumb-two .shape-two {
    position: absolute;
    left: 34px;
    top: 5px;
    z-index: -1;
    width: 40px;
    height: 48px;
}
.instructor__thumb-two .shape-two .svg-icon {
    width: 100%;
    height: 100%;
    display: block;
}
.instructor__thumb-two .shape-two .svg-icon path {
    stroke: var(--tg-theme-secondary);
}
.instructor__thumb-three {
    width: 50%;
    flex: 0 0 auto;
    text-align: center;
    position: relative;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    min-height: 453px;
}
.instructor__thumb-three .shape-one {
    position: absolute;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    bottom: 0;
    z-index: -1;
    color: var(--tg-theme-secondary);
}
@media (max-width: 991.98px) {
    .testimonial__item-four::before {
        display: none;
    }
    .testimonial-pagination-two,
    .testimonial__item-four .rating {
        justify-content: center;
    }
    .testimonial__content-three {
        text-align: center;
    }
    .testimonial__shape-wrap img:nth-child(2) {
        right: 8%;
        top: 4%;
    }
    .instructor__item {
        justify-content: center;
        display: block;
        text-align: center;
    }
    .instructor__item-two {
        padding: 0 35px 0 25px;
    }
    .instructor__thumb {
        margin: 0 auto 20px;
    }
    .instructor__thumb-three .shape-one {
        width: 100%;
    }
    .instructor__thumb-three .shape-one svg {
        width: 100%;
        height: 100%;
    }
}
.instructor__thumb-four {
    overflow: hidden;
    -webkit-border-radius: 1000px;
    -moz-border-radius: 1000px;
    -o-border-radius: 1000px;
    -ms-border-radius: 1000px;
    border-radius: 1000px;
    margin-bottom: 25px;
    transition: 0.3s ease-in-out;
}
.instructor__thumb-five {
    position: relative;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
    overflow: hidden;
    margin-bottom: 20px;
}
.instructor__thumb-five img {
    width: 100%;
}
.instructor__content .title {
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: 600;
}
.instructor__content .designation {
    display: block;
    color: var(--tg-theme-primary);
    line-height: 1.2;
    margin-bottom: 12px;
}
.instructor__content .avg-rating {
    display: flex;
    align-items: center;
    font-size: 15px;
    color: var(--tg-common-color-gray-3);
    gap: 5px;
    margin-bottom: 15px;
    line-height: 1.2;
}
.instructor__content-three .ratting,
.instructor__social .list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.instructor__content .avg-rating i,
.instructor__content-three .ratting {
    color: var(--tg-common-color-yellow);
}
.instructor__content-two .title {
    margin-bottom: 5px;
    font-size: 28px;
}
.instructor__content-three {
    margin-left: 40px;
    margin-top: 40px;
}
.instructor__content-three .ratting {
    display: flex;
    align-items: center;
    gap: 3px;
    line-height: 1;
    font-size: 15px;
}
.instructor__content-three .ratting-wrap {
    display: inline-flex;
    align-items: center;
    border: 1px solid var(--tg-common-color-gray-3);
    -webkit-border-radius: 35px;
    -moz-border-radius: 35px;
    -o-border-radius: 35px;
    -ms-border-radius: 35px;
    border-radius: 35px;
    background: var(--tg-common-color-white);
    padding: 7px 10px;
    gap: 8px;
    margin-bottom: 20px;
}
.instructor__content-three .ratting-wrap span {
    font-size: 14px;
    color: var(--tg-common-color-gray-3);
    line-height: 1;
}
.instructor__content-three .title {
    margin-bottom: 10px;
    font-size: 24px;
}
.instructor__content-three .designation {
    display: block;
    line-height: 1;
    font-size: 18px;
    font-weight: 400;
    color: var(--tg-theme-primary);
    margin-bottom: 15px;
}
.instructor__content-three p {
    margin-bottom: 22px;
    width: 80%;
}
.instructor__content-three .instructor__social,
.shop-details-content p {
    margin-bottom: 35px;
}
.instructor__content-three .instructor__social .list-wrap li a {
    width: 50px;
    height: 50px;
    font-size: 20px;
}
.instructor__content-four .title {
    margin-bottom: 8px;
    font-size: 24px;
    font-weight: 600;
    text-transform: capitalize;
}
.instructor__content-four span {
    display: block;
    line-height: 1.2;
}
.instructor__content-five .title {
    margin-bottom: 6px;
    font-weight: 700;
    font-size: 24px;
    text-transform: capitalize;
}
.instructor__content-five span {
    display: block;
    line-height: 1;
    color: var(--tg-body-color);
    text-transform: capitalize;
}
.instructor__social .list-wrap {
    display: flex;
    align-items: center;
    gap: 8px;
}
.instructor-nav .swiper-wrapper button,
.instructor__social .list-wrap li a,
.instructor__social-two .list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.instructor__social .list-wrap li a {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0.835px solid #9292b4;
    background: var(--tg-common-color-white);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    box-shadow: 2.50435px 3.33913px 0 0 rgba(0, 0, 0, 0.25);
    font-size: 18px;
    color: var(--tg-common-color-gray-3);
}
.instructor__social-two .list-wrap,
.progress-item .progress {
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
}
.instructor__social .list-wrap li a:hover {
    background: var(--tg-theme-primary);
    box-shadow: none;
    border-color: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.instructor__details-content > .list-wrap > li a:hover,
.instructor__social-two .list-wrap li a:hover {
    color: var(--tg-theme-secondary);
}
.instructor__social-two .list-wrap {
    display: flex;
    align-items: center;
    gap: 14px;
    background: var(--tg-theme-primary);
    border-radius: 30px;
    left: 0;
    right: 0;
    justify-content: center;
    padding: 22px 10px;
    transition: 0.3s ease-out;
    transform: translateY(100px);
    visibility: hidden;
    opacity: 0;
}
.features__item-two,
.instructor__nav-two .instructor-button-next,
.instructor__nav-two .instructor-button-prev {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.instructor__social-two .list-wrap li {
    line-height: 1;
}
.instructor__social-two .list-wrap li a {
    font-size: 22px;
    color: var(--tg-common-color-white);
}
.instructor-nav .swiper-wrapper {
    display: inline-flex;
    align-items: center;
    gap: 30px;
}
.instructor-nav .swiper-wrapper button {
    border: 2px solid #ababab;
    background: #d9d9d9;
    padding: 0;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    position: relative;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    filter: grayscale(1);
    line-height: 1;
}
.instructor-nav .swiper-wrapper .swiper-slide {
    width: auto !important;
}
.instructor-nav .swiper-slide-thumb-active button {
    filter: grayscale(0);
    background: var(--tg-common-color-white);
    border-color: var(--tg-theme-primary);
}
.instructor-slider-dot {
    text-align: center;
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    -o-border-radius: 100px;
    -ms-border-radius: 100px;
    border-radius: 100px;
    border: 1px solid #d0d0d0;
    background: var(--tg-common-color-white);
    padding: 40px 120px;
    margin: 0 25px;
    position: relative;
    height: 190px;
}
@media (max-width: 1199.98px) {
    .testimonial__item::before {
        top: 30px;
        right: 20px;
        font-size: 45px;
    }
    .testimonial__item-two {
        padding: 30px 25px;
    }
    .testimonial__item-two::before {
        top: 30px;
        right: 20px;
        font-size: 45px;
    }
    .testimonial__item-four {
        width: 100%;
    }
    .testimonial__content-three {
        margin: 0;
    }
    .testimonial__shape img {
        max-width: 350px;
    }
    .testimonial__shape-wrap img:first-child {
        right: 10%;
        bottom: 11%;
        max-width: 75px;
    }
    .instructor__content-wrap {
        width: 100%;
        margin-bottom: 50px;
    }
    .instructor__content-two .title {
        font-size: 26px;
    }
    .instructor__content-four .title {
        font-size: 22px;
    }
    .instructor-slider-dot {
        padding: 40px 85px;
    }
}
@media (max-width: 767.98px) {
    .testimonial__item-two {
        padding: 30px;
    }
    .testimonial__item-three p {
        font-size: 20px;
    }
    .testimonial__item-four p {
        font-size: 19px;
    }
    .testimonial__author {
        gap: 15px;
    }
    .testimonial__nav {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        margin-top: 30px;
    }
    .testimonial__nav button {
        position: inherit;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0);
    }
    .testimonial__nav-two {
        justify-content: flex-start;
        margin-bottom: 50px;
    }
    .testimonial__shape img {
        max-width: 250px;
        top: -167px;
        right: 2%;
    }
    .instructor__nav-two,
    .instructor__thumb-two,
    .testimonial__shape-wrap img:first-child {
        display: none;
    }
    .testimonial__shape-wrap img:nth-child(2) {
        right: 3%;
        top: 2%;
    }
    .section-pb-130 {
        padding-bottom: 100px;
    }
    .instructor__area {
        padding: 100px 0 65px;
    }
    .instructor__item-two {
        min-height: auto;
        padding: 30px;
    }
    .instructor__item-three {
        flex-wrap: wrap;
    }
    .instructor__thumb-four img,
    .instructor__thumb-three {
        width: 100%;
    }
    .instructor__content-two .title {
        font-size: 24px;
    }
    .instructor__content-three {
        margin-left: 0;
    }
    .instructor-slider-dot {
        padding: 40px 75px;
        -webkit-border-radius: 30px;
        -moz-border-radius: 30px;
        -o-border-radius: 30px;
        -ms-border-radius: 30px;
        border-radius: 30px;
    }
}
.instructor__nav-two .instructor-button-next,
.instructor__nav-two .instructor-button-prev {
    width: 50px;
    height: 50px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-theme-secondary);
    border: 2px solid #333;
    position: absolute;
    left: 40px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    font-size: 18px;
    color: var(--tg-common-color-black);
    -webkit-box-shadow: 2px 4px 0 0 #382900;
    -moz-box-shadow: 2px 4px 0 0 #382900;
    -ms-box-shadow: 2px 4px 0 0 #382900;
    -o-box-shadow: 2px 4px 0 0 #382900;
    box-shadow: 2px 4px 0 0 #382900;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    transition: 0.3s ease-out;
}
.instructor__details-content > .list-wrap,
.instructor__details-info {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.cta__content .title,
.cta__content p,
.instructor__details-content .title,
.instructor__details-content p,
.instructor__details-nav button {
    color: var(--tg-common-color-white);
}
.instructor__details-biography,
.instructor__details-info {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    margin-bottom: 30px;
}
@media (max-width: 1199.98px) {
    .instructor__nav-two .instructor-button-next,
    .instructor__nav-two .instructor-button-prev {
        left: 20px;
    }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
    .container,
    .custom-container {
        max-width: 540px;
    }
    .breadcrumb__content .title,
    .section__title .title {
        font-size: 36px;
    }
    .section__title .title span:not(.svg-icon) > svg {
        top: 0;
    }
    .banner__content .title,
    .banner__content-seven .title,
    .banner__content-two .title {
        font-size: 40px;
    }
    .banner__content-five .title,
    .banner__content-three .title,
    .slider__content .title {
        font-size: 42px;
    }
    .banner__content-four .title {
        font-size: 46px;
    }
    .banner__content-six .title,
    .choose__content-two .title-two {
        font-size: 38px;
    }
    .banner__images {
        max-width: 90%;
    }
    .banner__images-four .shape.big-shape {
        left: 18%;
    }
    .banner__images-four .shape.big-shape img {
        max-width: 285px;
    }
    .banner__images-four .shape.big-shape-two img {
        max-width: 530px;
    }
    .banner__images-six .main-img .svg-icon {
        left: -9%;
        top: -7%;
    }
    .about__images-five .shape img:first-child {
        right: 5%;
    }
    .about__images-five .shape img:nth-child(2) {
        left: 6%;
        bottom: 3%;
    }
    .about__images-five .shape img:nth-child(3) {
        bottom: -6%;
        right: 11%;
    }
    .about__mask-img-one {
        width: 450px;
        height: 420px;
        margin: 0 auto;
    }
    .courses__nav .nav {
        padding: 0 0 15px;
    }
    .courses__nav .nav .nav-item .nav-link::after {
        bottom: -15px;
    }
    .courses__item-thumb-three img {
        height: 270px;
    }
    .courses__item-thumb-four img {
        height: 320px;
    }
    .courses-top-left p {
        text-align: center;
        margin-bottom: 15px;
    }
    .choose__content-two .shape img {
        bottom: 40%;
    }
    .choose__content-inner-img {
        margin-bottom: 0;
    }
    .choose__shape img:first-child {
        right: 6%;
    }
    .choose__shape img:nth-child(2) {
        right: 0;
    }
    .circle__text-wrap {
        display: flex;
    }
    .testimonial__img-three .svg-icon {
        top: -18%;
        right: 3%;
    }
    .testimonial__img-four .svg-icon {
        top: -14%;
    }
    .testimonial__mask-img {
        width: 450px;
        height: 360px;
    }
    .testimonial__item {
        padding: 30px;
    }
    .instructor__thumb-three .shape-one {
        width: 400px;
    }
    .instructor__nav-two {
        display: block;
    }
    .instructor__nav-two .instructor-button-next,
    .instructor__nav-two .instructor-button-prev {
        right: 15px;
    }
}
.instructor__nav-two .instructor-button-next {
    left: auto;
    right: 40px;
}
@media (max-width: 1199.98px) {
    .instructor__nav-two .instructor-button-next {
        right: 20px;
    }
}
.instructor__nav-two .instructor-button-next i {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
}
.instructor__shape img {
    position: absolute;
    right: 24%;
    top: 40%;
    z-index: -1;
}
@media (max-width: 1500px) {
    .testimonial__nav button {
        right: -20px;
    }
    .testimonial__nav button.testimonial-button-prev {
        left: -20px;
    }
    .testimonial__shape-wrap-two img:first-child {
        top: 6%;
        max-width: 80px;
    }
    .testimonial__shape-wrap-two img:nth-child(2) {
        max-width: 80px;
    }
    .instructor-slider-dot {
        margin: 0;
    }
    .instructor__shape img {
        right: 17%;
    }
}
@media (max-width: 1199.98px) {
    .instructor__shape img {
        right: 10%;
    }
}
.instructor__shape-two img:first-child {
    top: 4%;
    left: 5%;
    max-width: 65px;
    -webkit-animation-duration: 30s;
    animation-duration: 30s;
}
.instructor__shape-two img:nth-child(2) {
    right: 3%;
    bottom: 27%;
    max-width: 104px;
}
@media (max-width: 1800px) {
    .instructor__shape-two img:nth-child(2) {
        bottom: 10%;
    }
}
.instructor__details-info {
    background: var(--tg-common-color-dark);
    border-radius: 10px;
    padding: 50px;
    display: flex;
    gap: 45px;
}
.instructor__details-content > .list-wrap > li::before,
.instructor__details-thumb img {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
}
@media (max-width: 991.98px) {
    .instructor__content .avg-rating {
        justify-content: center;
    }
    .instructor__content-three p {
        width: 100%;
    }
    .instructor__content-three .instructor__social .list-wrap {
        justify-content: flex-start;
    }
    .instructor__social .list-wrap {
        justify-content: center;
    }
    .instructor__shape img {
        right: 4%;
        width: 100px;
        top: 32%;
    }
    .instructor__shape-two img:first-child {
        top: 1%;
        left: 3%;
        max-width: 55px;
    }
    .instructor__shape-two img:nth-child(2) {
        bottom: 5%;
        max-width: 85px;
    }
    .instructor__details-info {
        padding: 35px 30px;
        gap: 25px;
    }
}
@media (max-width: 767.98px) {
    .instructor__shape-two img:first-child {
        top: 0;
        max-width: 45px;
    }
    .instructor__shape-two img:nth-child(2) {
        bottom: 3%;
        max-width: 70px;
    }
    .instructor__details-info {
        padding: 25px;
        flex-direction: column;
    }
}
.instructor__details-thumb {
    width: 250px;
    flex: 0 0 auto;
}
.instructor__details-thumb img {
    border-radius: 50%;
}
.instructor__details-content .title {
    margin-bottom: 8px;
    font-size: 24px;
}
.instructor__details-content .designation {
    display: block;
    font-size: 16px;
    color: var(--tg-common-color-white);
    line-height: 1;
    margin-bottom: 22px;
}
.instructor__details-content > .list-wrap {
    display: flex;
    gap: 15px 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}
.instructor__details-Skill .title,
.instructor__details-biography .title {
    margin-bottom: 14px;
    font-size: 24px;
}
.instructor__details-content > .list-wrap > li,
.instructor__details-social .list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.instructor__details-content > .list-wrap > li {
    color: #efeefe;
    display: flex;
    gap: 8px;
    line-height: 1;
    position: relative;
}
.instructor__details-content > .list-wrap > li::before {
    content: "";
    position: absolute;
    right: -17px;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background: var(--tg-common-color-white);
}
.instructor__details-content > .list-wrap > li i {
    font-size: 20px;
    line-height: 1;
}
.instructor__details-content > .list-wrap > li a {
    color: #efeefe;
}
.instructor__details-content > .list-wrap > li.avg-rating {
    font-size: 14px;
    gap: 5px;
}
.instructor__details-content > .list-wrap > li.avg-rating i {
    color: var(--tg-common-color-yellow);
    font-size: 14px;
}
.instructor__details-content p {
    margin-bottom: 25px;
}
.instructor__details-social .list-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.instructor__details-nav button,
.instructor__details-social .list-wrap li a {
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.instructor__details-social .list-wrap li a {
    width: 42px;
    height: 42px;
    display: flex;
    justify-content: center;
    background: var(--tg-common-color-white);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.instructor__details-social .list-wrap li a:hover {
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
}
.instructor__details-biography {
    border-radius: 10px;
    border: 1px solid #dfdfdf;
    background: var(--tg-common-color-white);
    box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    padding: 35px 40px;
}
.instructor__details-biography p {
    margin-bottom: 8px;
}
.event__details-content p,
.instructor__details-biography p:last-child,
.instructor__details-courses p {
    margin-bottom: 0;
}
.instructor__details-Skill {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    border: 1px solid #dfdfdf;
    background: var(--tg-common-color-white);
    -webkit-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    -moz-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    -ms-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    -o-box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.08);
    padding: 35px 40px 25px;
    margin-bottom: 45px;
}
.instructor__details-Skill p {
    margin-bottom: 18px;
}
.instructor__details-courses .main-title {
    margin-bottom: 5px;
    font-size: 30px;
}
.instructor__details-nav button {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    border: 2px solid var(--tg-common-color-dark);
    background: var(--tg-theme-primary);
    font-size: 20px;
    -webkit-box-shadow: 3.6px 2.4px 0 0 #23232b;
    -moz-box-shadow: 3.6px 2.4px 0 0 #23232b;
    -ms-box-shadow: 3.6px 2.4px 0 0 #23232b;
    -o-box-shadow: 3.6px 2.4px 0 0 #23232b;
    box-shadow: 3.6px 2.4px 0 0 #23232b;
}
.instructor__details-nav button.courses-button-prev {
    -webkit-box-shadow: -3.6px 2.4px 0 0 #23232b;
    -moz-box-shadow: -3.6px 2.4px 0 0 #23232b;
    -ms-box-shadow: -3.6px 2.4px 0 0 #23232b;
    -o-box-shadow: -3.6px 2.4px 0 0 #23232b;
    box-shadow: -3.6px 2.4px 0 0 #23232b;
}
.instructor__details-nav button.courses-button-prev i {
    transform: rotate(180deg);
}
.instructor__details-nav button:hover {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}
.instructor__progress-wrap .list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin: 0 -28px;
}
.instructor__progress-wrap .list-wrap li {
    width: 50%;
    flex: 0 0 auto;
    padding: 0 28px;
}
.instructor__sidebar {
    background: var(--tg-common-color-white);
    border: 1px solid #dfdfdf;
    -webkit-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -moz-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -ms-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -o-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    padding: 30px 25px 35px;
    margin-left: 20px;
}
.cta__bg,
.cta__bg-three,
.cta__bg-two {
    background-size: cover;
    background-position: center;
}
.cta__btn .btn:hover,
.cta__content .btn {
    box-shadow: none;
}
.instructor__sidebar .title {
    font-size: 20px;
    margin-bottom: 10px;
}
.comment-field,
.comment-notes,
.contact-form-wrap p,
.instructor__sidebar p,
.latest-comments .comments-text p,
.shop-widget ul li {
    margin-bottom: 15px;
}
.instructor__sidebar .form-grp {
    margin-bottom: 8px;
}
.instructor__sidebar .form-grp input,
.instructor__sidebar .form-grp textarea {
    border: none;
    background: #f4f3f8;
    width: 100%;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -o-border-radius: 4px;
    -ms-border-radius: 4px;
    border-radius: 4px;
    font-size: 14px;
    padding: 10px 20px;
    color: var(--tg-heading-color);
    height: 45px;
}
.instructor__sidebar .form-grp input::placeholder,
.instructor__sidebar .form-grp textarea::placeholder {
    font-size: 14px;
    color: #6d6c80;
}
.instructor__sidebar .form-grp textarea {
    min-height: 112px;
    max-height: 112px;
}
.instructor__sidebar .btn {
    width: 100%;
    justify-content: center;
    margin-top: 10px;
}
.cta__bg-three,
.progress-item .title {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.progress-item .title {
    display: flex;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 500;
}
.progress-item .progress {
    height: 10px;
    background: #ebebeb;
    border-radius: 30px;
}
.progress-item .progress .progress-bar {
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
    background-color: var(--tg-theme-secondary);
}
.progress-item-two {
    margin-bottom: 0;
    margin-top: 18px;
}
.progress-item-two .title {
    font-size: 12px;
    letter-spacing: 0.5px;
    opacity: 0.5;
    font-weight: 600;
    margin-bottom: 8px;
}
.progress-item-two .progress {
    height: 6px;
}
.progress-item-two .progress .progress-bar {
    background-color: #3eb75e;
}
.cta__area {
    position: relative;
    padding: 200px 0;
}
.cta__bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    mix-blend-mode: luminosity;
    background-attachment: fixed;
    z-index: -1;
}
.cta__bg-three,
.cta__bg-two,
.cta__inner-wrap,
.fact__bg {
    position: relative;
    z-index: 1;
}
.cta__bg::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--tg-common-color-black-3);
    opacity: 0.5;
    z-index: -1;
}
.cta__btn .btn:hover,
.cta__content .btn,
.cta__content-three .btn {
    background: var(--tg-theme-secondary);
}
.cta__bg-two {
    padding: 70px 0 0;
    overflow: hidden;
}
.cta__bg-three {
    margin: -85px 90px 0;
    border-radius: 15px;
    padding: 12px 48px 0;
    display: flex;
}
.cta__inner-wrap,
.event__thumb-three img {
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
}
.cta__inner-wrap {
    background: var(--tg-common-color-blue-2);
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    padding: 20px 70px 0;
    border-radius: 15px;
    overflow: hidden;
    gap: 40px;
    margin-top: -90px;
}
@media (max-width: 1500px) {
    .cta__area {
        padding: 170px 0;
    }
    .cta__bg-three {
        margin: -85px 60px 0;
    }
    .cta__inner-wrap {
        padding: 20px 50px 0;
    }
}
.cta__inner-wrap .shape {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: -1;
}
.cta__img .shape img {
    position: absolute;
    left: 22%;
    top: -10%;
    z-index: -1;
    max-width: 247px;
    -webkit-animation-duration: 100s;
    animation-duration: 100s;
}
.cta__img-two {
    width: 154px;
    flex: 0 0 auto;
    margin-right: 40px;
}
.cta__img-three {
    width: 104px;
    flex: 0 0 auto;
    position: relative;
}
.cta__content-three,
.cta__content-two {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.cta__img-three .svg-icon {
    position: absolute;
    right: 10%;
    top: -5%;
    width: 23px;
    height: auto;
}
.cta__img-three .svg-icon svg {
    color: var(--tg-theme-secondary);
    width: 100%;
    height: 100%;
}
.cta__content .title {
    margin-bottom: 12px;
    text-transform: capitalize;
    font-size: 60px;
    font-weight: 700;
}
.cta__content .btn,
.cta__content .btn svg {
    color: var(--tg-heading-color);
}
.cta__content p,
.cta__content-two .title {
    font-weight: 400;
    text-transform: capitalize;
}
@media (max-width: 1199.98px) {
    .instructor__sidebar {
        margin-left: 0;
        margin-top: 80px;
    }
    .cta__area {
        padding: 150px 0;
    }
    .cta__bg-three {
        margin: -85px 0 0;
    }
    .cta__inner-wrap {
        gap: 25px;
    }
    .cta__img .shape img {
        left: 5%;
        max-width: 230px;
    }
    .cta__content .title {
        font-size: 55px;
    }
}
@media (max-width: 991.98px) {
    .cta__bg-two {
        padding: 70px 0;
    }
    .cta__bg-three {
        padding: 30px 40px;
    }
    .cta__inner-wrap {
        padding: 35px 50px;
    }
    .cta__img,
    .cta__img-three,
    .cta__img-two {
        display: none;
    }
    .cta__content .title {
        font-size: 50px;
    }
}
@media (max-width: 767.98px) {
    .instructor__details-biography {
        padding: 30px 25px;
    }
    .instructor__details-Skill {
        padding: 30px 25px 15px;
    }
    .instructor__details-nav {
        margin-top: 25px;
        justify-content: flex-start;
    }
    .instructor__progress-wrap .list-wrap {
        margin: 0 -10px;
    }
    .instructor__progress-wrap .list-wrap li {
        padding: 0 10px;
        width: 100%;
    }
    .cta__area {
        padding: 120px 0;
    }
    .cta__bg-three {
        padding: 30px;
    }
    .cta__inner-wrap {
        padding: 35px 30px;
    }
    .cta__content .title {
        font-size: 40px;
    }
}
.cta__content p {
    margin-bottom: 25px;
    font-size: 24px;
    line-height: 1.2;
}
.cta__content-two {
    display: flex;
    align-items: center;
    gap: 20px 110px;
    margin-top: 60px;
}
@media (max-width: 1500px) {
    .cta__content-two {
        gap: 20px 70px;
    }
}
.cta__content-two .title {
    margin-bottom: 0;
    color: var(--tg-common-color-white);
    font-size: 36px;
}
@media (max-width: 1199.98px) {
    .cta__content-two {
        gap: 20px 30px;
    }
    .cta__content-two .title {
        font-size: 30px;
    }
}
@media (max-width: 991.98px) {
    .cta__content p {
        font-size: 20px;
    }
    .cta__content-two {
        flex-direction: column;
        margin-top: 0;
        text-align: center;
    }
    .cta__content-two .title {
        font-size: 36px;
    }
}
@media (max-width: 767.98px) {
    .cta__content-two .title {
        font-size: 30px;
    }
    .cta__shape img {
        display: none;
    }
}
.cta__content-two .title span {
    font-weight: 700;
    color: var(--tg-theme-secondary);
}
.cta__content-three {
    display: flex;
    align-items: center;
    gap: 30px;
    flex-grow: 1;
    justify-content: space-between;
}
.fact__inner-wrap-two,
.fact__item .count {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.cta__content-three .content__left .title {
    margin-bottom: 0;
    color: var(--tg-common-color-white);
    font-size: 28px;
    text-transform: capitalize;
    font-weight: 600;
}
.cta__content-three .content__left p {
    margin-bottom: 0;
    color: var(--tg-common-color-white);
    font-weight: 500;
    text-transform: capitalize;
}
.cta__btn .btn,
.cta__content-three .btn,
.cta__content-three .btn svg {
    color: var(--tg-heading-color);
}
.cta__content-three .btn {
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    box-shadow: none;
    padding: 16px 19px;
    width: 168px;
    flex: 0 0 auto;
}
.cta__btn .btn,
.cta__content-four .btn {
    background: var(--tg-common-color-white);
}
.cta__content-four {
    margin-top: -20px;
}
.cta__content-four .content__left .title {
    font-size: 30px;
    margin-bottom: 5px;
}
.cta__btn,
.event__details-content-wrap .row .col-30,
.event__inner-wrap .row .col-30 {
    width: 30%;
    flex: 0 0 auto;
}
.cta__btn .btn {
    box-shadow: 4px 6px 0 0 #0f0d32;
}
.fact__item .count,
.fact__item p {
    color: var(--tg-common-color-white);
}
.cta__shape img {
    position: absolute;
    right: 6%;
    top: 0;
    z-index: -1;
    max-width: 382px;
}
.cta__shape-two img {
    position: absolute;
    z-index: -1;
    left: 0;
    top: 0;
}
.fact__bg {
    background-size: cover;
    background-position: center;
    padding: 130px 0;
}
.fact__inner-wrap,
.fact__inner-wrap-two {
    background: var(--tg-common-color-blue-2);
    position: relative;
    z-index: 3;
}
.fact__inner-wrap {
    padding: 94px 70px 64px;
    -webkit-border-radius: 40px;
    -moz-border-radius: 40px;
    -o-border-radius: 40px;
    -ms-border-radius: 40px;
    border-radius: 40px;
    -webkit-box-shadow: 0 25px 70px 0 rgba(40, 37, 104, 0.4);
    -moz-box-shadow: 0 25px 70px 0 rgba(40, 37, 104, 0.4);
    -ms-box-shadow: 0 25px 70px 0 rgba(40, 37, 104, 0.4);
    -o-box-shadow: 0 25px 70px 0 rgba(40, 37, 104, 0.4);
    box-shadow: 0 25px 70px 0 rgba(40, 37, 104, 0.4);
}
.fact__inner-wrap-two {
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    -o-border-radius: 20px;
    -ms-border-radius: 20px;
    border-radius: 20px;
    padding: 85px 380px 60px 85px;
    display: flex;
    align-items: center;
    gap: 75px;
    overflow: hidden;
}
.event__area,
.features__area-six,
.features__item-five,
.features__item-three,
.features__item-two {
    z-index: 1;
    position: relative;
}
@media (max-width: 1500px) {
    .fact__inner-wrap-two {
        padding: 85px 360px 60px 70px;
        gap: 50px;
    }
}
@media (max-width: 1199.98px) {
    .cta__btn {
        width: auto;
        flex-grow: 1;
    }
    .fact__inner-wrap {
        padding: 60px 40px 30px;
    }
    .fact__inner-wrap-two {
        padding: 70px 50px 50px;
    }
}
.fact__inner-wrap-two .section__title {
    width: 46%;
    flex: 0 0 auto;
}
.fact__content-wrap,
.faq__content {
    width: 80%;
}
@media (max-width: 1500px) {
    .fact__content-wrap {
        width: 88%;
    }
}
.fact__content-wrap .title {
    margin-bottom: 15px;
    font-size: 48px;
    text-transform: capitalize;
    font-weight: 700;
}
.fact__content-wrap .title span {
    position: relative;
    display: inline-block;
}
.fact__content-wrap .title span::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 5px;
    width: 100%;
    height: 16px;
    background: var(--tg-theme-secondary);
    z-index: -1;
}
.event__content-three .title a:hover,
.event__content-two .title a:hover,
.event__item-content .title a:hover {
    color: inherit;
    background-size: 0 2px, 100% 2px;
}
.fact__content-wrap p {
    margin-bottom: 20px;
    font-size: 18px;
}
.fact__item {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}
.fact__item::before {
    content: "";
    position: absolute;
    right: -15px;
    top: -10%;
    width: 2px;
    height: 108px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -o-border-radius: 2px;
    -ms-border-radius: 2px;
    border-radius: 2px;
    background: linear-gradient(180deg, #fff 0, rgba(255, 255, 255, 0) 100%);
}
.fact__img-wrap,
.fact__img-wrap .shape-one {
    right: 0;
    bottom: 0;
    position: absolute;
}
@media (max-width: 991.98px) {
    .cta__content-three {
        flex-direction: column;
        text-align: center;
    }
    .cta__content-four {
        margin-top: 0;
    }
    .cta__shape img {
        right: 0;
    }
    .fact__bg {
        padding: 130px 0 100px;
    }
    .fact__inner-wrap-two {
        padding: 60px 40px 30px;
        flex-wrap: wrap;
        gap: 0;
        justify-content: center;
    }
    .fact__content-wrap {
        text-align: center;
        margin-bottom: 50px;
    }
    .fact__item::before {
        display: none;
    }
}
.fact__item .count {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 56px;
    line-height: 0.84;
    font-family: var(--tg-body-font-family);
}
.fact__item-two .count,
.fact__item-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.event__item-content .date,
.fact__item-two .count,
.faq__language-wrap span,
.features__content-six span,
.product-desc-wrap .nav-tabs .nav-link {
    font-family: var(--tg-heading-font-family);
}
@media (max-width: 1199.98px) {
    .fact__inner-wrap-two .section__title {
        width: 100%;
        text-align: center;
    }
    .fact__content-wrap {
        width: 100%;
    }
    .fact__content-wrap .title {
        font-size: 42px;
    }
    .fact__item .count {
        font-size: 48px;
    }
}
@media (max-width: 767.98px) {
    .fact__bg {
        padding: 100px 0 70px;
    }
    .fact__inner-wrap-two {
        padding: 60px 30px 30px;
    }
    .fact__content-wrap .title {
        font-size: 38px;
    }
    .fact__item .count {
        font-size: 30px;
    }
}
.fact__item p {
    margin-bottom: 0;
    font-weight: 500;
    line-height: 1.2;
}
.fact__item-wrap {
    display: flex;
    gap: 120px;
}
.fact__item-wrap .fact__item::before {
    right: -60px;
    top: -9%;
}
.fact__item-two {
    background: var(--tg-common-color-white);
    box-shadow: 0 0 30px 0 #f0f0f0;
    border: 1px solid #e3e3e3;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
    padding: 73px 20px;
}
.fact__item-two .count {
    color: var(--tg-theme-primary);
    font-size: 70px;
    line-height: 0.8;
    font-weight: 700;
    display: flex;
    align-items: center;
}
.fact__item-two p {
    color: var(--tg-body-color);
    font-weight: 400;
}
.features__icon-two,
.features__item-six {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.fact__img-wrap img {
    right: 30px;
    position: relative;
}
.fact__img-wrap .shape-one {
    z-index: -1;
}
.fact__img-wrap .shape-two .svg-icon {
    width: 40px;
    height: 40px;
    position: absolute;
    left: -7px;
    top: -22px;
    z-index: -1;
}
.fact__img-wrap .shape-two .svg-icon svg {
    width: 100%;
    height: 100%;
}
.fact__shape-wrap img:first-child {
    bottom: 16%;
    left: 35%;
}
@media (max-width: 991.98px) {
    .fact__item-wrap {
        gap: 60px;
    }
    .fact__item-wrap .fact__item::before {
        display: none;
    }
    .fact__shape-wrap img:first-child {
        bottom: 4%;
        left: 15%;
    }
}
.fact__shape-wrap img:nth-child(2) {
    right: 9%;
    top: 13%;
    -webkit-animation-duration: 15s;
    animation-duration: 15s;
}
.features__area {
    background: var(--tg-common-color-blue-2);
    padding: 120px 0 90px;
}
.features__area-three {
    background: var(--tg-common-color-gray);
}
.features__area-four {
    margin-top: -65px;
}
.features__area-eight {
    background: var(--tg-heading-color);
    padding: 65px 0 35px;
}
.features__item {
    text-align: center;
    margin-bottom: 30px;
}
.features__item:hover .features__icon {
    -webkit-transform: translateY(5px);
    -moz-transform: translateY(5px);
    -ms-transform: translateY(5px);
    -o-transform: translateY(5px);
    transform: translateY(5px);
}
.features__item-two {
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    -o-border-radius: 20px;
    -ms-border-radius: 20px;
    border-radius: 20px;
    border: 1px solid #c9e4e9;
    background: #f1fdff;
    box-shadow: 8px 8px 0 0 #c9e4e9;
    padding: 40px 40px 40px 50px;
    margin-bottom: 30px;
    transition: 0.3s ease-out;
}
.features__item-shape,
.features__item-three {
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.features__item-two:hover {
    box-shadow: none !important;
}
.features__item-two:hover .features__item-shape {
    color: #1bcbe3;
}
.features__item-three {
    border: 1px solid var(--tg-border-3);
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
    background: var(--tg-common-color-white);
    text-align: center;
    padding: 40px 35px;
    transition: 0.3s ease-out;
    margin-bottom: 30px;
}
@media (max-width: 1500px) {
    .fact__item-two {
        padding: 60px 20px;
    }
    .fact__shape-wrap img:nth-child(2) {
        right: 3%;
        top: 12%;
    }
    .features__item-three {
        padding: 40px 25px;
    }
}
@media (max-width: 1199.98px) {
    .fact__item-two {
        padding: 40px 20px;
    }
    .fact__item-two .count {
        font-size: 48px;
    }
    .fact__img-wrap {
        display: none;
    }
    .features__item-two {
        padding: 30px 25px;
    }
    .features__item-three {
        padding: 30px 20px;
    }
}
@media (max-width: 767.98px) {
    .fact__item-wrap {
        gap: 30px;
    }
    .fact__shape-wrap img:first-child {
        bottom: 1%;
        left: 9%;
        width: 50px;
    }
    .fact__shape-wrap img:nth-child(2) {
        right: 3%;
        top: 2%;
    }
    .features__area {
        padding: 100px 0 70px;
    }
    .features__item-three {
        padding: 30px;
    }
}
.features__item-three:hover {
    transform: translateY(-3px);
}
.features__item-three:hover .features__icon-three {
    transform: rotateY(180deg);
}
.features__item-four {
    text-align: center;
    margin-bottom: 30px;
}
.features__item-four:hover .features__icon-four {
    transform: translateY(5px);
}
.features__item-five {
    padding: 45px 60px 55px;
    text-align: center;
    margin-bottom: 30px;
}
.features__item-five-shape .shape-one {
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
}
.features__item-five-shape .shape-one svg {
    width: 100%;
    height: 100%;
    overflow: visible;
    display: block;
    position: absolute;
}
.features__item-five-shape .shape-one svg path:first-child {
    color: #4741cf;
}
.features__item-five-shape .shape-one svg path:nth-child(2),
.features__item-wrap-two
    .row
    [class*="col-"]:nth-child(2)
    .features__icon-three {
    color: var(--tg-theme-primary);
}
.features__item-five-shape .shape-two {
    position: absolute;
    left: 10px;
    top: 5px;
    right: 10px;
    bottom: 15px;
    z-index: -1;
}
.features__item-five-shape .shape-two svg {
    width: 100%;
    height: 100%;
    overflow: visible;
    display: block;
    position: absolute;
    color: var(--tg-common-color-white);
    -webkit-transition: 0.2s ease-out;
    -moz-transition: 0.2s ease-out;
    -ms-transition: 0.2s ease-out;
    -o-transition: 0.2s ease-out;
    transition: 0.2s ease-out;
}
.features__item-five-shape .shape-two svg .animation-dashed {
    animation: 0.5s linear infinite paused animation__shape;
}
.features__item-six {
    display: flex;
    align-items: center;
    gap: 18px;
    margin-bottom: 30px;
}
.features__item-six:hover .features__icon-six {
    transform: rotateY(180deg);
}
.features__item-shape {
    position: absolute;
    right: 28px;
    top: 21px;
    z-index: -1;
    transition: 0.3s ease-out;
    color: rgba(39, 39, 47, 0.2);
}
.features__item-wrap .row [class*="col-"]:nth-child(2) .features__item-two {
    border: 1px solid #c8c1ed;
    background: #edeaff;
    box-shadow: 8px 8px 0 0 #d9d5f1;
}
.features__item-wrap
    .row
    [class*="col-"]:nth-child(2)
    .features__item-two
    .features__icon-two {
    background: #5751e1;
}
.features__item-wrap
    .row
    [class*="col-"]:nth-child(2)
    .features__item-two:hover
    .features__item-shape {
    color: #5751e1;
}
.features__item-wrap .row [class*="col-"]:nth-child(3) .features__item-two {
    border: 1px solid #ebe0c4;
    background: #fff7e2;
    box-shadow: 8px 8px 0 0 #e5decb;
}
.features__item-wrap
    .row
    [class*="col-"]:nth-child(3)
    .features__item-two
    .features__icon-two {
    background: #ffc224;
}
.features__item-wrap
    .row
    [class*="col-"]:nth-child(3)
    .features__item-two:hover
    .features__item-shape {
    color: #ffc224;
}
.features__item-wrap-two
    .row
    [class*="col-"]:nth-child(3)
    .features__icon-three {
    color: var(--tg-common-color-pink);
}
.features__item-wrap-two
    .row
    [class*="col-"]:nth-child(4)
    .features__icon-three {
    color: var(--tg-common-color-cyan);
}
.features__item-wrap-three
    .row
    [class*="col-"]:nth-child(2)
    .features__item-five-shape
    .shape-one
    svg
    path:first-child {
    color: #da9d00;
}
.features__item-wrap-three
    .row
    [class*="col-"]:nth-child(2)
    .features__item-five-shape
    .shape-one
    svg
    path:nth-child(2) {
    color: #f3b107;
}
.features__item-wrap-three
    .row
    [class*="col-"]:nth-child(3)
    .features__item-five-shape
    .shape-one
    svg
    path:first-child {
    color: #be76b5;
}
.features__item-wrap-three
    .row
    [class*="col-"]:nth-child(3)
    .features__item-five-shape
    .shape-one
    svg
    path:nth-child(2) {
    color: #d78bcd;
}
.features__item-wrap-three
    .row
    [class*="col-"]:nth-child(4)
    .features__item-five-shape
    .shape-one
    svg
    path:first-child {
    color: #5eac1f;
}
.features__item-wrap-three
    .row
    [class*="col-"]:nth-child(4)
    .features__item-five-shape
    .shape-one
    svg
    path:nth-child(2) {
    color: #75c137;
}
.features__item-wrap-four {
    border: 1px solid #e3e3ef;
    background: var(--tg-common-color-white);
    -webkit-border-radius: 25px;
    -moz-border-radius: 25px;
    -o-border-radius: 25px;
    -ms-border-radius: 25px;
    border-radius: 25px;
    padding: 70px 70px 40px;
    margin-top: -100px;
    position: relative;
    z-index: 2;
}
.features__icon {
    margin-bottom: 22px;
    min-height: 93px;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    transition: 0.3s ease-out;
}
.features__icon-two {
    width: 70px;
    height: 70px;
    flex: 0 0 auto;
    color: var(--tg-common-color-white);
    background: #1bcbe3;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.features__icon-three {
    color: var(--tg-theme-secondary);
    margin-bottom: 25px;
    min-height: 50px;
    -webkit-transition: 0.5s ease-out;
    -moz-transition: 0.5s ease-out;
    -ms-transition: 0.5s ease-out;
    -o-transition: 0.5s ease-out;
    transition: 0.5s ease-out;
}
.features__icon-four,
.features__icon-six {
    align-items: center;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
}
.features__icon-four {
    width: 80px;
    height: 80px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    justify-content: center;
    background: var(--tg-theme-secondary);
    color: var(--tg-common-color-black-3);
    margin: 0 auto 30px;
    -webkit-border-radius: 25px;
    -moz-border-radius: 25px;
    -o-border-radius: 25px;
    -ms-border-radius: 25px;
    border-radius: 25px;
    transition: 0.3s ease-out;
}
.features__content .title,
.features__content-four .title,
.features__icon-five,
.features__icon-five svg {
    color: var(--tg-common-color-white);
}
.event__meta .list-wrap li::before,
.features__icon-six {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
}
.features__icon-five {
    position: relative;
    line-height: 0;
    font-size: 60px;
    display: inline-block;
    margin-bottom: 15px;
}
.features__content-two .content-top,
.features__icon-six {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.features__icon-five svg {
    position: absolute;
    right: -13px;
    top: -8px;
}
.features__icon-six {
    width: 70px;
    height: 70px;
    display: flex;
    justify-content: center;
    flex: 0 0 auto;
    background: #efeff3;
    border-radius: 50%;
    font-size: 35px;
    color: var(--tg-heading-color);
    transition: 0.3s ease-out;
}
.event__item-content .location:hover,
.features__icon-seven {
    color: var(--tg-theme-secondary);
}
.features__icon-seven {
    background: #28254e;
}
.event__item,
.event__item-two,
.event__item-wrap-two,
.features__content-five .btn {
    background: var(--tg-common-color-white);
}
.features__content .title {
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: var(--tg-fw-semi-bold);
}
.features__content p {
    margin-bottom: 0;
    color: var(--tg-common-color-gray-4);
}
.event__meta .list-wrap li.author a,
.features__content-two p {
    color: var(--tg-common-color-dark);
}
.features__content-two .content-top {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 18px;
}
.features__content-two .title {
    margin-bottom: 0;
    font-size: 22px;
}
.features__content-two p {
    margin-bottom: 0;
    text-transform: capitalize;
}
.features__content-three p {
    margin-bottom: 0;
    line-height: 1.62;
}
.features__content-four .title {
    margin-bottom: 15px;
    font-size: 22px;
}
.features__content-four p {
    margin-bottom: 0;
    color: #bcbad8;
    font-weight: 500;
}
.features__content-five .title,
.features__content-five p {
    color: var(--tg-common-color-white);
    text-transform: capitalize;
}
.features__content-five .title {
    margin-bottom: 10px;
    font-size: 20px;
}
.event__item-content .date,
.features__content-five .btn,
.features__content-five .btn svg {
    color: var(--tg-heading-color);
}
.features__content-five p {
    margin-bottom: 20px;
    font-weight: 400;
    line-height: 1.5;
}
.features__content-five .btn {
    box-shadow: none;
}
.features__content-six .title {
    margin-bottom: 6px;
    font-size: 18px;
    font-weight: 600;
    text-transform: capitalize;
}
.features__content-six span {
    display: block;
    line-height: 1.2;
}
.features__content-seven span {
    color: #bab8e5;
}
.features__shape-wrap img:first-child {
    left: 0;
    top: 21%;
}
.features__shape-wrap img:nth-child(2) {
    right: 0;
    top: 21%;
}
.features__shape-wrap-two img:first-child {
    left: 11%;
    top: 13%;
    max-width: 65px;
    -webkit-animation-duration: 30s;
    animation-duration: 30s;
}
.features__shape-wrap-two img:nth-child(2) {
    right: 10%;
    top: 24%;
    max-width: 113px;
}
.event__area::before {
    content: "";
    position: absolute;
    right: 18%;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 790px;
    height: 430px;
    background: #f2f4f6;
    -webkit-border-radius: 80px;
    -moz-border-radius: 80px;
    -o-border-radius: 80px;
    -ms-border-radius: 80px;
    border-radius: 80px;
    z-index: -1;
}
.event__area-three {
    position: relative;
    z-index: 1;
}
.event__area-three::before {
    content: "";
    position: absolute;
    right: 0;
    top: 0;
    background: var(--tg-common-color-blue-2);
    width: 100%;
    height: 58%;
    z-index: -1;
}
.event__date,
.event__item-content .date {
    font-size: 14px;
    font-weight: 600;
    z-index: 2;
}
.event__area-four {
    position: relative;
    padding: 140px 0 100px;
    overflow: hidden;
}
.event__details-content-wrap .row .col-70,
.event__inner-wrap .row .col-70 {
    width: 70%;
    flex: 0 0 auto;
}
.event__content p {
    margin-bottom: 25px;
}
.event__content-wrap {
    margin-left: 85px;
}
.event__content-wrap .section__title .title {
    font-weight: 700;
}
.event__content-wrap p {
    margin-bottom: 0;
    width: 90%;
}
@media (max-width: 1500px) {
    .features__item-five {
        padding: 45px 50px 55px;
    }
    .event__content-wrap {
        margin-left: 0;
    }
    .event__content-wrap p {
        width: 95%;
    }
}
.event__item {
    border: 1px solid #b5b5c3;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    padding: 20px 20px 20px 25px;
    margin-bottom: 30px;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    transition: 0.3s ease-out;
}
.event__content-three .title a,
.event__content-two .title a,
.event__item-content .title a {
    display: inline;
    transition: background-size 0.4s linear;
    background-image: linear-gradient(
            var(--tg-heading-color),
            var(--tg-heading-color)
        ),
        linear-gradient(var(--tg-heading-color), var(--tg-heading-color));
    background-size: 0 2px, 0 2px;
    background-position: 100% 100%, 0 100%;
    background-repeat: no-repeat;
}
.event__item:hover {
    -webkit-box-shadow: 10px 10px 0 #cac9d6;
    -moz-box-shadow: 10px 10px 0 #cac9d6;
    -ms-box-shadow: 10px 10px 0 #cac9d6;
    -o-box-shadow: 10px 10px 0 #cac9d6;
    box-shadow: 10px 10px 0 #cac9d6;
}
.event__item-thumb {
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    overflow: hidden;
}
.event__item-thumb img {
    width: 100%;
    height: 212px;
    object-fit: cover;
}
.event__item-content {
    position: relative;
    padding: 30px 0 0;
}
.event__item-content .date {
    background: var(--tg-theme-secondary);
    display: block;
    line-height: 1;
    border: 1px solid var(--tg-heading-color);
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
    padding: 8px 13px;
    position: absolute;
    left: 0;
    top: -20px;
    -webkit-box-shadow: 4px 4px 0 0 #3d3d3d;
    -moz-box-shadow: 4px 4px 0 0 #3d3d3d;
    -ms-box-shadow: 4px 4px 0 0 #3d3d3d;
    -o-box-shadow: 4px 4px 0 0 #3d3d3d;
    box-shadow: 4px 4px 0 0 #3d3d3d;
}
.event__item-content .location,
.event__item-three {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.event__item-content .title {
    font-size: 18px;
    margin-bottom: 15px;
    line-height: 1.4;
}
.event__item-content .location {
    display: flex;
    align-items: center;
    font-size: 15px;
    color: var(--tg-body-color);
    gap: 5px;
    line-height: 1;
}
.event__item-content .location i {
    font-size: 18px;
    line-height: 0;
    color: var(--tg-theme-primary);
}
.event__item-wrap-two {
    border: 1px solid #e3e3e3;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
    padding: 30px 30px 0;
}
@media (max-width: 1199.98px) {
    .features__item-wrap-four {
        padding: 70px 40px 40px;
    }
    .event__area::before {
        right: 10%;
    }
    .event__inner-wrap .row .col-30,
    .event__inner-wrap .row .col-70 {
        width: 100%;
    }
    .event__content {
        text-align: center;
        margin-bottom: 50px;
    }
    .event__content .tg-button-wrap {
        justify-content: center;
    }
    .event__content-wrap p {
        width: 100%;
    }
    .event__item-wrap-two {
        padding: 20px 15px 0;
    }
}
.event__item-wrap-three {
    margin-right: 90px;
}
.event__item-wrap-three .event__item-four:nth-child(2n) {
    flex-direction: row-reverse;
    justify-content: space-between;
}
.event__item-two {
    border: 1px solid #e3e3e3;
    padding: 30px 30px 48px;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
    margin-bottom: 30px;
}
.event__date,
.event__details-content-top .tag:hover,
.shop-thumb .flash-sale.hot {
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
}
@media (max-width: 1199.98px) {
    .event__item-two {
        padding: 20px 20px 48px;
    }
}
@media (max-width: 991.98px) {
    .features__shape-wrap img:first-child,
    .features__shape-wrap img:nth-child(2) {
        top: 30%;
    }
    .event__area::before {
        display: none;
    }
    .event__area-three .section__title {
        margin-bottom: 25px;
        text-align: center;
    }
    .event__area-three .section__content {
        text-align: center;
    }
    .event__content-wrap {
        text-align: center;
        margin-bottom: 50px;
    }
    .event__item-wrap-two {
        padding: 30px 30px 0;
    }
    .event__item-two {
        padding: 30px 30px 48px;
    }
}
.event__item-three {
    display: flex;
    align-items: center;
    gap: 30px;
    margin-bottom: 30px;
}
.event__item-four {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 40px;
}
.event__thumb-two,
.footer__newsletter-form {
    position: relative;
    margin-bottom: 25px;
}
.event__thumb-two img {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    width: 100%;
    height: 310px;
    object-fit: cover;
}
.event__thumb-three {
    width: 300px;
    flex: 0 0 auto;
}
@media (max-width: 1199.98px) {
    .event__item-three {
        gap: 15px;
    }
    .event__thumb-three {
        width: 220px;
    }
}
.event__thumb-three img {
    width: 100%;
    min-height: 245px;
    object-fit: cover;
    border-radius: 15px;
}
.event__thumb-four {
    width: 270px;
    flex: 0 0 auto;
}
.event__thumb-four img {
    width: 100%;
    height: 192px;
    object-fit: cover;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
}
@media (max-width: 767.98px) {
    .features__item-wrap-four {
        padding: 40px 40px 10px;
    }
    .event__content-four .title br,
    .features__shape-wrap img:first-child,
    .features__shape-wrap img:nth-child(2),
    .features__shape-wrap-two img:first-child {
        display: none;
    }
    .event__area-four {
        padding: 100px 0 60px;
    }
    .event__item-wrap-two {
        padding: 20px 20px 0;
    }
    .event__item-wrap-three .event__item-four:nth-child(2n) {
        flex-direction: column;
    }
    .event__item-two {
        padding: 20px 20px 48px;
    }
    .event__item-three {
        gap: 20px;
        flex-direction: column;
    }
    .event__item-four {
        flex-direction: column;
    }
    .event__thumb-four,
    .event__thumb-three {
        width: 100%;
    }
    .event__thumb-four img {
        height: 210px;
    }
}
.event__content-two .title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: capitalize;
}
.event__content-three p,
.event__content-two p,
.event__details-overview {
    margin-bottom: 20px;
}
.event__content-three .title {
    margin-bottom: 10px;
    font-size: 22px;
    font-weight: 600;
}
.event__content-four .event__date-two {
    margin-bottom: 18px;
}
.event__content-four .title {
    margin-bottom: 18px;
    font-size: 20px;
}
.event__date {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    padding: 10px 9px;
    line-height: 1;
    position: absolute;
    left: 25px;
    top: 23px;
}
.event__bottom-content .title,
.event__bottom-content .title a {
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.faq__area-two,
.faq__img {
    z-index: 1;
}
.event__date-two {
    position: inherit;
    margin-bottom: 10px;
}
.event__meta .list-wrap li,
.faq__area,
.faq__area-two,
.faq__img,
.faq__img-four,
.faq__img-three,
.faq__img-wrap {
    position: relative;
}
.event__bottom-content {
    margin-top: 80px;
}
.event__bottom-content .title {
    display: flex;
    margin-bottom: 0;
    justify-content: center;
    gap: 13px;
    font-size: 16px;
    font-weight: 500;
    flex-wrap: wrap;
}
.event__bottom-content .title a {
    display: flex;
    color: var(--tg-theme-primary);
    gap: 10px;
    position: relative;
}
.event__bottom-content .title a::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 100%;
    height: 2px;
    background: currentColor;
    transform-origin: right top;
    transform: scale(0, 1);
    transition: transform 0.4s cubic-bezier(0.74, 0.72, 0.27, 0.24);
}
.event__bottom-content .title a svg {
    line-height: 0;
    width: 20px;
    transform: translateY(-2px);
}
.event__bottom-content .title a:hover::before {
    -webkit-transform-origin: left top;
    -ms-transform-origin: left top;
    transform-origin: left top;
    -webkit-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    transform: scale(1, 1);
}
.event__shape img {
    position: absolute;
    right: 9%;
    bottom: 13%;
    z-index: -1;
}
.event__shape-wrap img:first-child {
    left: 0;
    top: 0;
    max-width: 359px;
}
.event__shape-wrap img:nth-child(2) {
    right: 8%;
    top: 22%;
}
.event__shape-two img {
    position: absolute;
    left: 18%;
    top: -215px;
    z-index: -1;
    max-width: 374px;
    -webkit-animation-duration: 50s;
    animation-duration: 50s;
}
.event__details-thumb {
    margin-bottom: 60px;
}
.event__details-thumb img {
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
    min-height: 300px;
    object-fit: cover;
}
.event__details-content .title {
    margin-bottom: 15px;
    font-size: 36px;
}
@media (max-width: 1500px) {
    .event__item-wrap-three {
        margin-right: 0;
    }
    .event__shape-wrap img:first-child {
        max-width: 250px;
    }
    .event__shape-two img {
        left: 11%;
    }
    .event__details-content .title {
        font-size: 32px;
    }
}
.event__details-content .title-two {
    font-size: 24px;
    margin-bottom: 15px;
}
@media (max-width: 1199.98px) {
    .event__content-two .title {
        font-size: 22px;
    }
    .event__content-three .title {
        font-size: 20px;
    }
    .event__bottom-content {
        margin-top: 50px;
    }
    .event__shape-wrap img:nth-child(2) {
        right: 3%;
        top: 13%;
    }
    .event__shape-two img {
        top: -195px;
        max-width: 320px;
    }
    .event__details-content .title {
        font-size: 30px;
    }
    .event__details-content-wrap .row .col-70 {
        width: 65%;
    }
    .event__details-content-wrap .row .col-30 {
        width: 35%;
    }
}
.event__details-content-top {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}
.event__details-content-top .tag {
    font-size: 13px;
    font-weight: 500;
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    display: block;
    padding: 8px 12px;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
    line-height: 1;
}
.event__details-content-top .avg-rating,
.event__meta .list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.event__details-content-top .avg-rating {
    display: flex;
    font-size: 14px;
    gap: 5px;
    color: var(--tg-common-color-gray-3);
}
.event__details-content-top .avg-rating i {
    color: var(--tg-common-color-yellow);
    line-height: 1;
}
.event__details-inner {
    margin: 25px 0 20px;
}
.event__details-inner .row .col-39 {
    width: 38.8%;
    flex: 0 0 auto;
}
.event__details-inner .row .col-61 {
    width: 61.2%;
    flex: 0 0 auto;
}
.event__details-inner img {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    width: 100%;
}
.event__details-inner-content .title {
    margin-bottom: 25px;
    font-size: 24px;
    text-transform: capitalize;
}
.event__details-inner-content .about__info-list-item {
    margin: 0 0 10px;
}
.event__details-inner-content .about__info-list-item:last-child {
    margin: 0;
}
.event__details-inner-content .about__info-list-item i {
    width: 24px;
    height: 24px;
    flex: 0 0 auto;
}
.event__details-inner-content .about__info-list-item p {
    font-size: 16px;
}
.event__meta .list-wrap {
    display: flex;
    gap: 10px 35px;
    flex-wrap: wrap;
    border-bottom: 1px solid #d9d9d9;
    padding-bottom: 25px;
    margin-bottom: 28px;
}
.event__meta .list-wrap li,
.event__meta-two .list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.event__meta .list-wrap li {
    display: flex;
    gap: 8px;
    color: var(--tg-common-color-gray-3);
}
.event__meta .list-wrap li::before {
    content: "";
    position: absolute;
    right: -19px;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 5px;
    background: #8c9ab4;
    border-radius: 50%;
}
.event__meta .list-wrap li.author img,
.faq__area::before {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
}
.event__meta .list-wrap li i {
    font-size: 20px;
    line-height: 0;
}
.event__meta .list-wrap li.author img {
    border-radius: 50%;
}
.event__meta .list-wrap li.author a:hover,
.event__meta .list-wrap li.location i,
.product-desc-wrap .nav-tabs .nav-link.active,
.shop-content .title a:hover,
.shop-details-bottom .list-wrap li a:hover {
    color: var(--tg-theme-primary);
}
.event__meta-two .list-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 15px 25px;
}
.event__meta-two .list-wrap li,
.faq__img-four .main-img {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.event__meta-two .list-wrap li {
    display: flex;
    align-items: center;
    line-height: 1;
    gap: 5px;
    font-size: 16px;
    font-weight: 400;
    color: var(--tg-heading-color);
}
.event__meta-two .list-wrap li i {
    color: var(--tg-theme-primary);
    font-weight: 700;
}
.event__sidebar {
    margin: -190px 50px 0 20px;
}
@media (max-width: 1199.98px) {
    .event__details-inner .row .col-39,
    .event__details-inner .row .col-61 {
        width: 100%;
    }
    .event__details-inner img {
        margin-bottom: 30px;
    }
    .event__sidebar {
        margin: -190px 0 0;
    }
}
.event__sidebar .courses__details-sidebar {
    margin: 0;
}
.event__widget:last-child {
    margin-bottom: 0;
}
.event__map {
    background: var(--tg-common-color-white);
    border: 1px solid #dfdfdf;
    -webkit-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -moz-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -ms-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -o-box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    box-shadow: 0 0 14px rgba(0, 0, 0, 0.08);
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    padding: 30px;
}
.event__map .title {
    font-size: 20px;
    margin-bottom: 20px;
}
.event__map .map {
    width: 100%;
    height: 240px;
}
.faq__area::after,
.faq__area::before {
    content: "";
    width: 500px;
    height: 500px;
    background: #e9e2f9;
    filter: blur(200px);
    z-index: -1;
    position: absolute;
}
.event__map .map iframe {
    width: 100%;
    height: 100%;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
}
.faq__area {
    background: var(--tg-common-color-gray);
    padding: 260px 0 120px;
    margin-top: -140px;
    z-index: 1;
    overflow: hidden;
}
.faq__area::before {
    left: 45px;
    bottom: 20%;
    border-radius: 50%;
}
.faq__area::after {
    right: 54px;
    top: 110px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.faq__img {
    width: 450px;
    height: 562px;
    border-radius: 1000px;
    background: var(--tg-common-color-white);
    box-shadow: 9px 8px 0 #171717;
    margin: 0 auto;
    overflow: hidden;
}
.faq__img img {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
}
.faq__img .shape-one {
    position: absolute;
    left: 27%;
    top: 11%;
    z-index: -1;
}
.faq__img .shape-one svg {
    color: var(--tg-common-color-yellow-2);
}
.faq__img .shape-two {
    width: 61px;
    height: 46px;
    position: absolute;
    right: 25%;
    top: 10%;
}
.faq__img .shape-two .svg-icon {
    width: 100%;
    height: 100%;
    display: block;
}
.faq__img .shape-two .svg-icon path {
    stroke: var(--tg-common-color-black);
}
.faq__img-two {
    background: #f3f3f3;
    box-shadow: 9px 8px 0 #b8b8b8;
}
.faq__img-three {
    margin-bottom: 50px;
}
.faq__img-three .shape {
    position: absolute;
    z-index: -1;
}
.faq__img-three .shape-one {
    right: 2%;
    top: -10%;
    max-width: 113px;
}
.faq__img-three .shape-two {
    right: 0;
    bottom: 0;
    max-width: 96px;
}
.faq__img-four .main-img {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    gap: 40px;
}
@media (max-width: 1199.98px) {
    .faq__img {
        width: 400px;
        height: 550px;
    }
    .faq__img .shape-one {
        left: 25%;
        top: 9%;
    }
    .faq__img .shape-two {
        right: 18%;
    }
    .faq__img-three .shape-one {
        max-width: 80px;
    }
    .faq__img-three .shape-two {
        max-width: 65px;
    }
    .faq__img-four .main-img {
        gap: 20px;
    }
    .faq__img-four .main-img img {
        max-width: 250px;
    }
}
@media (max-width: 767.98px) {
    .event__shape-two img {
        top: -177px;
        max-width: 240px;
    }
    .event__details-thumb {
        margin-bottom: 30px;
    }
    .event__details-content .title {
        font-size: 28px;
    }
    .faq__area {
        padding: 240px 0 100px;
    }
    .faq__img {
        width: 100%;
        height: 500px;
        max-width: 400px;
    }
    .faq__img .shape-one {
        left: 16%;
        top: 11%;
    }
    .faq__img-four .main-img {
        flex-direction: column;
    }
}
.faq__img-four .main-img img {
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
}
@media (max-width: 991.98px) {
    .event__details-content-wrap .row .col-30,
    .event__details-content-wrap .row .col-70 {
        width: 100%;
    }
    .event__sidebar {
        margin: 80px 0 0;
    }
    .faq__img-four,
    .faq__img-wrap {
        margin-bottom: 50px;
    }
    .faq__img-four .main-img {
        gap: 30px;
    }
    .faq__img-four .main-img img {
        max-width: 260px;
    }
}
@media (max-width: 767.98px) {
    .faq__img-four .main-img img {
        max-width: 100%;
        width: 100%;
    }
}
.faq__img-four .main-img img:nth-child(2) {
    margin-top: 185px;
}
@media (max-width: 767.98px) {
    .faq__img-four .main-img img:nth-child(2) {
        margin-top: 0;
    }
    .faq__img-four .shape img {
        display: none;
    }
}
.blog__shape-wrap img,
.blog__shape-wrap-three img,
.blog__shape-wrap-two img,
.faq__img-four .shape img {
    position: absolute;
    z-index: -1;
}
.faq__img-four .shape img:first-child {
    left: 12%;
    bottom: 0;
    max-width: 142px;
}
.faq__img-four .shape img:nth-child(2) {
    right: 24%;
    top: 5%;
    max-width: 106px;
}
.faq__img-four .shape img:nth-child(3) {
    right: 20%;
    top: 4%;
    max-width: 306px;
}
.faq__img-wrap {
    margin-top: 45px;
}
.faq__img-shape {
    position: absolute;
    left: -12px;
    top: -9px;
    right: -12px;
    bottom: -9px;
    z-index: -1;
}
.faq__img-shape svg {
    width: 100%;
    height: 100%;
    overflow: visible;
    display: block;
    position: absolute;
    -webkit-transition: 0.2s ease-out;
    -moz-transition: 0.2s ease-out;
    -ms-transition: 0.2s ease-out;
    -o-transition: 0.2s ease-out;
    transition: 0.2s ease-out;
}
.faq__img-shape svg .animation-dashed {
    animation: 0.8s linear infinite paused animation__shape-two;
}
.faq__language-wrap {
    background: var(--tg-theme-primary);
    position: absolute;
    left: 21%;
    bottom: 7%;
    box-shadow: -8px 8px 0 0 #0f0996;
    border-radius: 15px;
    text-align: center;
    padding: 31px 45px;
}
.faq__language-wrap .title {
    margin-bottom: 15px;
    color: var(--tg-common-color-white);
    font-size: 48px;
    font-weight: 700;
    text-transform: capitalize;
    line-height: 0.8;
}
.faq__language-wrap span {
    display: block;
    line-height: 1;
    color: var(--tg-common-color-white);
    font-weight: 500;
}
.faq__mask-img {
    -webkit-mask-image: url(../img/others/h5_faq_mask_img.png);
    mask-image: url(../img/others/h5_faq_mask_img.png);
    -webkit-mask-size: 100%;
    mask-size: 100%;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    width: 690px;
    height: 532px;
    margin: 0 auto;
}
@media (max-width: 1500px) {
    .faq__img-four .shape img:first-child {
        left: 5%;
    }
    .faq__language-wrap {
        left: 14%;
    }
    .faq__mask-img {
        width: 630px;
        height: 508px;
    }
}
@media (max-width: 1199.98px) {
    .faq__img-four .shape img:nth-child(3) {
        right: 18%;
        top: 7%;
        max-width: 260px;
    }
    .faq__language-wrap {
        left: 10%;
        padding: 24px 30px;
    }
    .faq__mask-img {
        width: 450px;
        height: 350px;
    }
    .faq__content {
        width: 100%;
    }
}
@media (max-width: 767.98px) {
    .faq__language-wrap {
        display: none;
    }
    .faq__mask-img {
        width: 330px;
        height: 257px;
    }
}
.faq__mask-img img {
    max-width: unset;
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.blog__details-content > p,
.faq__content p,
.footer__link .list-wrap li,
.product-desc-review .left-rc {
    margin-bottom: 10px;
}
.faq__content-two,
.newsletter__form {
    width: 80%;
}
@media (max-width: 1500px) {
    .faq__content-two {
        width: 90%;
    }
    .shop-details-images-wrap {
        flex-direction: column-reverse;
    }
}
.faq__content-two > p {
    margin-bottom: 25px;
}
.faq__content-three {
    width: 97%;
}
.faq__wrap .accordion-item {
    background-color: transparent;
    border: none;
    border-radius: 0;
}
.faq__wrap .accordion-item .accordion-button {
    background-color: transparent;
    border: none;
    border-bottom: 1px solid var(--tg-common-color-gray-5);
    padding: 22px 0;
    font-size: 20px;
    font-weight: 500;
    font-family: var(--tg-heading-font-family);
    border-radius: 0;
    color: var(--tg-body-color);
    position: relative;
}
.blog__post-thumb-two,
.faq__wrap-two .accordion-item {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
}
.faq__wrap .accordion-item .accordion-button::after {
    content: "\f105";
    font-family: flaticon_skill_grow;
    font-size: 20px;
    position: absolute;
    right: 0;
    top: 25px;
    color: var(--tg-common-color-dark);
    box-shadow: none;
    background: 0 0;
    font-weight: 700;
}
.faq__wrap .accordion-item .accordion-button:not(.collapsed) {
    box-shadow: none;
    color: var(--tg-theme-primary);
}
.faq__wrap .accordion-item .accordion-button:not(.collapsed)::after {
    transform: rotate(-90deg);
    color: var(--tg-theme-primary);
}
.faq__wrap .accordion-item .accordion-button:focus {
    box-shadow: none;
}
.faq__wrap .accordion-item:last-of-type .accordion-button.collapsed {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.faq__wrap .accordion-body {
    padding: 15px 0 0;
}
.faq__wrap .accordion-body p {
    margin-bottom: 0;
    color: var(--tg-common-color-dark);
}
.faq__wrap-two .accordion-item {
    background-color: #f8f8f8;
    border-radius: 10px;
    margin-bottom: 13px;
}
.blog__post-content-four .blog__post-meta,
.faq__wrap-two .accordion-item:last-child,
.product-desc-wrap .tab-content p,
.shop-details-bottom .list-wrap li:last-child,
.shop-widget .form-check .rating ul li,
.shop-widget ul li:last-child,
.shop-widget:last-child {
    margin-bottom: 0;
}
.faq__wrap-two .accordion-item .accordion-button {
    background-color: #f8f8f8;
    border-bottom: none;
    padding: 21px 24px;
    font-size: 18px;
    font-weight: 600;
    border-radius: 10px 10px 0 0;
    color: var(--tg-heading-color);
}
.blog__post-content-three .post-tag-two,
.faq__wrap-two .accordion-body p,
.shop-content .price del,
.shop-content .rating .avg,
.shop-details-bottom .list-wrap li .code,
.shop-details-bottom .list-wrap li a,
.shop-top-right select {
    color: var(--tg-body-color);
}
.faq__wrap-two .accordion-item .accordion-button::after {
    content: "\f105";
    font-family: flaticon_skill_grow;
    font-size: 20px;
    position: absolute;
    right: 20px;
    top: 21px;
    color: var(--tg-heading-color);
    box-shadow: none;
    background: 0 0;
    font-weight: 700;
}
.faq__wrap-two .accordion-item .accordion-button:not(.collapsed) {
    box-shadow: none;
    color: var(--tg-heading-color);
    background-color: var(--tg-theme-secondary);
}
.faq__wrap-two .accordion-item .accordion-button:not(.collapsed)::after {
    transform: rotate(-90deg);
    color: var(--tg-heading-color);
}
.faq__wrap-two .accordion-body {
    padding: 15px 25px 40px 24px;
}
.faq__shape img {
    position: absolute;
    z-index: -1;
    left: 5%;
    top: 16%;
    max-width: 65px;
}
@media (max-width: 1800px) {
    .faq__shape img {
        left: 3%;
        top: 6%;
    }
}
.curved-circle {
    font-size: 20px;
    font-weight: 700;
    text-transform: uppercase;
    color: var(--tg-common-color-dark);
    right: 50%;
    transform: translateX(-50%);
    top: -8%;
    letter-spacing: 5px;
}
.shop-top-left p {
    margin-bottom: 0;
    font-size: 18px;
}
@media (max-width: 767.98px) {
    .faq__wrap .accordion-item .accordion-button {
        padding: 22px 30px 22px 0;
    }
    .faq__wrap-two .accordion-item .accordion-button {
        padding: 22px 30px 22px 24px;
    }
    .faq__wrap-two .accordion-body {
        padding: 15px 25px 25px 24px;
    }
    .faq__shape img {
        left: 3%;
        top: 3%;
        max-width: 50px;
    }
    .curved-circle {
        display: none;
    }
    .shop-top-left p {
        margin-bottom: 15px;
        text-align: center;
    }
}
.shop-top-right {
    position: relative;
    width: 190px;
    margin-left: auto;
}
@media (max-width: 767.98px) {
    .shop-top-right {
        margin: 0 auto;
    }
}
.shop-top-right select {
    background-color: var(--tg-common-color-gray);
    border: none;
    font-weight: 400;
    font-size: 16px;
    text-transform: capitalize;
    border-radius: 4px;
    outline: 0;
    padding: 12px 37px 9px 20px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 100%;
    line-height: 1.2;
    height: 40px;
}
.shop-action a,
.shop-item {
    background: var(--tg-common-color-white);
}
.shop-top-right::after {
    content: "\f078";
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    font-family: "Font Awesome 5 Free";
    font-weight: 700;
    right: 15px;
    font-size: 14px;
    color: var(--tg-theme-primary);
}
.shop-item {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 25px 25px 30px;
    margin-bottom: 30px;
}
@media (max-width: 1199.98px) {
    .faq__content-three,
    .faq__content-two {
        width: 100%;
    }
    .curved-circle {
        font-size: 15px;
        top: -6%;
        letter-spacing: 3px;
    }
    .shop-item {
        padding: 20px 20px 25px;
    }
}
@media (max-width: 991.98px) {
    .faq__img-wrap {
        margin-top: 0;
    }
    .shop-item {
        padding: 25px 25px 35px;
    }
    .shop-sidebar {
        margin-top: 80px;
    }
}
.shop-item:hover .shop-thumb img {
    opacity: 0.8;
}
.shop-thumb {
    position: relative;
    text-align: center;
    margin-bottom: 25px;
}
.shop-thumb img {
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
}
.shop-thumb .flash-sale {
    position: absolute;
    left: 0;
    top: 0;
    line-height: 1;
    padding: 6px 13px;
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    border-radius: 4px;
    z-index: 1;
}
.blog__post-area::before,
.shop-action a {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
}
.shop-action {
    display: flex;
    align-items: center;
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 50%;
    gap: 10px;
}
.shop-action li {
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: visibility 0.3s ease-out 0.1s, opacity 0.3s ease-out 0.15s,
        transform 0.3s ease-out 0.1s;
}
.shop-action li:nth-child(2) {
    transition-delay: 0.15s;
}
.shop-action li:nth-child(3) {
    transition-delay: 0.2s;
}
.shop-action a {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: var(--tg-theme-primary);
    font-size: 15px;
    line-height: 0;
}
.shop-content .price {
    font-weight: 500;
    font-size: 18px;
    margin-bottom: 0;
    color: var(--tg-theme-primary);
}
.shop-content .price del {
    margin-left: 5px;
    opacity: 0.7;
}
.shop-content .title {
    margin-bottom: 10px;
    font-weight: 500;
    font-size: 18px;
}
.shop-content .rating {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    color: #f8bc24;
    gap: 3px;
    line-height: 1;
    margin: 0 0 10px;
}
.shop-widget {
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    -ms-box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    -o-box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -o-border-radius: 8px;
    -ms-border-radius: 8px;
    border-radius: 8px;
    padding: 30px;
    background: #f7f7fa;
    margin-bottom: 30px;
}
@media (max-width: 1199.98px) {
    .shop-widget {
        padding: 25px 20px;
    }
}
.shop-widget .form-check-input {
    border-radius: 4px;
    border: 1px solid #b2bdcd;
    margin: 0 10px 0 0;
    width: 16px;
    height: 16px;
}
.shop-details-images-wrap .nav-tabs {
    border-bottom: none;
    gap: 15px;
    margin: 0;
    width: 119px;
    flex: 0 0 119px;
    flex-direction: column;
}
.shop-details-images-wrap .nav-item {
    width: 120px;
    flex: 0 0 auto;
}
@media (max-width: 767.98px) {
    .shop-details-images-wrap .nav-item {
        width: auto;
        flex: 1 0 0%;
    }
    .shop-details-images-wrap .tab-content img {
        width: 100%;
    }
}
.shop-details-images-wrap .nav-link {
    margin-bottom: 0;
    background: 0 0;
    border: 1px solid #e4e4e4;
    border-radius: 6px;
    padding: 0;
    overflow: hidden;
    opacity: 0.5;
}
.shop-details-images-wrap .nav-link.active {
    border-color: var(--tg-theme-primary);
    opacity: 1;
}
.shop-details-images-wrap .tab-content img {
    border-radius: 6px;
    border: 1px solid #e4e4e4;
}
.shop-details-content {
    margin-left: 25px;
}
@media (max-width: 1199.98px) {
    .shop-details-content {
        margin-left: 0;
    }
}
.shop-details-content > .title {
    font-weight: 600;
    font-size: 30px;
    margin-bottom: 12px;
}
.shop-details-content .product-review {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}
.shop-details-content .product-review .rating {
    display: flex;
    align-items: center;
    color: #f8bc24;
    gap: 3px;
}
.shop-details-content .price {
    margin-bottom: 20px;
    font-weight: 500;
    font-size: 24px;
    color: var(--tg-theme-primary);
}
.shop-details-qty {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 40px;
}
.shop-details-qty .title {
    font-weight: 500;
    font-size: 18px;
    color: var(--tg-theme-primary);
    display: block;
}
.shop-details-qty .wishlist-btn {
    background: #f6f7fa;
    border: 1px solid #e4e4e4;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}
.shop-details-bottom .list-wrap li {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}
.shop-details-bottom .list-wrap li .title {
    font-weight: 500;
    font-size: 16px;
    display: block;
    color: var(--tg-heading-color);
}
.blog__post-content .title a:hover,
.blog__post-content-five .title a:hover,
.blog__post-content-four .title a:hover,
.blog__post-content-six .title a:hover,
.blog__post-content-three .title a:hover,
.blog__post-content-two .title a:hover {
    color: inherit;
    background-size: 0 2px, 100% 2px;
}
.cart-plus-minus {
    position: relative;
    width: 116px;
    flex: 0 0 auto;
}
.cart-plus-minus input {
    width: 100%;
    border: 1px solid #d7d7d7;
    border-radius: 5px;
    height: 50px;
    text-align: center;
    padding: 0 30px;
    font-weight: 500;
    font-size: 16px;
    color: var(--tg-heading-color);
}
.cart-plus-minus .qtybutton {
    position: absolute;
    top: 0;
    left: 0;
    width: 31px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 600;
    color: #838383;
    cursor: pointer;
    border-right: 1px solid #d7d7d7;
    user-select: none;
    -moz-user-select: none;
    line-height: 0;
}
.cart-plus-minus .qtybutton.inc {
    left: auto;
    right: 0;
    border-right: none;
    border-left: 1px solid #d7d7d7;
}
.cart-plus-minus .qtybutton.dec {
    font-size: 26px;
}
.product-desc-wrap {
    margin-top: 100px;
}
.product-desc-wrap .nav-tabs {
    border-bottom: 2px solid #e6eaef;
    gap: 30px;
    margin-bottom: 25px;
}
.product-desc-wrap .nav-tabs .nav-link {
    margin-bottom: 0;
    background: 0 0;
    border: none;
    border-radius: 0;
    padding: 0 5px 10px;
    font-weight: 500;
    font-size: 19px;
    color: var(--tg-body-color);
    position: relative;
}
.blog__details-bottom .tg-post-social .social-title,
.blog__details-bottom .tg-post-tag .tag-title {
    font-size: 17px;
    margin-bottom: 0;
}
.product-desc-wrap .nav-tabs .nav-link.active::before {
    opacity: 1;
}
.product-desc-wrap .nav-tabs .nav-link::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 100%;
    height: 2px;
    background: var(--tg-theme-primary);
    transition: 0.3s linear;
    opacity: 0;
}
.product-desc-review {
    padding: 25px 30px;
    border: 1px solid #e6eaef;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -o-border-radius: 5px;
    -ms-border-radius: 5px;
    border-radius: 5px;
}
.shop-active [class*="col-"] {
    padding: 0 15px;
}
.shop-active .slick-arrow {
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--tg-common-color-gray);
    color: var(--tg-theme-primary);
    border: 1px solid var(--tg-common-color-white);
    box-shadow: 0 10px 20px #dfdfdf;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
}
.blog__post-area::after,
.blog__post-area::before {
    width: 473px;
    height: 473px;
    background: #e9e2f9;
    filter: blur(200px);
    z-index: -1;
    position: absolute;
    content: "";
}
.shop-active .slick-arrow.slick-next {
    left: auto;
    right: -10px;
}
.blog__bg {
    background-size: cover;
    background-position: center;
    padding: 120px 0 90px;
    position: relative;
    z-index: 1;
}
.blog__post-area {
    position: relative;
    padding: 240px 0 90px;
    background: var(--tg-common-color-gray);
    z-index: 1;
    margin-top: -150px;
}
.blog__post-area::before {
    left: 50px;
    top: 50px;
    border-radius: 50%;
}
.blog__post-area::after {
    right: 10%;
    bottom: 20px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.blog__post-area-two {
    padding: 120px 0 90px;
    margin-top: 0;
}
.blog__post-area-four {
    position: relative;
    z-index: 1;
    overflow: hidden;
}
.blog__post-area-five {
    position: relative;
    z-index: 1;
}
.blog__post-area-seven,
.blog__post-area-six {
    background: var(--tg-common-color-gray-10);
}
.blog__post-item,
.blog__post-item-five,
.blog__post-item-four,
.blog__post-item-six,
.blog__post-item-three,
.blog__post-item-two {
    margin-bottom: 30px;
    background: var(--tg-common-color-white);
}
.blog__post-area-eight,
.sidebar-search-form form {
    position: relative;
}
.blog__post-item {
    border: 1px solid #b5b5c3;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    padding: 20px 25px 35px;
    -webkit-transition: 0.2s ease-out;
    -moz-transition: 0.2s ease-out;
    -ms-transition: 0.2s ease-out;
    -o-transition: 0.2s ease-out;
    transition: 0.2s ease-out;
}
.blog__post-item:hover {
    -webkit-box-shadow: 10px 10px 0 0 #cac9d6;
    -moz-box-shadow: 10px 10px 0 0 #cac9d6;
    -ms-box-shadow: 10px 10px 0 0 #cac9d6;
    -o-box-shadow: 10px 10px 0 0 #cac9d6;
    box-shadow: 10px 10px 0 0 #cac9d6;
}
.blog__post-item-two {
    position: relative;
    border: 1px solid var(--tg-border-6);
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    padding: 25px;
}
.blog__post-item-three {
    border: 1px solid var(--tg-border-6);
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    padding: 25px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 22px;
}
@media (max-width: 1199.98px) {
    .blog__post-item-three {
        padding: 22px 20px;
    }
}
.blog__post-item-four {
    border: 1px solid #e3e3e3;
    border-radius: 30px;
    padding: 30px 30px 35px;
}
@media (max-width: 1199.98px) {
    .blog__post-item-four {
        padding: 20px 20px 25px;
    }
}
.blog__post-item-five {
    box-shadow: 0 0 30px 0 #ebebeb;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
    overflow: hidden;
}
.blog__post-item-six {
    border-radius: 15px;
}
.blog-widget,
.dashboard__review-table tbody tr:nth-child(2n) td,
.sidebar-search-form input {
    background: var(--tg-common-color-gray);
}
.blog__post-thumb {
    margin-bottom: 22px;
    position: relative;
}
.blog__post-thumb img {
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
    width: 100%;
    height: 224px;
    object-fit: cover;
}
.blog__post-thumb .post-tag {
    font-size: 13px;
    font-weight: 500;
    line-height: 1;
    color: var(--tg-common-color-white);
    background: var(--tg-theme-primary);
    position: absolute;
    left: 22px;
    top: 17px;
    padding: 9px 14px;
    border-radius: 30px;
    z-index: 1;
}
.blog__post-thumb .post-tag:hover {
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
}
.blog__post-thumb-two {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}
.blog__post-thumb-two img {
    width: 100%;
    height: 462px;
    object-fit: cover;
}
.blog__post-thumb-two::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        180deg,
        rgba(22, 20, 57, 0) 0,
        rgba(22, 20, 57, 0) 0,
        #010005 100%
    );
    pointer-events: none;
}
.blog__post-thumb-three {
    width: 283px;
    flex: 0 0 auto;
}
@media (max-width: 1199.98px) {
    .blog__post-thumb-three {
        width: 200px;
    }
}
.blog__post-thumb-three img {
    width: 100%;
    height: 190px;
    object-fit: cover;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
}
.blog__details-thumb,
.blog__post-thumb-four {
    margin-bottom: 30px;
}
.blog__post-thumb-four img {
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    -o-border-radius: 20px;
    -ms-border-radius: 20px;
    border-radius: 20px;
    width: 100%;
    height: 290px;
    object-fit: cover;
}
@media (max-width: 1199.98px) {
    .blog__post-thumb-four img {
        height: 260px;
    }
}
.blog__post-thumb-five img {
    width: 100%;
    height: 290px;
    object-fit: cover;
}
.blog__post-thumb-six {
    position: relative;
    border-radius: 15px 15px 0 0;
    overflow: hidden;
}
.blog__post-thumb-six img {
    width: 100%;
    height: 290px;
    object-fit: cover;
}
.blog__post-thumb-six .post-tag-four {
    position: absolute;
    left: 30px;
    top: 30px;
    margin-bottom: 0;
    z-index: 1;
}
.blog__post-content .title {
    margin-bottom: 0;
    font-size: 20px;
    font-weight: 600;
}
.blog__post-content .title a {
    display: inline;
    background-image: linear-gradient(
            var(--tg-heading-color),
            var(--tg-heading-color)
        ),
        linear-gradient(var(--tg-heading-color), var(--tg-heading-color));
    background-size: 0 2px, 0 2px;
    background-position: 100% 100%, 0 100%;
    background-repeat: no-repeat;
    transition: background-size 0.4s linear;
}
.blog__post-content-two {
    position: absolute;
    left: 25px;
    right: 25px;
    bottom: 25px;
    padding: 30px 40px;
}
@media (max-width: 1199.98px) {
    .blog__post-content-two {
        padding: 30px 20px;
    }
}
.blog__post-content-two .title {
    margin-bottom: 0;
    color: var(--tg-common-color-white);
    font-size: 26px;
}
.blog-widget .shop-cat-list .list-wrap li a:hover,
.blog__post-content-six .btn,
.blog__post-content-six .btn svg,
.blog__post-meta .list-wrap li a:hover,
.contact-info-wrap .list-wrap li .content a:hover {
    color: var(--tg-theme-primary);
}
@media (max-width: 1199.98px) {
    .blog__post-content-two .title {
        font-size: 24px;
    }
}
.blog__post-content-two .title a {
    display: inline;
    background-image: linear-gradient(
            var(--tg-common-color-white),
            var(--tg-common-color-white)
        ),
        linear-gradient(
            var(--tg-common-color-white),
            var(--tg-common-color-white)
        );
    background-size: 0 2px, 0 2px;
    background-position: 100% 100%, 0 100%;
    background-repeat: no-repeat;
    transition: background-size 0.4s linear;
}
.blog__post-content-five .title a,
.blog__post-content-four .title a,
.blog__post-content-six .title a,
.blog__post-content-three .title a {
    display: inline;
    background-image: linear-gradient(
            var(--tg-heading-color),
            var(--tg-heading-color)
        ),
        linear-gradient(var(--tg-heading-color), var(--tg-heading-color));
    background-size: 0 2px, 0 2px;
    background-position: 100% 100%, 0 100%;
    background-repeat: no-repeat;
    transition: background-size 0.4s linear;
}
.blog__post-content-three .title {
    margin-bottom: 0;
    font-size: 20px;
}
@media (max-width: 1199.98px) {
    .blog__post-content-three .title {
        font-size: 18px;
    }
}
.blog__post-content-four .title {
    margin-bottom: 20px;
    font-size: 22px;
    font-weight: 600;
}
.blog__post-content-five {
    padding: 30px 35px 40px;
}
@media (max-width: 1199.98px) {
    .blog__post-content-four .title {
        font-size: 20px;
    }
    .blog__post-content-five {
        padding: 30px 22px;
    }
}
.blog__post-content-five .title {
    margin-bottom: 25px;
    font-size: 22px;
    font-weight: 600;
    text-transform: capitalize;
}
.blog-widget .shop-cat-list .list-wrap li:last-child,
.blog-widget:last-child,
.blog__post-content-five .blog__post-meta {
    margin-bottom: 0;
}
.blog__post-content-five .blog__post-meta ul li {
    font-size: 15px;
}
.blog__post-content-six {
    border: 1px solid #e3e3e3;
    border-top: none;
    border-radius: 0 0 15px 15px;
    padding: 30px 30px 35px;
}
.blog__post-content-six .title {
    font-size: 22px;
    text-transform: capitalize;
    margin-bottom: 22px;
}
.blog-widget .tagcloud,
.blog__post-meta .list-wrap,
.blog__post-meta .list-wrap li {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.blog__post-content-six .btn {
    box-shadow: none;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    background: #eeedff;
}
.blog-widget .tagcloud a:hover,
.blog__post-content-six .btn:hover,
.latest-comments .comments-text .comment-reply .comment-reply-link:hover,
.post-tag-four:hover,
.post-tag-three:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.blog__post-content-six .btn:hover svg {
    color: var(--tg-common-color-white);
}
.blog__post-meta {
    margin-bottom: 20px;
}
.blog__post-meta .list-wrap {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}
.blog__post-meta .list-wrap li {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    line-height: 1;
}
.blog__post-meta .list-wrap li i {
    font-size: 20px;
    color: var(--tg-theme-primary);
    margin-right: 8px;
}
.blog__post-meta .list-wrap li a {
    color: var(--tg-body-color);
    margin-left: 5px;
}
.blog__post-meta-two {
    margin-bottom: 14px;
}
.blog__post-meta-two .list-wrap li {
    color: var(--tg-common-color-gray-6);
}
.blog__post-meta-two .list-wrap li i {
    color: var(--tg-common-color-white);
}
.blog__post-meta-two .list-wrap li a {
    color: var(--tg-common-color-gray-6);
}
.blog-sidebar {
    margin-left: 15px;
}
.blog-sidebar-two {
    margin-left: 0;
    margin-right: 15px;
}
.blog-widget {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 30px;
}
.blog-widget .shop-cat-list .list-wrap li {
    line-height: 1;
    margin-bottom: 20px;
}
.blog-widget .shop-cat-list .list-wrap li a {
    display: inline-flex;
    align-items: center;
    line-height: 1;
    font-size: 16px;
    color: var(--tg-body-color);
    gap: 5px;
}
.blog-widget .shop-cat-list .list-wrap li a i {
    color: var(--tg-theme-primary);
    font-size: 20px;
    line-height: 0;
}
.blog-widget .tagcloud {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 7px;
}
.app-download,
.blog__details-bottom .tg-post-tag,
.blog__details-bottom .tg-post-tag .list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.blog-widget .tagcloud a {
    font-size: 16px;
    color: var(--tg-theme-primary);
    display: block;
    background: var(--tg-common-color-white);
    padding: 10px 15px;
    line-height: 1;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -o-border-radius: 3px;
    -ms-border-radius: 3px;
    border-radius: 3px;
}
.blog__shape img {
    position: absolute;
    right: -3%;
    top: 7%;
    z-index: -1;
}
.blog__shape-wrap img:first-child {
    right: -204px;
    top: -198px;
    -webkit-animation-duration: 70s;
    animation-duration: 70s;
    max-width: 428px;
}
.blog__shape-wrap img:nth-child(2) {
    bottom: -220px;
    left: -150px;
    -webkit-animation-duration: 70s;
    animation-duration: 70s;
    max-width: 428px;
}
.blog__shape-wrap-two img:first-child {
    left: 0;
    top: 18%;
    max-width: 219px;
}
.blog__shape-wrap-two img:nth-child(2) {
    right: 5%;
    bottom: 11%;
    max-width: 65px;
}
.blog__shape-wrap-three img:first-child {
    right: 5%;
    top: 10%;
    max-width: 104px;
}
.blog__shape-wrap-three img:nth-child(2) {
    left: 4%;
    bottom: 3%;
    max-width: 118px;
}
.blog__details-thumb img {
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    min-height: 300px;
    object-fit: cover;
}
.blog__details-content .title {
    margin-bottom: 10px;
    font-size: 36px;
}
.blog__details-content .inner-title {
    margin-bottom: 10px;
    font-size: 30px;
}
.blog__details-content-inner {
    margin: 30px 0 25px;
}
.account__form .form-grp,
.blog__details-content-inner > p,
.contact-form-wrap .form-grp {
    margin-bottom: 20px;
}
.blog__details-bottom {
    border-top: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    padding: 20px 0;
    margin: 40px 0 30px;
}
.blog__details-bottom .tg-post-tag {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}
.app-download,
.blog__details-bottom .tg-post-tag .list-wrap {
    display: flex;
    gap: 8px;
}
.blog__details-bottom .tg-post-social,
.blog__details-bottom .tg-post-social .list-wrap {
    align-items: center;
    gap: 10px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.blog__details-bottom .tg-post-tag .list-wrap li a {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    display: block;
    font-size: 15px;
    -webkit-border-radius: 50px;
    -moz-border-radius: 50px;
    -o-border-radius: 50px;
    -ms-border-radius: 50px;
    border-radius: 50px;
    padding: 10px 21px;
    line-height: 1.2;
}
.blog__details-bottom .tg-post-tag .list-wrap li a:hover {
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
}
.blog__details-bottom .tg-post-social {
    display: flex;
}
.blog__details-bottom .tg-post-social .list-wrap {
    display: flex;
}
.blog__details-bottom .tg-post-social .list-wrap li a {
    font-size: 20px;
    color: var(--tg-body-color);
}
.blog__details-bottom .tg-post-social .list-wrap li a:hover {
    color: var(--tg-theme-secondary);
}
.blog__post-author {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    padding: 41px 45px;
    background: #f7f7fa;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    gap: 30px;
    margin-bottom: 40px;
}
.blog__post-author-thumb {
    width: 115px;
    flex: 0 0 auto;
}
.blog__post-author-thumb img,
.latest-comments .comments-avatar img {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
}
.blog__post-author-content .designation {
    display: block;
    line-height: 1;
    color: var(--tg-common-color-dark);
    margin-bottom: 5px;
}
.rc-post-content .date,
.rc-post-item {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.blog__post-author-content .name {
    font-size: 24px;
    margin-bottom: 10px;
    font-weight: 500;
}
.blog__post-author-content p,
.rc-post-item:last-child {
    margin-bottom: 0;
}
.widget-title {
    margin-bottom: 20px;
    font-size: 20px;
}
.sidebar-search-form input {
    width: 100%;
    border: none;
    padding: 15px 45px 15px 20px;
    color: var(--tg-body-color);
    font-size: 16px;
    line-height: 1;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
}
.sidebar-search-form input::placeholder {
    color: var(--tg-body-color);
    font-size: 16px;
    opacity: 0.8;
}
.sidebar-search-form button {
    border: none;
    padding: 0;
    background: 0 0;
    font-size: 22px;
    line-height: 0;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
}
.rc-post-item {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}
.rc-post-thumb {
    width: 74px;
    flex: 0 0 auto;
}
.rc-post-thumb img {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -o-border-radius: 3px;
    -ms-border-radius: 3px;
    border-radius: 3px;
}
.rc-post-content .date {
    display: flex;
    gap: 6px;
    line-height: 1;
    font-size: 13px;
    font-weight: 500;
    color: var(--tg-common-color-gray-3);
    margin-bottom: 12px;
}
.newsletter__content .title,
.newsletter__form form input,
.newsletter__inner-wrap .title,
.post-tag-two {
    color: var(--tg-common-color-white);
}
.rc-post-content .date i {
    color: var(--tg-theme-primary);
    font-size: 14px;
}
.rc-post-content .title {
    font-size: 14px;
    font-weight: 500;
    text-transform: capitalize;
    margin-bottom: 0;
}
blockquote {
    background: #efeefe;
    margin: 40px 90px;
    padding: 35px 120px 35px 45px;
    position: relative;
    z-index: 1;
}
@media (max-width: 1500px) {
    .shop-details-images-wrap .nav-tabs {
        width: 100%;
        flex: 0 0 auto;
        flex-direction: row;
    }
    .blog__shape-wrap-two img:first-child {
        top: 12%;
    }
    .blog__shape-wrap-two img:nth-child(2) {
        bottom: 5%;
    }
    .blog__shape-wrap-three img:nth-child(2) {
        max-width: 90px;
    }
    .blog__details-content .title {
        font-size: 32px;
    }
    blockquote {
        margin: 40px 50px;
    }
}
blockquote::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 6px;
    height: 100%;
    background: var(--tg-theme-primary);
}
.newsletter__form-two input,
.newsletter__inner-wrap .btn,
.video__play-btn a {
    background: var(--tg-common-color-white);
}
blockquote::after {
    content: "\f10e";
    position: absolute;
    right: 30px;
    top: 22px;
    line-height: 1;
    font-size: 62px;
    color: #ceccfd;
    font-family: var(--tg-icon-font-family);
    font-weight: 700;
    z-index: -1;
}
.customer__form-wrap .title,
.dashboard__counter-item .content .count,
.instructor__profile-form .form-grp label,
.lesson__video-wrap-top-left span,
.pagination__wrap ul li a,
blockquote > p {
    font-family: var(--tg-heading-font-family);
}
blockquote > p {
    margin-bottom: 0;
    font-size: 20px;
    font-weight: 500;
    font-style: italic;
    color: var(--tg-common-color-dark);
}
.comment-wrap-title .title {
    margin-bottom: 50px;
    font-size: 24px;
}
.comment-respond {
    background: #f7f7fa;
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    -o-border-radius: 12px;
    -ms-border-radius: 12px;
    border-radius: 12px;
    padding: 32px 45px 50px;
}
.comment-reply-title {
    margin-bottom: 5px;
    font-size: 30px;
    text-transform: capitalize;
}
@media (max-width: 1199.98px) {
    .blog__post-content-six {
        padding: 25px 20px 30px;
    }
    .blog-sidebar {
        margin-left: 0;
    }
    .blog-sidebar-two {
        margin-right: 0;
    }
    .blog__shape-wrap img:nth-child(2) {
        max-width: 380px;
    }
    .blog__shape-wrap-three img:first-child {
        top: 6%;
        max-width: 80px;
    }
    .blog__details-content .title {
        font-size: 30px;
    }
    .blog__post-author {
        padding: 30px 25px;
    }
    blockquote {
        margin: 40px 20px;
        padding: 35px 100px 35px 30px;
    }
    blockquote > p {
        font-size: 18px;
    }
    .comment-respond {
        padding: 32px 25px 50px;
    }
    .comment-reply-title {
        font-size: 26px;
    }
}
.comment-form .row [class*="col-"] {
    padding: 0 10px;
}
.comment-field input,
.comment-field textarea {
    width: 100%;
    display: block;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -o-border-radius: 5px;
    -ms-border-radius: 5px;
    border-radius: 5px;
    background: var(--tg-common-color-white);
    border: 1px solid #e1e4e7;
    font-size: 16px;
    color: var(--tg-body-color);
    padding: 15px;
    line-height: 1.2;
    height: 50px;
}
.comment-field input::placeholder,
.comment-field textarea::placeholder {
    opacity: 0.8;
    font-size: 16px;
    color: var(--tg-body-color);
}
.comment-field textarea {
    min-height: 130px;
    max-height: 130px;
}
.comment-field.checkbox-grp {
    display: flex;
    align-items: center;
    margin: 15px 0 32px;
}
.latest-comments .comments-box,
.latest-comments .comments-text .avatar-name {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.comment-field.checkbox-grp input {
    width: 22px;
    margin-right: 9px;
    height: 22px;
    cursor: pointer;
}
.comment-field.checkbox-grp label {
    font-weight: 400;
    font-size: 16px;
    color: var(--tg-body-color);
    user-select: none;
}
.latest-comments .list-wrap li {
    padding-bottom: 30px;
    margin-bottom: 45px;
    border-bottom: 1px solid #e8e8e8;
}
.latest-comments .list-wrap li:last-child {
    margin-bottom: 0;
    border-bottom: none;
}
.latest-comments .list-wrap li:last-child .comments-box {
    margin-left: 45px;
}
.latest-comments .comments-box {
    display: flex;
    align-items: flex-start;
    gap: 25px;
}
.latest-comments .comments-avatar {
    flex: 0 0 auto;
    width: 115px;
}
.latest-comments .comments-text .avatar-name {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
.pagination__wrap ul,
.pagination__wrap ul li a {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.latest-comments .comments-text .avatar-name .date {
    font-size: 14px;
    margin-left: auto;
    font-weight: 500;
}
.latest-comments .comments-text .comment-reply .comment-reply-link {
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    background: #f7f7fb;
    display: inline-block;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    line-height: 1;
    padding: 10px 18px;
}
.pagination__wrap ul {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px 10px;
}
.pagination__wrap ul li a {
    display: flex;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: #e6e9ef;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    font-size: 18px;
    color: var(--tg-heading-color);
    font-weight: var(--tg-fw-medium);
}
.pagination__wrap ul li a:hover,
.pagination__wrap ul li.active a {
    color: var(--tg-common-color-white);
    background: var(--tg-theme-primary);
}
.post-tag-two {
    font-weight: 500;
    font-size: 13px;
    display: inline-block;
    line-height: 1;
    border: 1px solid #e4e4e4;
    border-radius: 100px;
    padding: 8px 13px;
    margin-bottom: 20px;
}
.post-tag-four,
.post-tag-three {
    font-size: 13px;
    display: inline-block;
    line-height: 1;
    padding: 8px 14px;
    background: var(--tg-theme-secondary);
    color: var(--tg-heading-color);
    font-weight: 500;
}
.post-tag-three {
    border-radius: 100px;
    margin-bottom: 15px;
}
.post-tag-four {
    border-radius: 4px;
    margin-bottom: 18px;
}
.newsletter__area {
    background: var(--tg-theme-primary);
    position: relative;
    padding: 35px 0 0;
    z-index: 1;
    overflow: hidden;
}
.newsletter__area-two {
    background: var(--tg-common-color-gray-10);
}
.newsletter__inner-wrap {
    background: var(--tg-theme-primary);
    border-radius: 20px;
    padding: 66px 90px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
    position: relative;
    z-index: 1;
    overflow: hidden;
}
@media (max-width: 1500px) {
    .newsletter__inner-wrap {
        padding: 66px 70px;
    }
}
@media (max-width: 991.98px) {
    .shop-widget {
        padding: 25px 30px;
    }
    .shop-details-content {
        margin: 60px 0 0;
    }
    .blog__post-item-three {
        padding: 25px;
    }
    .blog__post-thumb-three {
        width: 280px;
    }
    .blog__post-content-two {
        padding: 30px;
    }
    .blog__post-content-two .title {
        font-size: 26px;
    }
    .blog__post-content-three .title {
        font-size: 20px;
    }
    .blog-sidebar {
        margin-top: 80px;
    }
    .blog__shape img {
        display: none;
    }
    .blog__shape-wrap-two img:first-child {
        top: 4%;
    }
    .newsletter__area {
        padding: 40px 0;
    }
    .newsletter__inner-wrap {
        flex-direction: column;
        text-align: center;
    }
}
@media (max-width: 767.98px) {
    .blog__details-content .title,
    .shop-details-content > .title {
        font-size: 26px;
    }
    .shop-details-content .product-review {
        margin-bottom: 15px;
    }
    .shop-details-content p {
        margin-bottom: 30px;
    }
    .related-product-area {
        margin-top: 90px;
    }
    .blog__post-area {
        padding: 220px 0 70px;
    }
    .blog__post-area-two {
        padding: 100px 0 70px;
    }
    .blog__post-content-two,
    .blog__post-item-two {
        padding: 20px;
    }
    .blog__post-item-three {
        padding: 20px;
        flex-direction: column;
    }
    .blog__post-thumb img,
    .blog__post-thumb-five img,
    .blog__post-thumb-six img {
        height: auto;
    }
    .blog__post-thumb-two img {
        height: 420px;
    }
    .blog__post-thumb-three {
        width: 100%;
    }
    .blog__post-thumb-three img {
        height: 240px;
    }
    .blog__details-content .inner-title,
    .blog__post-content-two .title {
        font-size: 24px;
    }
    .blog__shape-wrap img:first-child {
        max-width: 360px;
    }
    .blog__shape-wrap img:nth-child(2) {
        max-width: 340px;
    }
    .blog__shape-wrap-two img:first-child {
        display: none;
    }
    .blog__shape-wrap-two img:nth-child(2) {
        bottom: 2%;
        max-width: 50px;
    }
    .blog__shape-wrap-three img:first-child {
        top: 2%;
        max-width: 60px;
    }
    .blog__shape-wrap-three img:nth-child(2) {
        max-width: 70px;
        bottom: 1%;
    }
    .blog__details-bottom .tg-post-social {
        margin-top: 15px;
    }
    .blog__post-author {
        flex-wrap: wrap;
        gap: 25px;
    }
    blockquote {
        margin: 40px 0;
        padding: 30px;
    }
    blockquote::after {
        right: 17px;
        top: 17px;
        font-size: 40px;
    }
    .latest-comments .list-wrap li:last-child .comments-box {
        margin-left: 0;
    }
    .latest-comments .comments-box {
        gap: 20px;
        flex-wrap: wrap;
    }
    .newsletter__inner-wrap {
        padding: 40px 25px;
    }
}
.newsletter__inner-wrap .title {
    margin-bottom: 0;
    font-size: 30px;
    font-weight: 700;
    text-transform: capitalize;
    width: 44%;
    flex: 0 0 auto;
}
@media (max-width: 1199.98px) {
    .newsletter__inner-wrap .title {
        width: 60%;
        font-size: 28px;
    }
    .newsletter__inner-wrap-two {
        flex-wrap: wrap;
    }
    .newsletter__inner-wrap-two .title {
        width: 100%;
        text-align: center;
    }
}
.newsletter__inner-wrap .btn {
    border: none;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    box-shadow: none;
}
.newsletter__form form .btn,
.newsletter__form-two .btn,
.newsletter__inner-wrap .btn:hover,
.video__play-btn a:hover {
    background: var(--tg-theme-secondary);
}
.newsletter__inner-wrap .btn:hover {
    color: var(--tg-heading-color);
}
.newsletter__inner-wrap .btn:hover svg {
    color: var(--tg-heading-color) !important;
}
.newsletter__inner-wrap .shape {
    position: absolute;
    z-index: -1;
}
.newsletter__inner-wrap .shape.shape-one {
    left: 0;
    top: 0;
    max-width: 198px;
}
.newsletter__inner-wrap .shape.shape-two {
    right: 5%;
    top: 14%;
    max-width: 138px;
    -webkit-animation-duration: 30s;
    animation-duration: 30s;
}
.newsletter__btn {
    flex-grow: 1;
    text-align: right;
}
.newsletter__form form,
.newsletter__form-two,
.work__list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.newsletter__img-wrap img:not(:first-child) {
    position: absolute;
}
.newsletter__img-wrap img:nth-child(2) {
    left: 5%;
    bottom: 0;
    z-index: -1;
}
.newsletter__img-wrap img:nth-child(3) {
    right: 16%;
    top: 18%;
    z-index: -1;
}
.newsletter__content .title {
    margin-bottom: 25px;
    font-size: 36px;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 1.2;
}
.newsletter__content .title span {
    font-weight: 700;
}
.newsletter__form form {
    display: flex;
    align-items: center;
    gap: 10px;
}
.newsletter__form form input {
    width: 100%;
    border: 1px solid #433ec2;
    background: #4a44d1;
    -webkit-border-radius: 50px;
    -moz-border-radius: 50px;
    -o-border-radius: 50px;
    -ms-border-radius: 50px;
    border-radius: 50px;
    font-size: 16px;
    padding: 18px 20px;
    line-height: 1.4;
    height: 60px;
    flex-grow: 1;
}
.newsletter__form form input::placeholder {
    color: #9e9bf1;
    font-size: 16px;
    text-transform: capitalize;
}
.newsletter__form form .btn {
    min-width: 198px;
    border: 1px solid #141109;
    padding: 20px 30px;
    color: var(--tg-heading-color);
    box-shadow: 4px 6px 0 0 #3d3d3d;
}
.newsletter__form form .btn:hover {
    box-shadow: none;
}
.newsletter__form-two {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-grow: 1;
}
.newsletter__form-two input {
    width: 100%;
    border: none;
    border-radius: 6px;
    color: var(--tg-heading-color);
    font-weight: 500;
    font-size: 16px;
    padding: 16px 25px;
    height: 60px;
}
.newsletter__form-two .btn {
    color: var(--tg-heading-color);
    min-width: 180px;
    padding: 21px 30px;
}
.newsletter__form-two .btn:hover,
.video__content .title,
.video__content p,
.work__list-box .icon {
    color: var(--tg-common-color-white);
}
.newsletter__form-two .btn:hover {
    background: var(--tg-heading-color);
}
.newsletter__shape img {
    position: absolute;
    right: 14%;
    top: 0;
    z-index: -1;
}
.work__images {
    margin: 0 auto;
    text-align: center;
}
.work__images .shape {
    right: 220px;
}
.work__content {
    width: 85%;
}
.singUp-wrap p,
.work__content > p {
    margin-bottom: 25px;
}
.work__list-wrap {
    display: flex;
    align-items: center;
    margin: 0 -15px;
}
.work__list-box {
    width: 50%;
    flex: 0 0 auto;
    padding: 0 15px;
    margin-bottom: 30px;
}
.work__list-box-top {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 18px;
}
.account__social-btn,
.contact-info-wrap .list-wrap li,
.video__play-btn a,
.work__list-box .icon {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.work__list-box-top .title {
    font-size: 20px;
    margin-bottom: 0;
}
.work__list-box .icon {
    width: 60px;
    height: 60px;
    flex: 0 0 auto;
    align-items: center;
    justify-content: center;
    display: flex;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    background: #f66;
    border: 1px solid #ce3b3b;
    font-size: 35px;
    line-height: 0;
    -webkit-box-shadow: 5px 5px 0 0 rgba(0, 0, 0, 0.25);
    -moz-box-shadow: 5px 5px 0 0 rgba(0, 0, 0, 0.25);
    -ms-box-shadow: 5px 5px 0 0 rgba(0, 0, 0, 0.25);
    -o-box-shadow: 5px 5px 0 0 rgba(0, 0, 0, 0.25);
    box-shadow: 5px 5px 0 0 rgba(0, 0, 0, 0.25);
}
.video__bg,
.video__bg::before {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
}
.dashboard__content-wrap,
.dashboard__sidebar-wrap {
    box-shadow: 0 6px 34px rgba(215, 216, 222, 0.41);
    overflow: hidden;
}
.contact-info-wrap .list-wrap li:last-child,
.work__list-box p {
    margin-bottom: 0;
}
.work__list-box:nth-child(2) .icon {
    background: #1bcbe3;
    border: 1px solid #019aaf;
}
.video__area {
    position: relative;
    padding: 160px 0;
}
@media (max-width: 1199.98px) {
    .newsletter__img-wrap img:nth-child(2) {
        left: 15%;
    }
    .newsletter__img-wrap img:nth-child(3) {
        right: 5%;
    }
    .newsletter__content .title {
        font-size: 34px;
    }
    .newsletter__form,
    .work__content {
        width: 100%;
    }
    .newsletter__shape img {
        display: none;
    }
    .work__list-box-top {
        display: block;
    }
    .work__list-box .icon {
        margin-bottom: 20px;
    }
    .video__area {
        padding: 150px 0;
    }
}
.video__bg {
    position: absolute;
    background-position: center;
    background-size: cover;
    background-attachment: fixed;
}
.video__bg::before {
    content: "";
    position: absolute;
    background: var(--tg-common-color-black-3);
    opacity: 0.3;
}
.video__content {
    margin-left: 90px;
}
.video__content .title {
    font-size: 48px;
    font-weight: 700;
    text-transform: capitalize;
    margin-bottom: 15px;
}
@media (max-width: 1500px) {
    .newsletter__shape img {
        right: 0;
    }
    .video__content .title {
        font-size: 45px;
    }
    .error-area {
        padding: 100px 0;
    }
}
@media (max-width: 1199.98px) {
    .video__content {
        margin-left: 0;
    }
    .video__content .title {
        font-size: 42px;
    }
}
.video__content p {
    margin-bottom: 30px;
    text-transform: capitalize;
}
.video__play-btn {
    margin-left: 180px;
}
.video__play-btn a {
    width: 76px;
    height: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    font-size: 25px;
    color: var(--tg-common-color-black-3);
    line-height: 0;
    position: relative;
}
.video__play-btn a::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    width: 100%;
    height: 100%;
    border: 1px solid var(--tg-common-color-white);
    border-radius: 50%;
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    animation: 1.5s ease-out infinite pulse-border;
}
.contact-info-wrap .list-wrap li {
    display: flex;
    align-items: center;
    padding: 40px;
    margin-bottom: 30px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    border: 1px solid #e1e1e1;
    background: #f7f7fa;
    gap: 25px;
}
@media (max-width: 1199.98px) {
    .contact-info-wrap .list-wrap li {
        padding: 25px;
        gap: 15px;
        flex-wrap: wrap;
    }
}
.contact-info-wrap .list-wrap li .icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    font-size: 20px;
    line-height: 0;
    flex: 0 0 auto;
}
.contact-info-wrap .list-wrap li .content .title {
    font-size: 20px;
    margin-bottom: 5px;
}
.contact-info-wrap .list-wrap li .content p {
    margin-bottom: 0;
    font-weight: 500;
    line-height: 1.4;
}
.contact-info-wrap .list-wrap li .content a {
    display: block;
    font-weight: 500;
    color: var(--tg-body-color);
    line-height: 1.4;
}
.contact-form-wrap {
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    -o-border-radius: 12px;
    -ms-border-radius: 12px;
    border-radius: 12px;
    border: 1px solid #e1e1e1;
    background: #f7f7fa;
    padding: 30px 40px 42px;
}
@media (max-width: 1199.98px) {
    .contact-form-wrap {
        padding: 30px 25px 40px;
    }
}
@media (max-width: 991.98px) {
    .newsletter__inner-wrap .title {
        width: 100%;
    }
    .newsletter__img-wrap {
        display: none;
    }
    .newsletter__content,
    .video__content {
        text-align: center;
    }
    .work__images {
        margin-bottom: 50px;
    }
    .video__area {
        padding: 140px 0;
    }
    .video__content .title {
        font-size: 40px;
    }
    .video__play-btn {
        margin: 0 0 50px;
    }
    .video__play-btn a {
        margin: 0 auto;
    }
    .contact-info-wrap .list-wrap li {
        flex-wrap: nowrap;
    }
    .contact-form-wrap {
        padding: 30px 30px 40px;
        margin-top: 60px;
    }
    .order__info-wrap {
        margin-top: 50px;
    }
}
@media (max-width: 767.98px) {
    .newsletter__inner-wrap .title {
        font-size: 26px;
    }
    .newsletter__inner-wrap .shape.shape-two {
        display: none;
    }
    .newsletter__form form,
    .newsletter__form-two {
        flex-wrap: wrap;
        justify-content: center;
    }
    .work__images .shape {
        right: 88px;
    }
    .contact-info-wrap .list-wrap li {
        flex-direction: column;
        align-items: flex-start;
        flex-direction: row;
        align-items: center;
    }
    .contact-form-wrap {
        padding: 25px 20px 30px;
    }
}
.contact-form-wrap .title {
    margin-bottom: 5px;
    position: relative;
    font-size: 30px;
}
.contact-form-wrap .form-grp input,
.contact-form-wrap .form-grp textarea {
    width: 100%;
    border: 1px solid #e1e4e7;
    border-radius: 5px;
    display: block;
    background: var(--tg-common-color-white);
    font-weight: 400;
    font-size: 16px;
    color: var(--tg-body-color);
    padding: 11px 20px;
    height: 50px;
    transition: 0.3s;
}
.contact-form-wrap .form-grp input::placeholder,
.contact-form-wrap .form-grp textarea::placeholder {
    font-weight: 400;
    font-size: 16px;
    color: var(--tg-body-color);
    opacity: 0.8;
}
.contact-form-wrap .form-grp textarea {
    min-height: 230px;
    max-height: 230px;
}
.contact-map {
    width: 100%;
    height: 555px;
    margin-top: 70px;
}
@media (max-width: 1199.98px) {
    .contact-form-wrap .title {
        font-size: 28px;
    }
    .contact-map {
        height: 430px;
    }
}
.contact-map iframe {
    width: 100%;
    height: 100%;
    filter: grayscale(1);
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    -o-border-radius: 12px;
    -ms-border-radius: 12px;
    border-radius: 12px;
}
.ajax-response.error,
.ajax-response.success {
    margin: 20px 0 0 !important;
}
.ajax-response.error {
    color: red;
}
.ajax-response.success {
    color: green;
}
.singUp-wrap {
    background: #f7f7fa;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 30px 50px 40px;
}
@media (max-width: 767.98px) {
    .contact-form-wrap .title {
        font-size: 24px;
    }
    .contact-map {
        height: 380px;
    }
    .singUp-wrap {
        padding: 30px 20px;
    }
}
.singUp-wrap .title {
    font-size: 36px;
    margin-bottom: 10px;
}
.account__social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-common-color-white);
    border: 1px solid #e1e1e1;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -o-border-radius: 5px;
    -ms-border-radius: 5px;
    border-radius: 5px;
    font-size: 16px;
    gap: 10px;
    color: var(--tg-common-color-dark);
    padding: 10px 20px;
}
.account__social-btn:hover {
    color: var(--tg-theme-primary);
    border-color: var(--tg-theme-primary);
}
.account__divider {
    position: relative;
    text-align: center;
    padding-block-end: 35px;
    margin-block-start: 35px;
}
.account__divider::before {
    content: "";
    height: 1px;
    width: 100%;
    left: 0;
    top: 0;
    background: #e1e1e1;
    position: absolute;
}
.account__divider span {
    font-size: 16px;
    color: var(--tg-heading-color);
    padding: 20px;
    top: -35px;
    left: 45%;
    position: absolute;
    background: #f7f7fa;
}
.account__form .form-grp label {
    font-size: 18px;
    font-weight: 600;
    line-height: 1;
    margin-bottom: 10px;
}
.account__form .form-grp input {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
    color: var(--tg-heading-color);
    border: 1px solid #e1e1e1;
    background: var(--tg-common-color-white);
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -o-border-radius: 5px;
    -ms-border-radius: 5px;
    border-radius: 5px;
    line-height: 1;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    transition: 0.3s ease-out;
}
.account__check-forgot a,
.account__check-forgot a:hover,
.account__switch p a,
.coupon__code-info a:hover {
    color: var(--tg-theme-primary);
}
.account__form .form-grp input::placeholder {
    font-weight: 400;
    font-size: 16px;
    color: var(--tg-body-color);
    opacity: 0.8;
    text-transform: capitalize;
}
.account__form .form-grp input:focus {
    border-color: var(--tg-theme-primary);
}
.account__form .btn {
    width: 100%;
    justify-content: center;
    margin-top: 30px;
}
.account__check,
.account__switch p {
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.account__switch {
    margin-top: 30px;
}
.account__switch p {
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    gap: 5px;
}
.account__switch p a:hover {
    color: var(--tg-theme-secondary);
}
.account__check {
    display: flex;
    gap: 15px;
    justify-content: space-between;
}
.account__check-remember {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    gap: 5px;
}
.account__check-remember input {
    width: 20px;
    height: 20px;
    margin-inline-end: 3px;
}
.account__check-remember input:focus {
    box-shadow: none;
}
.account__check-remember input:checked {
    background-color: var(--tg-theme-primary);
    border-color: var(--tg-theme-primary);
}
.account__check-remember label {
    user-select: none;
}
.account__check-forgot a {
    font-size: 16px;
}
.coupon__code-info {
    background: #f5f5f5;
    padding: 20px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 25px;
}
.coupon__code-form {
    margin-bottom: 35px;
    display: none;
}
.coupon__code-form p {
    margin-bottom: 15px;
}
.coupon__code-form input {
    width: 100%;
    background: #f5f5f5;
    height: 55px;
    border: none;
    color: var(--tg-heading-color);
    padding: 0 20px;
    border-radius: 30px;
    margin-bottom: 15px;
}
.customer__form-wrap {
    border: 1px solid #ebebeb;
    padding: 30px;
}
.customer__form-wrap .title {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    padding-bottom: 0.75rem;
    margin-bottom: 0.75rem;
    border-bottom: 1px solid #ebebeb;
    color: var(--tg-heading-color);
    line-height: 1;
}
.customer__form-wrap .title.title-two {
    margin-top: 20px;
}
.customer__form-wrap .form-grp {
    margin-bottom: 15px;
}
.cart__table tbody tr,
.customer__form-wrap .form-grp.select-grp,
.dashboard__top-wrap {
    position: relative;
}
.customer__form-wrap .form-grp.select-grp::after {
    content: "\f078";
    position: absolute;
    bottom: 13px;
    font-family: "Font Awesome 5 Free";
    font-weight: 700;
    right: 20px;
    font-size: 14px;
    color: var(--tg-theme-primary);
}
.customer__form-wrap .form-grp label {
    display: block;
    margin-bottom: 5px;
    font-size: 15px;
    color: var(--tg-body-color);
}
.customer__form-wrap .form-grp input,
.customer__form-wrap .form-grp textarea {
    width: 100%;
    color: var(--tg-heading-color);
    height: 53px;
    padding: 0 20px;
    background: #f5f5f5;
    border: none;
}
.customer__form-wrap .form-grp textarea {
    padding: 10px 20px 0;
    min-height: 120px;
    max-height: 120px;
}
.customer__form-wrap .form-grp select {
    background-color: #f5f5f5;
    border: none;
    color: var(--tg-heading-color);
    font-weight: 400;
    font-size: 16px;
    text-transform: capitalize;
    border-radius: 0;
    outline: 0;
    padding: 12px 37px 9px 20px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 100%;
    line-height: 1.2;
    height: 53px;
    cursor: pointer;
}
.order__info-wrap {
    border: 2px solid var(--tg-theme-primary);
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    padding: 30px;
}
.order__info-wrap > .title {
    font-size: 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    padding-bottom: 0.75rem;
    margin-bottom: 0.75rem;
    border-bottom: 1px solid #ebebeb;
}
.order__info-wrap .list-wrap {
    margin-bottom: 20px;
}
.order__info-wrap .list-wrap li {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #ebebeb;
    color: var(--tg-body-color);
    font-weight: 600;
    font-size: 15px;
    gap: 8px;
    padding: 12px 0;
}
.order__info-wrap .list-wrap li span {
    color: var(--tg-heading-color);
    display: block;
    line-height: 1;
    font-weight: 700;
}
.cart__table tbody td a,
.order__info-wrap p a:hover {
    color: var(--tg-theme-primary);
}
.order__info-wrap .list-wrap li.title span {
    color: var(--tg-body-color);
}
.order__info-wrap .list-wrap li:last-child span {
    font-size: 1.25rem;
}
.instructor__profile-form .form-grp,
.order__info-wrap p {
    margin-bottom: 15px;
}
.order__info-wrap .btn {
    width: 100%;
    margin-top: 10px;
    text-align: center;
}
.cart__table thead th {
    padding: 12px;
    font-size: 15px;
    font-weight: 600;
}
.cart__table tbody td {
    padding: 12px 17px;
    text-align: left;
    line-height: 1.1;
    border: none;
    border-top: 1px solid #ebebeb;
    color: var(--tg-body-color);
}
.cart__table tbody td:first-child {
    padding-left: 0;
}
.cart__table tbody td.product__name {
    font-weight: 500;
}
.cart__table tbody td.product__remove {
    padding-right: 5px;
    text-align: right;
}
.cart__table tbody td.product__remove a {
    font-size: 25px;
}
.cart__table tbody td.product__quantity {
    text-align: right;
    padding-right: 0;
}
.cart__table .product__thumb img {
    max-width: 70px;
}
.cart__table .product__remove {
    padding: 0 !important;
    width: 0 !important;
}
.cart__actions {
    padding-top: 25px !important;
    padding-bottom: 10px !important;
    padding-right: 0 !important;
}
.cart__actions-form {
    display: inline-flex;
    align-items: center;
    gap: 10px;
}
.cart__collaterals-wrap .list-wrap li,
.dashboard__instructor-info {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.cart__actions-form input {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    width: 100%;
    margin-bottom: 0;
    height: 50px;
    background: #f5f5f5;
    color: var(--tg-heading-color);
    padding: 0 0 0 20px;
    border: 0;
    border-radius: 30px;
}
.cart__collaterals-wrap {
    border: 1px solid #ebebeb;
    border-radius: 0;
    padding: 1.25rem;
    height: 100%;
    margin-left: 50px;
}
.cart__collaterals-wrap .title {
    font-size: 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    padding-bottom: 0.9375rem;
    border-bottom: 1px solid #ebebeb;
}
.cart__collaterals-wrap .list-wrap {
    padding-bottom: 0.9375rem;
}
.cart__collaterals-wrap .list-wrap li {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: space-between;
    border-bottom: 1px solid #ebebeb;
    color: var(--tg-heading-color);
    font-size: 15px;
    font-weight: 600;
    padding: 12px 5px;
}
.cart__collaterals-wrap .list-wrap li span {
    color: var(--tg-body-color);
    font-weight: 400;
}
.cart__collaterals-wrap .list-wrap li span.amount {
    font-weight: 600;
    color: var(--tg-heading-color);
    font-size: 1.25rem;
}
.cart__collaterals-wrap .btn {
    width: 100%;
    text-align: center;
    justify-content: center;
}
.f-right {
    float: right;
}
.dashboard__area {
    margin-top: -170px;
    position: relative;
    z-index: 2;
}
.dashboard__top-bg {
    background-size: cover;
    background-position: center center;
    position: relative;
    margin-bottom: 30px;
    border-radius: 10px;
    min-height: 350px;
    z-index: 1;
}
.dashboard__top-bg::before {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    display: block;
    z-index: -1;
    content: "";
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0),
        rgba(0, 0, 0, 0.5) 90%
    );
    transition: opacity 0.65s cubic-bezier(0.05, 0.2, 0.1, 1);
    cursor: pointer;
    border-radius: 10px;
}
.dashboard__instructor-info-left .thumb img,
.lesson__video-wrap-top-left a {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
}
.dashboard__instructor-info {
    display: flex;
    align-items: flex-end;
    gap: 20px;
    justify-content: space-between;
    position: absolute;
    padding: 0 40px 30px;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1;
}
@media (max-width: 991.98px) {
    .cart__collaterals-wrap {
        margin-top: 50px;
    }
    .dashboard__instructor-info {
        padding: 0 30px 30px;
    }
}
.dashboard__instructor-info-left,
.dashboard__instructor-info-left .content .list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.dashboard__instructor-info-left {
    display: flex;
    gap: 20px;
}
@media (max-width: 767.98px) {
    .singUp-wrap .title {
        font-size: 26px;
    }
    .cart__table .product__price,
    .cart__table .product__subtotal,
    .cart__table thead th.product__price,
    .cart__table thead th.product__subtotal,
    .singUp-wrap p br {
        display: none;
    }
    .coupon__code-info {
        flex-wrap: wrap;
    }
    .customer__form-wrap,
    .order__info-wrap {
        padding: 25px 20px;
    }
    .customer__form-wrap .title {
        font-size: 1.3rem;
    }
    .cart__table tbody td {
        padding: 12px 10px;
    }
    .cart__table .product__remove a {
        position: absolute;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        width: 1.375rem;
        height: 1.375rem;
        border-radius: 50%;
        color: #fff;
        background-color: red;
        font-size: 15px !important;
        left: 0;
        margin-top: -10px;
    }
    .cart__actions-form {
        width: 100%;
    }
    .f-right {
        float: none;
    }
    .update__cart-btn {
        text-align: center !important;
        width: 100%;
        margin-top: 20px;
    }
    .update__cart-btn .btn {
        width: 100%;
        text-align: center;
        justify-content: center;
    }
    .dashboard__top-bg {
        background-position: right center;
    }
    .dashboard__instructor-info {
        flex-wrap: wrap;
        padding: 0 20px 30px;
        gap: 15px;
    }
    .dashboard__instructor-info-left {
        flex-wrap: wrap;
        gap: 8px;
    }
}
.dashboard__instructor-info-left .thumb {
    width: 120px;
    height: 120px;
}
.dashboard__instructor-info-left .thumb img {
    border-radius: 50%;
    width: 100%;
    background: var(--tg-common-color-white);
    border: 2px solid var(--tg-common-color-white);
    padding: 4px;
}
.dashboard__instructor-info-left .content .title {
    font-size: 20px;
    color: var(--tg-common-color-white);
    margin-bottom: 12px;
    text-transform: capitalize;
}
.dashboard__instructor-info-left .content .list-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 15px;
}
.dashboard__instructor-info-left .content .list-wrap li,
.dashboard__sidebar-menu .list-wrap li a {
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.dashboard__instructor-info-left .content .list-wrap li {
    color: var(--tg-common-color-white);
    display: flex;
    gap: 5px;
}
.dashboard__instructor-info-left .content .list-wrap li svg {
    color: var(--tg-common-color-white);
    width: 18px;
    height: 18px;
}
.dashboard__instructor-info-right {
    margin-bottom: 20px;
}
.dashboard__sidebar-wrap {
    padding: 30px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    background: var(--tg-common-color-white);
    border: 4px solid #d9d9f3;
    position: sticky;
    top: 120px;
}
@media (max-width: 1199.98px) {
    .cart__collaterals-wrap {
        margin-left: 0;
    }
    .dashboard__sidebar-wrap {
        padding: 20px 15px;
    }
}
.dashboard__sidebar-title .title {
    margin-bottom: 0;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
    opacity: 0.5;
}
.dashboard__sidebar-menu .list-wrap li {
    padding-bottom: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid #d3d2df;
}
.dashboard__sidebar-menu .list-wrap li:last-child {
    border: none;
    margin-bottom: 0;
    padding-bottom: 0;
}
.dashboard__sidebar-menu .list-wrap li a {
    display: flex;
    gap: 10px;
    color: var(--tg-body-color);
    font-size: 16px;
    font-weight: 500;
}
.dashboard__sidebar-menu .list-wrap li a i {
    line-height: 0;
    font-size: 18px;
}
.dashboard__sidebar-menu .list-wrap li a:hover,
.dashboard__sidebar-menu .list-wrap li.active a {
    color: var(--tg-theme-primary);
}
.footer__content-two .list-wrap li a:hover,
.footer__content-two .list-wrap li.email a:hover {
    color: var(--tg-theme-secondary);
}
.dashboard__content-wrap {
    background: var(--tg-common-color-white);
    border-radius: 6px;
    padding: 30px;
}
.dashboard__content-wrap-two {
    padding: 30px 30px 0;
}
.dashboard__content-wrap .row > :nth-child(2) .dashboard__counter-item {
    background: rgba(185, 102, 231, 0.1294117647);
}
.dashboard__content-wrap .row > :nth-child(2) .dashboard__counter-item .icon {
    color: #b966e7;
    background: rgba(185, 102, 231, 0.1294117647);
}
.dashboard__content-wrap
    .row
    > :nth-child(2)
    .dashboard__counter-item
    .content
    .count,
.dashboard__content-wrap
    .row
    > :nth-child(2)
    .dashboard__counter-item
    .content
    p {
    color: #b966e7;
}
.dashboard__content-wrap .row > :nth-child(3) .dashboard__counter-item {
    background: rgba(128, 0, 128, 0.1294117647);
}
.dashboard__content-wrap .row > :nth-child(3) .dashboard__counter-item .icon {
    color: purple;
    background: rgba(128, 0, 128, 0.1294117647);
}
.dashboard__content-wrap
    .row
    > :nth-child(3)
    .dashboard__counter-item
    .content
    .count,
.dashboard__content-wrap
    .row
    > :nth-child(3)
    .dashboard__counter-item
    .content
    p {
    color: purple;
}
.dashboard__content-wrap .row > :nth-child(4) .dashboard__counter-item {
    background: rgba(219, 112, 147, 0.1294117647);
}
.dashboard__content-wrap .row > :nth-child(4) .dashboard__counter-item .icon {
    color: #db7093;
    background: rgba(219, 112, 147, 0.1294117647);
}
.dashboard__content-wrap
    .row
    > :nth-child(4)
    .dashboard__counter-item
    .content
    .count,
.dashboard__content-wrap
    .row
    > :nth-child(4)
    .dashboard__counter-item
    .content
    p {
    color: #db7093;
}
.dashboard__content-wrap .row > :nth-child(5) .dashboard__counter-item {
    background: rgba(233, 150, 122, 0.1294117647);
}
.dashboard__content-wrap .row > :nth-child(5) .dashboard__counter-item .icon {
    color: #e9967a;
    background: rgba(233, 150, 122, 0.1294117647);
}
.dashboard__content-wrap
    .row
    > :nth-child(5)
    .dashboard__counter-item
    .content
    .count,
.dashboard__content-wrap
    .row
    > :nth-child(5)
    .dashboard__counter-item
    .content
    p {
    color: #e9967a;
}
.dashboard__content-wrap .row > :nth-child(6) .dashboard__counter-item {
    background: rgba(255, 143, 60, 0.062745098);
}
.dashboard__content-wrap .row > :nth-child(6) .dashboard__counter-item .icon {
    color: #ff8f3c;
    background: rgba(255, 143, 60, 0.15);
}
.dashboard__content-wrap
    .row
    > :nth-child(6)
    .dashboard__counter-item
    .content
    .count,
.dashboard__content-wrap
    .row
    > :nth-child(6)
    .dashboard__counter-item
    .content
    p {
    color: #ff8f3c;
}
.dashboard__counter-item .content p,
.dashboard__counter-item .icon,
.dashboard__quiz-info span a:hover,
.dashboard__review-action a,
.dashboard__review-table tbody tr td .course-feedback a:hover,
.dashboard__review-table tbody tr td > a:hover {
    color: var(--tg-theme-primary);
}
.dashboard__content-title .title {
    margin-bottom: 25px;
    font-size: 20px;
    text-transform: capitalize;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(230, 227, 241, 0.31);
}
.dashboard__counter-item {
    background: rgba(47, 87, 239, 0.13);
    border-radius: 5px;
    text-align: center;
    padding: 30px 30px 50px;
    z-index: 1;
    overflow: hidden;
    position: relative;
    transition: transform 0.65s cubic-bezier(0.23, 1, 0.32, 1);
    margin-bottom: 30px;
}
.dashboard__counter-item:hover {
    transform: translateY(-3px);
}
.dashboard__counter-item .icon {
    position: relative;
    background: rgba(47, 87, 239, 0.13);
    width: 100px;
    height: 100px;
    padding: 25px;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 30px;
    line-height: 0;
}
.dashboard__quiz-info .title,
.dashboard__review-table .table,
.dashboard__review-table tbody tr td p,
.footer__content .list-wrap li:last-child,
.footer__link .list-wrap li:last-child,
.profile__content-wrap .list-wrap li:last-child {
    margin-bottom: 0;
}
.dashboard__counter-item .content .count {
    margin-bottom: 10px;
    font-size: 50px;
    font-weight: 700;
    color: var(--tg-theme-primary);
    line-height: 1;
}
.dashboard__counter-item .content p {
    margin-bottom: 0;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.5;
}
.dashboard__review-table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}
.dashboard__review-table thead th {
    color: var(--tg-heading-color);
    font-weight: 700;
    font-size: 16px;
    background: rgba(47, 87, 239, 0.1294117647);
    padding: 10px 20px;
    border: none;
}
.dashboard__review-table tbody tr td {
    padding: 10px 20px;
    border: none;
    vertical-align: middle;
}
.dashboard__review-table tbody tr td > a {
    color: var(--tg-heading-color);
    font-weight: 500;
}
.dashboard__quiz-info span a,
.dashboard__review-table tbody tr td p.color-black {
    color: var(--tg-heading-color);
}
.dashboard__review-table tbody tr td .course-feedback {
    color: var(--tg-body-color);
}
.dashboard__review-table tbody tr td .course-feedback a {
    color: var(--tg-heading-color);
    font-weight: 500;
    font-size: 15px;
}
.dashboard__review-action {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    gap: 20px;
    justify-content: flex-end;
}
.dashboard__review-action a {
    height: 30px;
    line-height: 0;
    font-size: 14px;
    padding: 0 14px;
    background: rgba(47, 87, 239, 0.1294117647);
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
}
.dashboard__quiz-info span,
.profile__content-wrap .list-wrap li,
.review__wrap .rating {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.dashboard__review-action a:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
    transform: translateY(-2px);
}
.dashboard__quiz-result.fail,
.dashboard__review-action a:nth-child(2) {
    background: rgba(255, 0, 3, 0.062745098);
    color: #ff0003;
}
.dashboard__review-action a:nth-child(2):hover {
    background: #ff0003;
    color: var(--tg-common-color-white);
}
.dashboard__quiz-info .title {
    font-size: 16px;
    font-family: var(--tg-body-font-family);
    font-weight: 500;
}
.dashboard__quiz-info span {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--tg-body-color);
}
.dashboard__quiz-result {
    display: inline-block;
    background: rgba(62, 183, 94, 0.062745098);
    padding: 5px 7px;
    font-size: 12px;
    display: flex;
    height: 22px;
    align-items: center;
    border-radius: 6px;
    justify-content: center;
    width: max-content;
    color: #3eb75e;
}
.dashboard__quiz-result.processing {
    background: rgba(47, 87, 239, 0.1294117647);
    color: #2f57ef;
}
.dashboard__quiz-result.hold {
    background: rgba(255, 143, 60, 0.062745098);
    color: #ff8f3c;
}
.dashboard__nav-wrap .nav-tabs {
    border-bottom: 3px solid #d9d9f3;
    gap: 5px 30px;
    flex-wrap: wrap;
    margin: 0 0 30px;
}
.instructor__profile-form .form-grp input:focus,
.instructor__profile-form .form-grp select:focus,
.instructor__profile-form .form-grp textarea:focus {
    border-color: var(--tg-theme-primary);
}
.dashboard__nav-wrap .nav-tabs .nav-link {
    font-weight: var(--tg-fw-medium);
    color: #7f7e97;
    border: none;
    position: relative;
    background: 0 0;
    padding: 16px 28px;
}
.dashboard__nav-wrap .nav-tabs .nav-link::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -2px;
    right: 0;
    width: 0;
    height: 3px;
    background: var(--tg-theme-primary);
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -o-border-radius: 30px;
    -ms-border-radius: 30px;
    border-radius: 30px;
    margin: 0 auto;
    z-index: 1;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -ms-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    transition: 0.3s ease-out;
}
.footer__bottom-menu .list-wrap li a::before,
.footer__link .list-wrap li a::before {
    bottom: -1px;
    height: 1px;
    background: currentColor;
    transition: transform 0.4s cubic-bezier(0.74, 0.72, 0.27, 0.24);
    left: 0;
}
.dashboard__nav-wrap .nav-tabs .nav-link.active,
.load-more-btn .link-btn:hover,
.plyr--full-ui input[type="range"] {
    color: var(--tg-theme-primary);
}
.dashboard__nav-wrap .nav-tabs .nav-link.active::after {
    width: 100%;
}
.profile__content-wrap .list-wrap li {
    display: flex;
    gap: 4px 30px;
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 18px;
}
.profile__content-wrap .list-wrap li span {
    min-width: 290px;
}
.review__wrap .rating {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: var(--tg-common-color-yellow);
}
.review__wrap span {
    font-size: 14px;
    font-weight: 500;
}
.review__wrap-two span {
    color: var(--tg-common-color-white);
}
.instructor__cover-bg {
    position: relative;
    background-size: cover;
    background-position: center center;
    min-height: 245px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -o-border-radius: 10px;
    -ms-border-radius: 10px;
    border-radius: 10px;
    margin-bottom: 30px;
}
.instructor__cover-info,
.instructor__cover-info-left button {
    position: absolute;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    bottom: 0;
    right: 0;
}
.instructor__cover-info {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
    padding: 0 40px 30px;
    left: 0;
}
.footer__bg,
.instructor__cover-info-left,
.instructor__profile-form .form-grp.select-grp {
    position: relative;
}
.instructor__cover-info-left .thumb {
    width: 120px;
    height: 120px;
}
.instructor__cover-info-left .thumb img {
    border-radius: 50%;
    width: 100%;
    background: var(--tg-common-color-white);
    border: 2px solid var(--tg-common-color-white);
    padding: 4px;
}
.instructor__cover-info-left button {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-common-color-white);
    border: none;
    color: var(--tg-theme-primary);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    font-size: 14px;
    padding: 0;
}
.instructor__cover-info-left button:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.instructor__profile-form .form-grp label {
    display: block;
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: 600;
    text-transform: capitalize;
    color: var(--tg-heading-color);
}
.instructor__profile-form .form-grp input,
.instructor__profile-form .form-grp textarea {
    width: 100%;
    background: 0 0;
    border: 2px solid #e6e3f1;
    border-radius: 6px;
    line-height: 23px;
    padding: 10px 20px;
    font-size: 14px;
    color: var(--tg-heading-color);
    height: 50px;
    display: block;
    transition: 0.3s ease-in-out;
}
.instructor__profile-form .form-grp input::placeholder,
.instructor__profile-form .form-grp textarea::placeholder {
    font-size: 14px;
}
.instructor__profile-form .form-grp textarea {
    min-height: 140px;
    max-height: 140px;
}
.instructor__profile-form .form-grp select {
    background-color: transparent;
    border: 2px solid #e6e3f1;
    color: var(--tg-heading-color);
    font-weight: 400;
    font-size: 16px;
    text-transform: capitalize;
    border-radius: 6px;
    outline: 0;
    padding: 10px 37px 10px 20px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 100%;
    line-height: 1.2;
    height: 50px;
    cursor: pointer;
    transition: 0.3s ease-in-out;
}
.instructor__profile-form .form-grp.select-grp::after {
    content: "\f078";
    position: absolute;
    bottom: 13px;
    font-family: "Font Awesome 5 Free";
    font-weight: 700;
    right: 20px;
    font-size: 14px;
    color: var(--tg-theme-primary);
}
.load-more-btn .link-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: var(--tg-heading-color);
    font-weight: 600;
}
.lesson__content .accordion-button,
.lesson__content .course-item-link {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.load-more-btn .link-btn svg {
    width: 14px;
    height: 14px;
}
.lesson__content > .title {
    margin-bottom: 20px;
    font-size: 20px;
    padding: 17px 20px;
    border-bottom: 1px solid #dcdceb;
}
.lesson__content .accordion-item {
    border: none;
    background: 0 0;
}
.lesson__content .accordion-button {
    border: none;
    box-shadow: none;
    font-size: 16px;
    color: var(--tg-heading-color);
    padding: 18px 50px 18px 20px;
    font-weight: 500;
    border-bottom: 1px solid #dcdceb;
    display: flex;
}
.lesson__content .accordion-button span {
    margin-left: auto;
    font-size: 14px;
}
.lesson__content .accordion-button:not(.collapsed) {
    background: 0 0;
    color: var(--tg-theme-primary);
}
.lesson__content .accordion-button:not(.collapsed)::after {
    content: "\f068";
}
.lesson__content .accordion-button::after {
    content: "\f067";
    font-family: var(--tg-icon-font-family);
    font-weight: 700;
    background-image: none;
    width: auto;
    height: auto;
    position: absolute;
    right: 20px;
    top: 17px;
    font-size: 16px;
}
.lesson__content .course-item {
    padding: 13px 20px;
    background: #f8f8ff;
    margin-bottom: 2px;
}
.lesson__content .course-item-link {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    color: var(--tg-common-color-dark);
    cursor: no-drop;
}
.lesson__content .course-item-link .item-name,
.lesson__content .course-item-meta {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.lesson__content .course-item-link .item-name {
    display: flex;
    gap: 10px;
}
.lesson__content .course-item-link .item-name::before {
    content: "";
    display: block;
    background-image: url(../img/icons/play.svg);
    width: 30px;
    height: 30px;
    flex: 0 0 auto;
}
.lesson__content .course-item-link.active {
    color: var(--tg-theme-primary);
    cursor: pointer;
}
.lesson__content .course-item-meta {
    margin-left: auto;
    display: flex;
    line-height: 1;
    gap: 10px;
    flex: 0 0 auto;
}
.lesson__video-wrap-top,
.lesson__video-wrap-top-left {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
}
.lesson__content .course-item-meta .duration {
    margin: 2px 0 0;
}
.lesson__video-wrap {
    margin-bottom: 40px;
    position: relative;
}
.lesson__video-wrap:hover .lesson__next-prev-button button {
    opacity: 1;
    visibility: visible;
}
.lesson__video-wrap-top {
    display: flex;
    background: var(--tg-theme-primary);
    padding: 13px 20px;
    justify-content: space-between;
    gap: 10px;
}
.footer__area-three,
.footer__area-two {
    background: var(--tg-common-color-black);
}
.lesson__video-wrap-top-left {
    display: flex;
    gap: 20px;
}
.lesson__video-wrap-top-left a,
.lesson__video-wrap-top-right a {
    width: 35px;
    height: 35px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1294117647);
    color: var(--tg-common-color-white);
}
.lesson__video-wrap-top-left a {
    display: flex;
    justify-content: center;
    border-radius: 50%;
    flex: 0 0 auto;
}
.lesson__video-wrap-top-left a i {
    transform: rotate(180deg);
}
.lesson__video-wrap-top-left span {
    display: block;
    color: var(--tg-common-color-white);
    font-size: 18px;
    font-weight: 500;
}
.lesson__video-wrap-top-right a {
    display: flex;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    flex: 0 0 auto;
}
.footer__social,
.lesson__next-prev-button button {
    align-items: center;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
}
.lesson__video-wrap .plyr__poster {
    background-size: cover;
}
.lesson__video-wrap .plyr__control--overlaid {
    padding: 24px;
    background: var(--tg-theme-primary);
}
.lesson__video-wrap .plyr__control--overlaid:hover {
    background: var(--tg-theme-secondary);
}
.lesson__details-content {
    margin-right: 50px;
}
.lesson__next-prev-button button {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%) rotate(180deg);
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.27);
    display: flex;
    justify-content: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1294117647);
    color: var(--tg-common-color-white);
    line-height: 0;
    font-size: 18px;
    opacity: 0;
    visibility: hidden;
}
.lesson__next-prev-button button.next-button {
    left: auto;
    right: 20px;
    transform: translateY(-50%) rotate(0);
}
.lesson__next-prev-button button:hover {
    background: var(--tg-theme-primary);
    border-color: var(--tg-theme-primary);
}
.error-img {
    text-align: center;
    width: 648px;
    height: 323px;
    margin: 0 auto 65px;
}
.error-content .title {
    margin-bottom: 60px;
    font-weight: 700;
    font-size: 40px;
    letter-spacing: -1px;
}
.error-content .title span {
    display: block;
    margin-top: 10px;
}
.footer__area-two {
    margin-top: -160px;
}
.footer__area-two .footer__top {
    padding: 230px 0 60px;
}
.footer__area-five {
    padding-top: 100px;
    margin-top: -100px;
}
.footer__bg {
    z-index: 1;
    margin: 22px 0 0;
}
.footer__bg-shape {
    background-size: cover;
    background-position: center;
    width: 100%;
    height: 22px;
    position: absolute;
    left: 0;
    top: -25px;
}
.footer__bg-shape svg {
    color: var(--tg-common-color-blue-2);
    width: 100%;
    height: 22px;
}
.footer__top {
    padding: 100px 0 60px;
}
.footer__top .row [class*="col-"]:nth-child(2) .footer__widget,
.footer__top .row [class*="col-"]:nth-child(3) .footer__widget {
    margin-left: 60px;
}
.footer__top-three {
    position: relative;
    z-index: 1;
}
.footer__widget {
    margin-bottom: 35px;
}
.footer__widget-title {
    color: var(--tg-common-color-white);
    font-size: 22px;
    font-weight: 600;
    position: relative;
    padding-bottom: 20px;
    margin-bottom: 20px;
}
.footer__widget-title::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 4px;
    border-radius: 2px;
    background: var(--tg-theme-primary);
}
.footer__content p {
    margin-bottom: 12px;
    color: var(--tg-common-color-gray-5);
    width: 100%;
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
    .instructor__nav-two .instructor-button-next {
        right: 15px;
    }
    .instructor__details-info {
        padding: 35px 30px;
    }
    .cta__content .title {
        font-size: 42px;
    }
    .cta__content-two .title {
        font-size: 34px;
    }
    .fact__item .count {
        font-size: 36px;
    }
    .features__item-three {
        padding: 30px 20px;
    }
    .event__thumb-four img {
        height: 280px;
    }
    .faq__img-four .main-img {
        flex-direction: row;
    }
    .faq__img-four .main-img img {
        max-width: 240px;
        width: auto;
    }
    .faq__img-four .main-img img:nth-child(2) {
        margin-top: 50px;
    }
    .faq__mask-img {
        width: 420px;
        height: 325px;
    }
    .shop-top-left p {
        margin-bottom: 0;
        text-align: left;
    }
    .shop-top-right {
        margin: 0 0 0 auto;
    }
    .shop-item {
        padding: 20px 20px 25px;
    }
    .shop-details-images-wrap .nav-item {
        width: 120px;
        flex: 0 0 auto;
    }
    .blog__post-content-five,
    .blog__post-item-four {
        padding: 30px 30px 35px;
    }
    .blog__post-thumb-four img {
        height: 290px;
    }
    .newsletter__inner-wrap {
        padding: 50px 30px;
    }
    .contact-form-wrap {
        padding: 30px 25px 40px;
    }
    .singUp-wrap {
        padding: 30px;
    }
    .dashboard__instructor-info-left {
        gap: 20px;
    }
    .footer__content p {
        width: 100%;
    }
}
.footer__content .list-wrap li {
    color: var(--tg-common-color-gray-5);
    font-weight: 500;
    margin-bottom: 7px;
}
.footer__content-two .list-wrap li a {
    font-size: 18px;
    font-weight: 600;
    color: var(--tg-common-color-white);
}
.footer__contact-content p,
.footer__link .list-wrap li a,
.footer__newsletter p,
.footer__social li a,
.footer__social-wrap .title {
    color: var(--tg-common-color-gray-5);
}
.footer__content-two .list-wrap li.email a {
    font-size: 16px;
    font-weight: 400;
    color: #bbb;
}
.footer__link .list-wrap li a {
    font-size: 16px;
    font-weight: 500;
    position: relative;
}
.footer__link .list-wrap li a:hover,
.footer__social li a:hover,
.footer__social-two li a:hover {
    color: var(--tg-theme-secondary);
}
.footer__link .list-wrap li a::before {
    content: "";
    position: absolute;
    width: 100%;
    transform-origin: right top;
    transform: scale(0, 1);
}
.footer__link .list-wrap li a:hover::before {
    -webkit-transform-origin: left top;
    -ms-transform-origin: left top;
    transform-origin: left top;
    -webkit-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    transform: scale(1, 1);
}
.footer__contact-content p {
    margin-bottom: 10px;
}
.footer__newsletter p {
    margin-bottom: 20px;
}
.footer__newsletter-form input {
    width: 100%;
    background: #212040;
    padding: 13px 140px 13px 20px;
    border: none;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -o-border-radius: 6px;
    -ms-border-radius: 6px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 400;
    color: var(--tg-common-color-white);
    height: 50px;
}
.footer__newsletter-form input::placeholder {
    font-size: 14px;
    font-weight: 400;
    color: #706f84;
}
.footer__newsletter-form button {
    border: none;
    font-size: 15px;
    font-weight: 600;
    color: var(--tg-heading-color);
    background: var(--tg-theme-secondary);
    text-transform: capitalize;
    -webkit-border-radius: 0 6px 6px 0;
    -moz-border-radius: 0 6px 6px 0;
    -o-border-radius: 0 6px 6px 0;
    -ms-border-radius: 0 6px 6px 0;
    border-radius: 0 6px 6px 0;
    display: block;
    position: absolute;
    right: 0;
    top: 0;
    padding: 12px 28px;
    height: 50px;
}
.footer__newsletter-form button:hover {
    background: var(--tg-theme-primary);
    color: var(--tg-common-color-white);
}
.footer__newsletter-form-two button,
.footer__newsletter-form-two input {
    border-radius: 100px;
}
.footer__social {
    display: flex;
    gap: 15px;
    margin-bottom: 35px;
}
.footer__social-wrap .title {
    margin-bottom: 10px;
    font-size: 15px;
    font-weight: 500;
    font-family: var(--tg-body-font-family);
}
.footer__social-two {
    margin-bottom: 0;
}
.footer__social-two li a {
    color: var(--tg-common-color-white);
}
.footer__bottom {
    background: var(--tg-common-color-dark);
    padding: 35px 0;
}
.footer__bottom-two {
    border-top: 1px solid #191838;
    padding: 35px 0;
}
.footer__bottom-three {
    background: #272260;
}
.footer__bottom-three .copy-right-text p,
.footer__bottom-three .footer__bottom-menu .list-wrap li a {
    color: #cfd9ea;
}
.footer__bottom-four {
    position: relative;
    z-index: 2;
}
.footer__bottom-menu .list-wrap {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 32px;
}
.footer__bottom-menu .list-wrap li a {
    color: #8c9ab4;
    position: relative;
}
.footer__bottom-menu .list-wrap li a::before {
    content: "";
    position: absolute;
    width: 100%;
    transform-origin: right top;
    transform: scale(0, 1);
}
.footer__bottom-menu .list-wrap li a::after {
    content: "";
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 12px;
    background: #8c9ab4;
}
.footer__bottom-menu .list-wrap li a:hover {
    color: var(--tg-theme-secondary);
}
.footer__bottom-menu .list-wrap li a:hover::before {
    -webkit-transform-origin: left top;
    -ms-transform-origin: left top;
    transform-origin: left top;
    -webkit-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    transform: scale(1, 1);
}
.footer__bottom-menu .list-wrap li:last-child a::after {
    display: none;
}
.footer__shape {
    background-size: cover;
    background-position: center;
    position: absolute;
    z-index: -2;
    left: 0;
    bottom: -55px;
    width: 100%;
    height: 138px;
}
.footer__shape-wrap img {
    position: absolute;
    z-index: -1;
}
.footer__shape-wrap img:first-child {
    left: 4%;
    top: 13%;
    max-width: 75px;
}
@media (max-width: 1500px) {
    .error-img {
        margin-bottom: 45px;
    }
    .footer__shape-wrap img:first-child {
        top: 1%;
        max-width: 60px;
    }
}
.footer__shape-wrap img:nth-child(2) {
    left: 0;
    bottom: 16%;
    max-width: 82px;
    z-index: 1;
}
@media (max-width: 1199.98px) {
    .dashboard__content-wrap,
    .dashboard__counter-item {
        padding: 30px 20px;
    }
    .dashboard__content-wrap-two {
        padding: 30px 20px 0;
    }
    .dashboard__counter-item .content .count {
        font-size: 35px;
    }
    .dashboard__review-table tbody tr td,
    .dashboard__review-table thead th {
        padding: 10px;
    }
    .dashboard__review-action {
        gap: 10px;
    }
    .profile__content-wrap .list-wrap li span {
        min-width: 220px;
    }
    .error-content .title {
        margin-bottom: 35px;
        font-size: 35px;
    }
    .footer__shape-wrap img:nth-child(2) {
        bottom: 10%;
    }
}
.footer__shape-wrap img:nth-child(3) {
    right: 47%;
    bottom: 13%;
    max-width: 115px;
    z-index: 1;
}
@media (max-width: 991.98px) {
    .dashboard__sidebar-wrap {
        padding: 30px;
    }
    .dashboard__content-wrap {
        margin-top: 40px;
    }
    .lesson__details-content {
        margin: 0 30px;
    }
    .error-img {
        margin-bottom: 40px;
    }
    .footer__top .row [class*="col-"]:nth-child(2) .footer__widget,
    .footer__top .row [class*="col-"]:nth-child(3) .footer__widget {
        margin-left: 0;
    }
    .footer__bottom,
    .footer__bottom-two {
        padding: 25px 0;
    }
    .footer__shape-wrap img:nth-child(2) {
        bottom: 8%;
    }
    .footer__shape-wrap img:nth-child(3) {
        display: none;
    }
}
.footer__shape-wrap img:nth-child(4) {
    right: 6%;
    top: 7%;
    max-width: 120px;
}
@media (max-width: 1800px) {
    .footer__shape-wrap img:first-child {
        left: 1%;
    }
    .footer__shape-wrap img:nth-child(4) {
        right: 4%;
        top: 7%;
        max-width: 105px;
    }
}
@media (max-width: 1199.98px) {
    .footer__shape-wrap img:nth-child(3) {
        bottom: 9%;
    }
    .footer__shape-wrap img:nth-child(4) {
        right: 5%;
        top: 26%;
    }
}
.footer__shape-wrap .shape-inner {
    right: 0;
    bottom: 22%;
    position: absolute;
    z-index: -2;
}
@media (max-width: 1800px) {
    .footer__shape-wrap .shape-inner {
        bottom: 43%;
    }
    .footer__shape-wrap .shape-inner svg {
        max-width: 90px;
        height: auto;
    }
}
.footer__shape-wrap .shape-inner svg path {
    animation: 0.8s linear infinite animation__shape-two;
}
.app-download a img {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -o-border-radius: 4px;
    -ms-border-radius: 4px;
    border-radius: 4px;
}
.copy-right-text p {
    margin-bottom: 0;
    color: #8c9ab4;
}
@media (max-width: 767.98px) {
    .dashboard__instructor-info-left .content .title {
        font-size: 18px;
        margin-bottom: 8px;
    }
    .dashboard__instructor-info-left .content .list-wrap li {
        font-size: 13px;
    }
    .dashboard__instructor-info-left .content .list-wrap li svg {
        width: 16px;
        height: 16px;
    }
    .dashboard__instructor-info-right {
        margin-bottom: 0;
    }
    .dashboard__sidebar-wrap {
        padding: 25px 20px;
    }
    .dashboard__review-table .table {
        min-width: 780px;
    }
    .dashboard__nav-wrap .nav-tabs,
    .footer__bottom-menu .list-wrap {
        justify-content: center;
    }
    .profile__content-wrap .list-wrap li {
        flex-direction: column;
        font-size: 16px;
    }
    .instructor__cover-info {
        padding: 0 25px 25px;
    }
    .lesson__video-wrap-top-left span {
        font-size: 16px;
    }
    .lesson__details-content {
        margin: 0 15px;
    }
    .error-img {
        margin-bottom: 30px;
        width: 100%;
        height: 100%;
    }
    .error-content .title {
        font-size: 30px;
        margin-bottom: 30px;
        letter-spacing: 0;
    }
    .error-content .title span {
        margin-top: 5px;
    }
    .footer__top {
        padding: 80px 0 40px;
    }
    .footer__shape-wrap .shape-inner,
    .footer__shape-wrap img:first-child,
    .footer__shape-wrap img:nth-child(2),
    .footer__shape-wrap img:nth-child(4) {
        display: none;
    }
    .copy-right-text p {
        margin-bottom: 5px;
        text-align: center;
    }
}
@-webkit-keyframes hoverShine {
    100% {
        left: 125%;
    }
}
@keyframes hoverShine {
    100% {
        left: 125%;
    }
}
.rotateme {
    -webkit-animation-name: rotateme;
    animation-name: rotateme;
    -webkit-animation-duration: 180s;
    animation-duration: 180s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
}
@keyframes rotateme {
    from {
        -webkit-transform: rotate(0);
        transform: rotate(0);
    }
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-webkit-keyframes rotateme {
    from {
        -webkit-transform: rotate(0);
    }
    to {
        -webkit-transform: rotate(360deg);
    }
}
.alltuchtopdown {
    -webkit-animation: 1.5s ease-in-out infinite alternate alltuchtopdown;
    animation: 3s ease-in-out infinite alternate alltuchtopdown;
}
@keyframes alltuchtopdown {
    0%,
    100% {
        -webkit-transform: rotateX(0) translateY(0);
        -moz-transform: rotateX(0) translateY(0);
        -ms-transform: rotateX(0) translateY(0);
        -o-transform: rotateX(0) translateY(0);
        transform: rotateX(0) translateY(0);
    }
    50% {
        -webkit-transform: rotateX(0) translateY(-20px);
        -moz-transform: rotateX(0) translateY(-20px);
        -ms-transform: rotateX(0) translateY(-20px);
        -o-transform: rotateX(0) translateY(-20px);
        transform: rotateX(0) translateY(-20px);
    }
}
@-webkit-keyframes alltuchtopdown {
    0%,
    100% {
        -webkit-transform: rotateX(0) translateY(0);
        -moz-transform: rotateX(0) translateY(0);
        -ms-transform: rotateX(0) translateY(0);
        -o-transform: rotateX(0) translateY(0);
        transform: rotateX(0) translateY(0);
    }
    50% {
        -webkit-transform: rotateX(0) translateY(-20px);
        -moz-transform: rotateX(0) translateY(-20px);
        -ms-transform: rotateX(0) translateY(-20px);
        -o-transform: rotateX(0) translateY(-20px);
        transform: rotateX(0) translateY(-20px);
    }
}
@keyframes pulse-border {
    0% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
        opacity: 0;
    }
}
@-webkit-keyframes pulse-border {
    0% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
        opacity: 0;
    }
}
@keyframes animation__shape {
    0% {
        stroke-dashoffset: 12;
    }
    100% {
        stroke-dashoffset: 0;
    }
}
@keyframes animation__shape-two {
    0% {
        stroke-dashoffset: 40;
    }
    100% {
        stroke-dashoffset: 0;
    }
}
@keyframes fadeInUp2 {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
        -ms-transform: translateY(20px);
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}
.fadeInUp2 {
    -webkit-animation-name: fadeInUp2;
    animation-name: fadeInUp2;
    -webkit-animation-delay: 0.5s;
    animation-delay: 0.5s;
    -webkit-animation-duration: 0.5s;
    animation-duration: 0.5s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}
