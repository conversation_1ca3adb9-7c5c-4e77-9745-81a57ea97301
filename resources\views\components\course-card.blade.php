@props(['course', 'showWishlist' => true])

<div class="courses__item shine__animate-item">
    <div class="courses__item-thumb">
        <a href="{{ route('course.show', $course->slug) }}" class="shine__animate-link">
            @if($course->thumbnail)
                <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}">
            @else
                <img src="{{ asset('frontend/img/course/course_thumb01.jpg') }}" alt="{{ $course->title }}">
            @endif
        </a>
        @if($showWishlist)
            <a href="javascript:;" class="wsus-wishlist-btn common-white courses__wishlist-two"
               aria-label="WishList" data-slug="{{ $course->slug }}">
                <i class="far fa-heart"></i>
            </a>
        @endif
    </div>
    <div class="courses__item-content">
        <ul class="courses__item-meta list-wrap">
            <li class="courses__item-tag">
                <a href="{{ route('visitors.courses') }}?category={{ $course->category->slug }}">
                    {{ $course->category->name }}
                </a>
            </li>
            <li class="avg-rating">
                <i class="fas fa-star"></i>
                5.0 {{-- TODO: Implement actual rating system --}}
            </li>
        </ul>
        <h3 class="title">
            <a href="{{ route('course.show', $course->slug) }}">{{ $course->title }}</a>
        </h3>
        <p class="author">
            By <a href="#" class="instructor-link">{{ $course->instructor->name }}</a>
        </p>
        <div class="courses__item-bottom">
            <div class="button">
                @if($course->is_free)
                    <a href="{{ route('course.show', $course->slug) }}" class="enroll-free">
                        <span class="text">Enroll Free</span>
                        <i class="flaticon-arrow-right"></i>
                    </a>
                @else
                    <a href="javascript:;" class="add-to-cart" data-id="{{ $course->id }}">
                        <span class="text">Add To Cart</span>
                        <i class="flaticon-arrow-right"></i>
                    </a>
                @endif
            </div>

            <h4 class="price">
                @if($course->is_free)
                    Free
                @elseif($course->has_discount)
                    <span class="old-price">${{ number_format($course->price, 2) }}</span>
                    ${{ number_format($course->discount_price, 2) }}
                @else
                    ${{ number_format($course->price, 2) }}
                @endif
            </h4>
        </div>
    </div>
</div>
