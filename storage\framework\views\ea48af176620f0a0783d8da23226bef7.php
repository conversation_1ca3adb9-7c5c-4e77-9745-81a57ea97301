<?php $__env->startSection('content'); ?>
<main class="main-area fix">
    <!-- banner-area -->
    <section class="banner-area banner-bg tg-motion-effects" data-background="frontend/img/banner/banner_bg.png">
        <div class="container">
            <div class="row">
                <div class="col-xl-5 col-lg-6">
                    <div class="banner__content">
                        <h3 class="title tg-svg">
                            Learn New Skills Online with Top
                            <span class="position-relative">
                                <span class="svg-icon" id="banner-svg">
                                    <svg x="0px" y="0px" preserveAspectRatio="none" viewBox="0 0 209 59" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path class="path" d="M4.74438 7.70565C69.7006 -1.18799 136.097 -2.38304 203.934 4.1205C207.178 4.48495 209.422 7.14626 208.933 10.0534C206.793 23.6481 205.415 36.5704 204.801 48.8204C204.756 51.3291 202.246 53.5582 199.213 53.7955C136.093 59.7623 74.1922 60.5985 13.5091 56.3043C10.5653 56.0924 7.84371 53.7277 7.42158 51.0325C5.20725 38.2627 2.76333 25.6511 0.0898448 13.1978C-0.465589 10.5873 1.61173 8.1379 4.74438 7.70565Z" stroke="url(#paint0_linear_47_27)" stroke-width="7" pathLength="1"></path>
                                        <defs>
                                            <linearGradient id="paint0_linear_47_27" x1="99.8578" y1="2.70565" x2="99.8578" y2="56.0002" gradientUnits="userSpaceOnUse">
                                                <stop stop-color="#405FF2"></stop>
                                                <stop offset="1" stop-color="#3FDACF"></stop>
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                                Educators
                            </span>
                        </h3>
                        <p>Discover a World of Knowledge and Skills at Your Fingertips – Unlock Your Potential and Achieve Your Dreams with Our Comprehensive Learning Resources!</p>
                        <div class="banner__btn-wrap">
                            <div class="banner__btn">
                                <a href="<?php echo e(route('visitors.courses')); ?>" class="btn arrow-btn">Start Free Trial <img src="frontend/img/icons/right_arrow.svg" alt="img" class="injectable"></a>
                            </div>
                            <div class="banner__btn">
                                <a href="<?php echo e(route('visitors.courses')); ?>" class="btn btn-two arrow-btn">Explore Courses <img src="frontend/img/icons/right_arrow.svg" alt="img" class="injectable"></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-7 col-lg-6">
                    <div class="banner__images">
                        <img src="uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp" alt="img" class="main-img">
                        <div class="shape big-shape" data-aos="fade-up-right" data-aos-delay="600">
                            <img src="frontend/img/banner/banner_shape01.png" alt="shape" class="tg-motion-effects1">
                        </div>
                        <img src="frontend/img/banner/bg_dots.svg" alt="shape" class="shape bg-dots rotateme">
                        <img src="frontend/img/banner/banner_shape02.png" alt="shape" class="shape small-shape tg-motion-effects3">
                        <div class="banner__author">
                            <div class="banner__author-item">
                                <div class="image">
                                    <img src="frontend/img/banner/banner_author01.png" alt="img">
                                </div>
                                <h6 class="name">Robert Fox</h6>
                                <span class="designation">Adobe Expert</span>
                            </div>
                            <div class="banner__author-item">
                                <div class="image">
                                    <img src="frontend/img/banner/banner_author02.png" alt="img">
                                </div>
                                <h6 class="name">Michel Jones</h6>
                                <span class="designation">WordPress Expert</span>
                            </div>
                            <img src="frontend/img/banner/banner_shape02.png" alt="shape" class="shape banner-shape">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <img src="frontend/img/banner/banner_shape01.svg" alt="shape" class="line-shape" data-aos="fade-right" data-aos-delay="1600">
    </section>
    <!-- banner-area-end -->

    <!-- brand-area -->
    <div class="brand-area">
        <div class="container-fluid">
            <div class="marquee_mode">
                <div class="brand__item">
                    <a href="#"><img src="uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp" alt="brand"></a>
                    <img src="frontend/img/icons/brand_star.svg" alt="star">
                </div>
                <div class="brand__item">
                    <a href="#"><img src="uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp" alt="brand"></a>
                    <img src="frontend/img/icons/brand_star.svg" alt="star">
                </div>
                <div class="brand__item">
                    <a href="#"><img src="uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp" alt="brand"></a>
                    <img src="frontend/img/icons/brand_star.svg" alt="star">
                </div>
                <div class="brand__item">
                    <a href="#"><img src="uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp" alt="brand"></a>
                    <img src="frontend/img/icons/brand_star.svg" alt="star">
                </div>
                <div class="brand__item">
                    <a href="#"><img src="uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp" alt="brand"></a>
                    <img src="frontend/img/icons/brand_star.svg" alt="star">
                </div>
            </div>
        </div>
    </div>
    <!-- brand-area-end -->

    <!-- about-area -->
    <section class="about-area tg-motion-effects section-py-120">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-6 col-md-9">
                    <div class="about__images">
                        <img src="uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp" alt="img" class="main-img">
                        <img src="frontend/img/others/about_shape.svg" alt="img" class="shape alltuchtopdown">
                        <a href="https://www.youtube.com/watch?v=b2Az7_lLh3g" class="popup-video">
                            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="28" viewBox="0 0 22 28" fill="none">
                                <path d="M0.19043 26.3132V1.69421C0.190288 1.40603 0.245303 1.12259 0.350273 0.870694C0.455242 0.6188 0.606687 0.406797 0.79027 0.254768C0.973854 0.10274 1.1835 0.0157243 1.39936 0.00193865C1.61521 -0.011847 1.83014 0.0480663 2.02378 0.176003L20.4856 12.3292C20.6973 12.4694 20.8754 12.6856 20.9999 12.9535C21.1245 13.2214 21.1904 13.5304 21.1904 13.8456C21.1904 14.1608 21.1245 14.4697 20.9999 14.7376C20.8754 15.0055 20.6973 15.2217 20.4856 15.3619L2.02378 27.824C1.83056 27.9517 1.61615 28.0116 1.40076 27.9981C1.18536 27.9847 0.97607 27.8983 0.79269 27.7472C0.609313 27.596 0.457816 27.385 0.352299 27.1342C0.246782 26.8833 0.191236 26.6008 0.19043 26.3132Z" fill="currentcolor"></path>
                            </svg>
                        </a>
                        <div class="about__enrolled" data-aos="fade-right" data-aos-delay="200">
                            <p class="title"><span>36K+</span> Enrolled Students</p>
                            <img src="frontend/img/others/student_grp.png" alt="img">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="about__content">
                        <div class="section__title">
                            <span class="sub-title">Get To Know About Us</span>
                            <h2 class="title">
                                Empowering Minds,
                                <span class="position-relative">
                                    <svg x="0px" y="0px" preserveAspectRatio="none" viewBox="0 0 209 59" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path class="path" d="M4.74438 7.70565C69.7006 -1.18799 136.097 -2.38304 203.934 4.1205C207.178 4.48495 209.422 7.14626 208.933 10.0534C206.793 23.6481 205.415 36.5704 204.801 48.8204C204.756 51.3291 202.246 53.5582 199.213 53.7955C136.093 59.7623 74.1922 60.5985 13.5091 56.3043C10.5653 56.0924 7.84371 53.7277 7.42158 51.0325C5.20725 38.2627 2.76333 25.6511 0.0898448 13.1978C-0.465589 10.5873 1.61173 8.1379 4.74438 7.70565Z" stroke="url(#paint0_linear_47_27)" stroke-width="7" pathLength="1"></path>
                                        <defs>
                                            <linearGradient id="paint0_linear_47_27" x1="99.8578" y1="2.70565" x2="99.8578" y2="56.0002" gradientUnits="userSpaceOnUse">
                                                <stop stop-color="#405FF2"></stop>
                                                <stop offset="1" stop-color="#3FDACF"></stop>
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                    Shaping
                                </span>
                                Futures
                            </h2>
                        </div>
                        <p class="desc">Groove's intuitive shared inbox makes it easy for team members to organize, prioritize and.In this episode of the Smashing Pod we're talking about Web Platform Baseline.</p>
                        <ul class="about__info-list list-wrap">
                            <li class="about__info-list-item">
                                <i class="flaticon-angle-right"></i>
                                <p class="content">The Most World Class Instructors</p>
                            </li>
                            <li class="about__info-list-item">
                                <i class="flaticon-angle-right"></i>
                                <p class="content">Access Your Class anywhere</p>
                            </li>
                            <li class="about__info-list-item">
                                <i class="flaticon-angle-right"></i>
                                <p class="content">Flexible Course Plan</p>
                            </li>
                        </ul>
                        <div class="tg-button-wrap">
                            <a href="<?php echo e(route('visitors.about')); ?>" class="btn arrow-btn">Start Free Trial <img src="frontend/img/icons/right_arrow.svg" alt="img" class="injectable"></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- about-area-end -->

    <?php if($featuredCourses->count() > 0): ?>
    <!-- courses-area -->
    <section class="courses-area section-pt-120 section-pb-90" data-background="frontend/img/bg/courses_bg.jpg">
        <div class="container">
            <div class="section__title-wrap">
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <div class="section__title text-center mb-40">
                            <span class="sub-title">Featured Courses</span>
                            <h2 class="title">Explore Our World's Best Courses</h2>
                            <p>Discover top-rated courses designed by industry experts to help you master new skills and advance your career.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row courses__grid-wrap row-cols-1 row-cols-xl-4 row-cols-lg-3 row-cols-md-2 row-cols-sm-1">
                <?php $__currentLoopData = $featuredCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col">
                        <?php if (isset($component)) { $__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.course-card','data' => ['course' => $course]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('course-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['course' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($course)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702)): ?>
<?php $attributes = $__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702; ?>
<?php unset($__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702)): ?>
<?php $component = $__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702; ?>
<?php unset($__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702); ?>
<?php endif; ?>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="load__more-btn text-center mt-30">
                <a href="<?php echo e(route('visitors.courses')); ?>" class="btn arrow-btn">View All Courses <img src="frontend/img/icons/right_arrow.svg" alt="img" class="injectable"></a>
            </div>
        </div>
    </section>
    <!-- courses-area-end -->
    <?php endif; ?>

    <!-- courses-area -->
    <section class="courses-area section-py-120">
        <div class="container">
            <div class="courses__top-wrap">
                <div class="row align-items-end">
                    <div class="col-md-8">
                        <div class="section__title">
                            <span class="sub-title">Top Class Courses</span>
                            <h2 class="title">Explore Our World's Best Courses</h2>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="courses__top-right">
                            <a href="<?php echo e(route('visitors.courses')); ?>" class="btn arrow-btn">See All Courses <img src="frontend/img/icons/right_arrow.svg" alt="img" class="injectable"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="courses__nav">
                <ul class="nav nav-tabs" id="courseTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="all-tab" data-bs-toggle="tab"
                            data-bs-target="#all-tab-pane" type="button" role="tab"
                            aria-controls="all-tab-pane" aria-selected="true">
                            All Courses
                        </button>
                    </li>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="category-<?php echo e($category->id); ?>-tab" data-bs-toggle="tab"
                            data-bs-target="#category-<?php echo e($category->id); ?>-tab-pane" type="button" role="tab"
                            aria-controls="category-<?php echo e($category->id); ?>-tab-pane" aria-selected="false">
                            <?php echo e($category->name); ?>

                        </button>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>

            <div class="tab-content" id="courseTabContent">
                <div class="tab-pane fade show active" id="all-tab-pane" role="tabpanel" aria-labelledby="all-tab"
                    tabindex="0">
                    <div class="swiper courses-swiper-active">
                        <div class="swiper-wrapper">
                            <?php $__empty_1 = true; $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="swiper-slide">
                                    <?php if (isset($component)) { $__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.course-card','data' => ['course' => $course]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('course-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['course' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($course)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702)): ?>
<?php $attributes = $__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702; ?>
<?php unset($__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702)): ?>
<?php $component = $__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702; ?>
<?php unset($__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702); ?>
<?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="swiper-slide">
                                    <div class="text-center">
                                        <p>No courses available at the moment.</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="courses__nav">
                        <div class="courses-button-prev"><i class="flaticon-arrow-right"></i></div>
                        <div class="courses-button-next"><i class="flaticon-arrow-right"></i></div>
                    </div>
                </div>

                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="tab-pane fade" id="category-<?php echo e($category->id); ?>-tab-pane" role="tabpanel"
                     aria-labelledby="category-<?php echo e($category->id); ?>-tab" tabindex="0">
                    <div class="swiper courses-swiper-active">
                        <div class="swiper-wrapper">
                            <?php $__empty_1 = true; $__currentLoopData = $category->courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="swiper-slide">
                                    <?php if (isset($component)) { $__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.course-card','data' => ['course' => $course]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('course-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['course' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($course)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702)): ?>
<?php $attributes = $__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702; ?>
<?php unset($__attributesOriginal0a1b9827ce04f2b2ad6eeae95024b702); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702)): ?>
<?php $component = $__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702; ?>
<?php unset($__componentOriginal0a1b9827ce04f2b2ad6eeae95024b702); ?>
<?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="swiper-slide">
                                    <div class="text-center">
                                        <p>No courses available in this category.</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="courses__nav">
                        <div class="courses-button-prev"><i class="flaticon-arrow-right"></i></div>
                        <div class="courses-button-next"><i class="flaticon-arrow-right"></i></div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
    <!-- courses-area-end -->

    <!-- newsletter-area -->
    <section class="newsletter__area">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-4">
                    <div class="newsletter__img-wrap">
                        <img src="uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp" alt="img">
                        <img src="frontend/img/others/newsletter_shape01.png" alt="img" data-aos="fade-up" data-aos-delay="400">
                        <img src="frontend/img/others/newsletter_shape02.png" alt="img" class="alltuchtopdown">
                    </div>
                </div>
                <div class="col-lg-8">
                    <div class="newsletter__content">
                        <h2 class="title"><b>Want to stay informed about</b> <br> <b>new courses &amp; study?</b></h2>
                        <div class="newsletter__form">
                            <form action="#" method="post" class="newsletter">
                                <?php echo csrf_field(); ?>
                                <input type="email" placeholder="Type your e-mail" name="email">
                                <button type="submit" class="btn">Subscribe Now</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="newsletter__shape">
            <img src="frontend/img/others/newsletter_shape03.png" alt="img" data-aos="fade-left" data-aos-delay="400">
        </div>
    </section>
    <!-- newsletter-area-end -->

    <!-- instructor-area -->
    <section class="instructor__area">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-xl-4">
                    <div class="instructor__content-wrap">
                        <div class="section__title mb-15">
                            <span class="sub-title">Skilled Introduce</span>
                            <h2 class="title">Our Top Class &amp; Expert Instructors in One Place</h2>
                        </div>
                        <p>Combines the ideas of empowered learning and top-tier instruction for students. Emphasizes both instructor expertise</p>
                        <div class="tg-button-wrap">
                            <a href="#" class="btn arrow-btn">See All Instructors <img src="frontend/img/icons/right_arrow.svg" alt="img" class="injectable"></a>
                        </div>
                    </div>
                </div>
                <div class="col-xl-8">
                    <div class="instructor__item-wrap">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="instructor__item">
                                    <div class="instructor__thumb">
                                        <a href="#"><img src="uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp" alt="instructor"></a>
                                    </div>
                                    <div class="instructor__content">
                                        <h2 class="title"><a href="#">Expert Instructor</a></h2>
                                        <span class="designation">Senior Developer</span>
                                        <p class="avg-rating">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            (4.8 Reviews)
                                        </p>
                                        <div class="instructor__social">
                                            <ul class="list-wrap">
                                                <li><a href="#"><i class="fab fa-facebook-f"></i></a></li>
                                                <li><a href="#"><i class="fab fa-twitter"></i></a></li>
                                                <li><a href="#"><i class="fab fa-whatsapp"></i></a></li>
                                                <li><a href="#"><i class="fab fa-instagram"></i></a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- instructor-area-end -->

    <!-- fact-area -->
    <section class="fact__area">
        <div class="container">
            <div class="fact__inner-wrap">
                <div class="row">
                    <div class="col-lg-3 col-sm-6">
                        <div class="fact__item">
                            <h2 class="count">25K</h2>
                            <p>Active Students</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-sm-6">
                        <div class="fact__item">
                            <h2 class="count"><?php echo e($courses->count()); ?>+</h2>
                            <p>Total Courses</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-sm-6">
                        <div class="fact__item">
                            <h2 class="count">158</h2>
                            <p>Expert Instructors</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-sm-6">
                        <div class="fact__item">
                            <h2 class="count">99%</h2>
                            <p>Satisfaction Rate</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- fact-area-end -->
</main>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('welcome', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Laravel-Apps\lernovate\resources\views/index.blade.php ENDPATH**/ ?>