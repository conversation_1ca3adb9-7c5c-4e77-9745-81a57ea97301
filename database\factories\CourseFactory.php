<?php

namespace Database\Factories;

use App\Models\Course;
use App\Models\Category;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Course>
 */
class CourseFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Course::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(3);
        $isFreeCourse = $this->faker->boolean(30); // 30% chance of being free
        $price = $isFreeCourse ? 0 : $this->faker->randomFloat(2, 19.99, 299.99);
        $discountPrice = $isFreeCourse ? 0 : ($this->faker->boolean(40) ? $this->faker->randomFloat(2, 9.99, $price - 1) : null);

        return [
            'title' => $title,
            'slug' => Str::slug($title),
            'short_description' => $this->faker->paragraph(2),
            'description' => $this->faker->paragraphs(3, true),
            'learning_objectives' => $this->faker->paragraphs(2, true),
            'requirements' => $this->faker->paragraph(),
            'category_id' => Category::factory(),
            'instructor_id' => User::factory()->state(['role' => 'admin']),
            'status' => $this->faker->randomElement(['draft', 'published', 'archived']),
            'duration' => $this->faker->randomFloat(1, 1, 50),
            'difficulty_level' => $this->faker->randomElement(['beginner', 'intermediate', 'advanced']),
            'language' => $this->faker->randomElement(['english', 'spanish', 'french', 'german']),
            'certificate' => $this->faker->boolean(70), // 70% chance of having certificate
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'is_free' => $isFreeCourse,
            'price' => $price,
            'discount_price' => $discountPrice,
            'thumbnail' => null, // We'll handle file uploads separately in tests
            'preview_video' => $this->faker->boolean(50) ? $this->faker->url() : null,
            'meta_title' => $this->faker->sentence(4),
            'meta_description' => $this->faker->paragraph(),
            'tags' => implode(',', $this->faker->words(5)),
            'created_by' => User::factory()->state(['role' => 'admin']),
            'updated_by' => User::factory()->state(['role' => 'admin']),
        ];
    }

    /**
     * Indicate that the course is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
        ]);
    }

    /**
     * Indicate that the course is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
        ]);
    }

    /**
     * Indicate that the course is archived.
     */
    public function archived(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'archived',
        ]);
    }

    /**
     * Indicate that the course is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_free' => true,
            'price' => 0,
            'discount_price' => 0,
        ]);
    }

    /**
     * Indicate that the course is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_free' => false,
            'price' => $this->faker->randomFloat(2, 19.99, 299.99),
        ]);
    }

    /**
     * Indicate that the course is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the course has a certificate.
     */
    public function withCertificate(): static
    {
        return $this->state(fn (array $attributes) => [
            'certificate' => true,
        ]);
    }

    /**
     * Indicate that the course is for beginners.
     */
    public function beginner(): static
    {
        return $this->state(fn (array $attributes) => [
            'difficulty_level' => 'beginner',
        ]);
    }

    /**
     * Indicate that the course is intermediate level.
     */
    public function intermediate(): static
    {
        return $this->state(fn (array $attributes) => [
            'difficulty_level' => 'intermediate',
        ]);
    }

    /**
     * Indicate that the course is advanced level.
     */
    public function advanced(): static
    {
        return $this->state(fn (array $attributes) => [
            'difficulty_level' => 'advanced',
        ]);
    }
}
