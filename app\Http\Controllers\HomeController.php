<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Category;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the home page with courses
     */
    public function index()
    {
        // Get featured courses (limit to 8 for the slider)
        $featuredCourses = Course::with(['category', 'instructor'])
            ->published()
            ->featured()
            ->orderBy('created_at', 'desc')
            ->limit(8)
            ->get();

        // Get all published courses for the main section (limit to 12)
        $courses = Course::with(['category', 'instructor'])
            ->published()
            ->orderBy('created_at', 'desc')
            ->limit(12)
            ->get();

        // Get courses by category for tabs
        $categories = Category::active()
            ->whereHas('courses', function($query) {
                $query->published();
            })
            ->with(['courses' => function($query) {
                $query->published()
                      ->with(['category', 'instructor'])
                      ->orderBy('created_at', 'desc')
                      ->limit(8);
            }])
            ->limit(5)
            ->get();

        return view('index', compact('featuredCourses', 'courses', 'categories'));
    }

    /**
     * Display a specific course
     */
    public function show($slug)
    {
        $course = Course::with(['category', 'instructor'])
            ->where('slug', $slug)
            ->published()
            ->firstOrFail();

        // Get related courses from the same category
        $relatedCourses = Course::with(['category', 'instructor'])
            ->where('category_id', $course->category_id)
            ->where('id', '!=', $course->id)
            ->published()
            ->limit(4)
            ->get();

        return view('course-detail', compact('course', 'relatedCourses'));
    }
}
