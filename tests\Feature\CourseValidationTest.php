<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CourseValidationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $category;
    protected $instructor;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);

        // Create a category
        $this->category = Category::factory()->create([
            'name' => 'Test Category',
            'is_active' => true
        ]);

        // Create an instructor
        $this->instructor = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function course_creation_requires_status_field()
    {
        $courseData = $this->getValidCourseData();
        unset($courseData['status']); // Remove status field

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $courseData);

        $response->assertSessionHasErrors(['status']);
        $this->assertDatabaseMissing('courses', ['title' => $courseData['title']]);
    }

    /** @test */
    public function course_creation_validates_status_values()
    {
        $invalidStatuses = ['invalid', 'pending', 'active', ''];

        foreach ($invalidStatuses as $status) {
            $courseData = $this->getValidCourseData();
            $courseData['status'] = $status;

            $response = $this->actingAs($this->admin)
                ->post(route('admin.courses.store'), $courseData);

            $response->assertSessionHasErrors(['status']);
            $this->assertDatabaseMissing('courses', ['title' => $courseData['title']]);
        }
    }

    /** @test */
    public function course_creation_accepts_valid_status_values()
    {
        $validStatuses = ['draft', 'published', 'archived'];

        foreach ($validStatuses as $status) {
            $courseData = $this->getValidCourseData();
            $courseData['status'] = $status;
            $courseData['title'] = 'Test Course ' . $status; // Make title unique

            $response = $this->actingAs($this->admin)
                ->post(route('admin.courses.store'), $courseData);

            $response->assertRedirect(route('admin.courses.index'));
            $this->assertDatabaseHas('courses', [
                'title' => $courseData['title'],
                'status' => $status
            ]);
        }
    }

    /** @test */
    public function course_update_requires_status_field()
    {
        $course = Course::factory()->create([
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'status' => 'draft'
        ]);

        $updateData = $this->getValidCourseData();
        unset($updateData['status']); // Remove status field

        $response = $this->actingAs($this->admin)
            ->put(route('admin.courses.update', $course->id), $updateData);

        $response->assertSessionHasErrors(['status']);
        
        // Verify course status wasn't changed
        $course->refresh();
        $this->assertEquals('draft', $course->status);
    }

    /** @test */
    public function course_update_validates_status_values()
    {
        $course = Course::factory()->create([
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'status' => 'draft'
        ]);

        $invalidStatuses = ['invalid', 'pending', 'active', ''];

        foreach ($invalidStatuses as $status) {
            $updateData = $this->getValidCourseData();
            $updateData['status'] = $status;

            $response = $this->actingAs($this->admin)
                ->put(route('admin.courses.update', $course->id), $updateData);

            $response->assertSessionHasErrors(['status']);
            
            // Verify course status wasn't changed
            $course->refresh();
            $this->assertEquals('draft', $course->status);
        }
    }

    /** @test */
    public function course_update_accepts_valid_status_values()
    {
        $validStatuses = ['draft', 'published', 'archived'];

        foreach ($validStatuses as $status) {
            $course = Course::factory()->create([
                'category_id' => $this->category->id,
                'instructor_id' => $this->instructor->id,
                'status' => 'draft'
            ]);

            $updateData = $this->getValidCourseData();
            $updateData['status'] = $status;

            $response = $this->actingAs($this->admin)
                ->put(route('admin.courses.update', $course->id), $updateData);

            $response->assertRedirect(route('admin.courses.index'));
            
            // Verify course status was updated
            $course->refresh();
            $this->assertEquals($status, $course->status);
        }
    }

    /** @test */
    public function course_creation_form_displays_status_field_as_required()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.create'));

        $response->assertStatus(200);
        $response->assertSee('name="status"', false);
        $response->assertSee('required', false);
        $response->assertSee('value="draft"', false);
        $response->assertSee('value="published"', false);
        $response->assertSee('value="archived"', false);
    }

    /** @test */
    public function course_edit_form_displays_status_field_as_required()
    {
        $course = Course::factory()->create([
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'status' => 'published'
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.edit', $course->id));

        $response->assertStatus(200);
        $response->assertSee('name="status"', false);
        $response->assertSee('required', false);
        $response->assertSee('value="draft"', false);
        $response->assertSee('value="published"', false);
        $response->assertSee('value="archived"', false);
        // Check that current status is selected
        $response->assertSee('selected', false);
    }

    /** @test */
    public function course_validation_displays_error_messages_for_missing_status()
    {
        $courseData = $this->getValidCourseData();
        unset($courseData['status']);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $courseData);

        $response->assertSessionHasErrors(['status']);
        
        // Follow redirect to see error messages
        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.create'));
            
        // The error should be displayed in the validation errors section
        $this->assertTrue(session()->hasOldInput());
    }

    /**
     * Get valid course data for testing
     */
    private function getValidCourseData(): array
    {
        return [
            'title' => $this->faker->sentence(3),
            'short_description' => $this->faker->paragraph(2),
            'description' => $this->faker->paragraphs(3, true),
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
            'duration' => 10.5,
            'difficulty_level' => 'beginner',
            'language' => 'english',
            'price' => 99.99,
            'is_free' => false,
            'is_featured' => false,
            'certificate' => true,
        ];
    }
}
