 <div class=main-sidebar style=overflow:hidden;outline:none tabindex=1>
                <aside id=sidebar-wrapper>
                    <div class=sidebar-brand>
                        <a href=https://skillgro.websolutionus.com/admin/dashboard><img class=admin_logo
                                src=""
                                alt=SkillGro></a>
                    </div>
                    <div class="sidebar-brand sidebar-brand-sm sf-hidden">

                    </div>
                    <ul class=sidebar-menu>
                        <li class=active>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/dashboard><i
                                    class="fas fa-home"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>

                        <li class=menu-header>Manage Contents</li>
                        <li class="nav-item dropdown">
                            <a href=javascript:void(0) class="nav-link has-dropdown"><i
                                    class="fas fa-graduation-cap"></i><span>Manage Courses</span></a>
                            <ul class=dropdown-menu style=display:none>
                                <li><a class="nav-link" href="/admin/courses">All Courses</a></li>
                                <li><a class="nav-link" href="/admin/courses/create">Add New Course</a></li>
                                <li><a class="nav-link" href="/admin/course-categories">Course Categories</a></li>
                                <li><a class="nav-link" href="/admin/course-levels">Course Levels</a></li>
                                <li><a class="nav-link" href="/admin/course-languages">Course Languages</a></li>





                            </ul>
                        </li>

                        <li>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/certificate-builder><i
                                    class="fas fa-certificate"></i>
                                <span>Certificate Builder</span>
                            </a>
                        </li>
                        <li>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/badges><i
                                    class="fas fa-award"></i>
                                <span>Badges</span>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a href=javascript:void(0) class="nav-link has-dropdown"><i
                                    class="fas fa-newspaper"></i><span>Manage Blogs</span></a>
                            <ul class=dropdown-menu style=display:none>
                                <li><a class="nav-link" href="/admin/blogs">All Blogs</a></li>
                                <li><a class="nav-link" href="/admin/blogs/create">Add New Blog</a></li>
                                <li><a class="nav-link" href="/admin/blog-categories">Blog Categories</a></li>
                            </ul>
                        </li>

                        <li class=menu-header>Manage Orders</li>
                        <li class="nav-item dropdown">
                            <a href=# class="nav-link has-dropdown"><i class="fas fa-shopping-bag"></i>
                                <span>Manage Order </span>
                            </a>
                            <ul class=dropdown-menu style=display:none>
                                <li><a class="nav-link" href="/admin/orders">All Orders</a></li>
                                <li><a class="nav-link" href="/admin/orders/pending">Pending Orders</a></li>
                                <li><a class="nav-link" href="/admin/orders/completed">Completed Orders</a></li>
                                <li class="dropdown-close-btn"><a href="javascript:void(0)" class="nav-link text-muted"><small>✕ Close Menu</small></a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a href=# class="nav-link has-dropdown"><i class="fas fa-money-bill-wave"></i>
                                <span>Manage Coupon </span>
                            </a>
                            <ul class=dropdown-menu style=display:none>
                                <li><a class="nav-link" href="/admin/coupons">All Coupons</a></li>
                                <li><a class="nav-link" href="/admin/coupons/create">Create Coupon</a></li>
                                <li class="dropdown-close-btn"><a href="javascript:void(0)" class="nav-link text-muted"><small>✕ Close Menu</small></a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a href=# class="nav-link has-dropdown"><i class="fas fa-hand-holding-usd"></i><span
                                    class="beep parent">Withdraw Payment</span></a>
                            <ul class=dropdown-menu style=display:none>


                            </ul>

                        <li class=menu-header>Manage Users</li>
                        <li class="nav-item dropdown">
                            <a href=javascript:void(0) class="nav-link has-dropdown"><i
                                    class="fas fa-chalkboard-teacher"></i><span class="beep parent">Instructor
                                    Requests</span></a>
                            <ul class=dropdown-menu style=display:none>


                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a href=javascript:void(0) class="nav-link has-dropdown">
                                <i class="fas fa-users"></i><span>Manage Users</span>
                            </a>
                            <ul class=dropdown-menu style=display:none>






                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a href=javascript:void(0) class="nav-link has-dropdown"><i
                                    class="fas fa-location-arrow"></i><span>Locations</span></a>
                            <ul class=dropdown-menu style=display:none>

                            </ul>
                        </li>

                        <li class=menu-header>Site Contents</li>
                        <li class="nav-item dropdown">
                            <a href=# class="nav-link has-dropdown"><i class="fas fa-swatchbook"></i>
                                <span>Appearance </span>
                            </a>
                            <ul class=dropdown-menu style=display:none>



                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a href=javascript:void(0) class="nav-link has-dropdown"><i
                                    class="fas fa-puzzle-piece"></i><span>Sections</span></a>
                            <ul class=dropdown-menu style=display:none>










                            </ul>
                        </li>

                        <li>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/brand><i
                                    class="fas fa-copyright"></i>
                                <span>Brands</span>
                            </a>
                        </li>
                        <li>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/footersetting>
                                <i class="fas fa-shoe-prints"></i> <span>Footer Setting</span>
                            </a>
                        </li>

                        <li class=menu-header>Manage Website</li>
                        <li>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/menu-builder><i
                                    class="fas fa-th"></i>
                                <span>Menu Builder</span>
                            </a>
                        </li>

                        <li>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/page-builder>
                                <i class="fas fa-file"></i> <span>Page Builder</span>
                            </a>
                        </li>

                        <li>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/social-link>
                                <i class="fas fa-hashtag"></i> <span>Social Links</span>
                            </a>
                        </li>

                        <li>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/faq>
                                <i class="fas fa-question-circle"></i> <span>FAQs</span>
                            </a>
                        </li>

                        <li class=menu-header>Settings</li>
                        <li>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/settings><i
                                    class="fas fa-cog"></i>
                                <span>Settings</span>
                            </a>
                        </li>

                        <li class=menu-header>Utility</li>
                        <li class="nav-item dropdown">
                            <a href=javascript:void(0) class="nav-link has-dropdown"><i
                                    class="fas fa-bullhorn"></i><span>NewsLetter</span></a>
                            <ul class=dropdown-menu style=display:none>


                            </ul>
                        </li>

                        <li>
                            <a class=nav-link href=https://skillgro.websolutionus.com/admin/testimonial>
                                <i class="fas fa-comment"></i> <span>Testimonial</span>
                            </a>
                        </li>

                        <li><a class=nav-link href=https://skillgro.websolutionus.com/admin/contact-messages><i
                                    class="fas fa-envelope"></i> <span>Contact Messages</span></a></li>
                        <li class="nav-item dropdown" id=addon_sidemenu style=display:none>


                        </li>
                    </ul>
                    <div class="py-3 text-center">
                        <div class="btn-sm-group-vertical version_button" role=group
                            aria-label="Responsive button group">
                            <button class="btn btn-primary logout_btn mt-2" disabled>Version
                                2.5.0</button>
                            <button class="btn btn-danger mt-2"><i class="fas fa-sign-out-alt"></i></button>
                        </div>
                    </div>
                </aside>
            </div>

<!-- jQuery (required for dropdown functionality) -->
<script src="/js/jquery-3.6.0.min.js"></script>

<!-- Dropdown Menu Script -->
<script>
// Prevent multiple initializations
if (window.dropdownInitialized) {
    console.log('Dropdown already initialized, skipping...');
} else {
    window.dropdownInitialized = true;
    console.log('=== INITIALIZING SINGLE DROPDOWN FUNCTIONALITY ===');

    // Use pure JavaScript for better compatibility and no conflicts
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Document ready, initializing dropdowns...');

        // Get all dropdown triggers
        const dropdownTriggers = document.querySelectorAll('.nav-item.dropdown > .nav-link.has-dropdown');
        console.log('Found dropdown triggers:', dropdownTriggers.length);

        // Remove any existing event listeners to prevent duplicates
        dropdownTriggers.forEach(function(trigger) {
            // Clone node to remove all event listeners
            const newTrigger = trigger.cloneNode(true);
            trigger.parentNode.replaceChild(newTrigger, trigger);
        });

        // Re-get the triggers after cloning
        const cleanTriggers = document.querySelectorAll('.nav-item.dropdown > .nav-link.has-dropdown');

        cleanTriggers.forEach(function(trigger) {
            trigger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Dropdown clicked!');

                const parent = this.closest('.nav-item.dropdown');
                const menu = parent.querySelector('.dropdown-menu');

                if (!parent || !menu) {
                    console.error('Parent or menu not found');
                    return;
                }

                // Check if currently visible
                const isVisible = menu.style.display === 'block' || (!menu.style.display && menu.offsetHeight > 0);

                // Simple toggle
                if (isVisible) {
                    console.log('Closing dropdown');
                    menu.style.display = 'none';
                    parent.classList.remove('active');
                } else {
                    console.log('Opening dropdown');
                    menu.style.display = 'block';
                    parent.classList.add('active');
                }
            });
        });

        // Handle menu item clicks
        document.querySelectorAll('.dropdown-menu a').forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.stopPropagation();
                console.log('Menu item clicked:', this.textContent, '- dropdown stays open');

                // Check if this is a close button
                if (this.closest('.dropdown-close-btn')) {
                    e.preventDefault();
                    const dropdown = this.closest('.nav-item.dropdown');
                    const menu = dropdown.querySelector('.dropdown-menu');
                    menu.style.display = 'none';
                    dropdown.classList.remove('active');
                    console.log('Close button clicked - closing dropdown');
                    return;
                }

                // Add active state
                document.querySelectorAll('.dropdown-menu a').forEach(function(otherLink) {
                    otherLink.classList.remove('menu-item-active');
                });
                this.classList.add('menu-item-active');
            });
        });

        // Handle sidebar toggle
        const sidebarToggle = document.querySelector('[data-toggle="sidebar"]');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                document.body.classList.toggle('sidebar-gone');
            });
        }

        console.log('Dropdown initialization complete - single event handlers only');
    });

}
</script>

<!-- CSS for dropdown animations -->
<style>
.nav-item.dropdown.active > .nav-link {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.dropdown-menu {
    transition: all 0.3s ease;
}

.nav-item.dropdown .dropdown-menu li a {
    padding: 8px 15px;
    color: #333;
    text-decoration: none;
    display: block;
}

.nav-item.dropdown .dropdown-menu li a:hover {
    background-color: #f8f9fa;
    color: #007bff;
    transition: all 0.2s ease;
}

.nav-item.dropdown .dropdown-menu li a.menu-item-active {
    background-color: #007bff;
    color: white;
}

/* Keep dropdown open styling */
.nav-item.dropdown.active {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Smooth transitions for all dropdown interactions */
.nav-item.dropdown .dropdown-menu {
    transition: all 0.3s ease;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Better visual feedback for dropdown state */
.nav-item.dropdown.active > .nav-link:after {
    content: '';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%) rotate(180deg);
    transition: transform 0.3s ease;
}

.nav-item.dropdown > .nav-link:after {
    content: '▼';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
    font-size: 12px;
}

/* Close button styling */
.dropdown-close-btn {
    border-top: 1px solid #eee;
    margin-top: 5px;
    padding-top: 5px;
}

.dropdown-close-btn a {
    font-size: 11px;
    opacity: 0.7;
    text-align: center;
    display: block;
}

.dropdown-close-btn a:hover {
    opacity: 1;
    background-color: #f0f0f0;
}

@media (max-width: 991.98px) {
    .sidebar-menu .dropdown-menu {
        position: static !important;
        float: none !important;
        width: auto !important;
        margin-top: 0 !important;
        background-color: transparent !important;
        border: 0 !important;
        box-shadow: none !important;
        padding-left: 20px !important;
    }
}
</style>
