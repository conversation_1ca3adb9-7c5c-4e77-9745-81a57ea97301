// Admin Dashboard JavaScript

$(document).ready(function() {
    // Sidebar toggle
    $('[data-toggle="sidebar"]').on('click', function(e) {
        e.preventDefault();
        $('body').toggleClass('sidebar-gone');
    });

    // Dropdown menu toggle
    $('.nav-item.dropdown > .nav-link').on('click', function(e) {
        e.preventDefault();
        var $dropdown = $(this).parent();
        var $menu = $dropdown.find('.dropdown-menu');
        
        // Close other dropdowns
        $('.nav-item.dropdown').not($dropdown).removeClass('active').find('.dropdown-menu').slideUp();
        
        // Toggle current dropdown
        $dropdown.toggleClass('active');
        $menu.slideToggle();
    });

    // Auto-hide alerts after 5 seconds
    $('.alert').delay(5000).fadeOut();

    // Confirm delete actions
    $('.btn-delete, .delete-btn').on('click', function(e) {
        if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
            e.preventDefault();
            return false;
        }
    });

    // Form validation helper
    $('.form-control').on('blur', function() {
        var $this = $(this);
        var value = $this.val().trim();
        
        if ($this.prop('required') && value === '') {
            $this.addClass('is-invalid');
        } else {
            $this.removeClass('is-invalid');
        }
    });

    // Auto-generate slug from title
    $('input[name="title"]').on('keyup', function() {
        var title = $(this).val();
        var slug = title.toLowerCase()
            .replace(/[^\w ]+/g, '')
            .replace(/ +/g, '-');
        $('input[name="slug"]').val(slug);
    });

    // Image preview
    $('input[type="file"]').on('change', function() {
        var input = this;
        var $preview = $(input).siblings('.image-preview');
        
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            
            reader.onload = function(e) {
                if ($preview.length === 0) {
                    $preview = $('<div class="image-preview mt-2"><img src="" class="img-thumbnail" style="max-width: 200px;"></div>');
                    $(input).after($preview);
                }
                $preview.find('img').attr('src', e.target.result);
            };
            
            reader.readAsDataURL(input.files[0]);
        }
    });

    // DataTable initialization (if DataTables is loaded)
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.data-table').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']],
            columnDefs: [
                { orderable: false, targets: 'no-sort' }
            ]
        });
    }

    // Select2 initialization (if Select2 is loaded)
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            theme: 'bootstrap4',
            width: '100%'
        });
    }

    // Summernote initialization (if Summernote is loaded)
    if (typeof $.fn.summernote !== 'undefined') {
        $('.summernote').summernote({
            height: 300,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ]
        });
    }

    // Copy to clipboard functionality
    $('.copy-to-clipboard').on('click', function(e) {
        e.preventDefault();
        var text = $(this).data('text') || $(this).text();
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('Copied to clipboard!', 'success');
            });
        } else {
            // Fallback for older browsers
            var $temp = $('<input>');
            $('body').append($temp);
            $temp.val(text).select();
            document.execCommand('copy');
            $temp.remove();
            showToast('Copied to clipboard!', 'success');
        }
    });

    // Toast notification function
    function showToast(message, type = 'info') {
        var toastClass = 'alert-' + type;
        var toast = $('<div class="alert ' + toastClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">' +
            message +
            '<button type="button" class="close" data-dismiss="alert">' +
            '<span>&times;</span>' +
            '</button>' +
            '</div>');
        
        $('body').append(toast);
        
        setTimeout(function() {
            toast.fadeOut(function() {
                $(this).remove();
            });
        }, 3000);
    }

    // Bulk actions
    $('#select-all').on('change', function() {
        $('.item-checkbox').prop('checked', this.checked);
        toggleBulkActions();
    });

    $('.item-checkbox').on('change', function() {
        var totalCheckboxes = $('.item-checkbox').length;
        var checkedCheckboxes = $('.item-checkbox:checked').length;
        
        $('#select-all').prop('checked', totalCheckboxes === checkedCheckboxes);
        toggleBulkActions();
    });

    function toggleBulkActions() {
        var checkedCount = $('.item-checkbox:checked').length;
        if (checkedCount > 0) {
            $('.bulk-actions').show();
            $('.bulk-count').text(checkedCount);
        } else {
            $('.bulk-actions').hide();
        }
    }

    // Sortable lists (if jQuery UI is loaded)
    if (typeof $.fn.sortable !== 'undefined') {
        $('.sortable').sortable({
            handle: '.sort-handle',
            update: function(event, ui) {
                var order = $(this).sortable('toArray', {attribute: 'data-id'});
                // Send AJAX request to update order
                // Implementation depends on specific requirements
            }
        });
    }
});

// Global CSRF token setup for AJAX requests
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

// Global error handler for AJAX requests
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    if (xhr.status === 419) {
        alert('Your session has expired. Please refresh the page and try again.');
        location.reload();
    } else if (xhr.status === 403) {
        alert('You do not have permission to perform this action.');
    } else if (xhr.status >= 500) {
        alert('A server error occurred. Please try again later.');
    }
});
