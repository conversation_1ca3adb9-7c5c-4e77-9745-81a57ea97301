/**
 * Admin Dashboard Dropdown Menu Script
 * Handles sidebar dropdown functionality for the admin dashboard
 */

(function($) {
    'use strict';

    // Initialize dropdown functionality when document is ready
    $(document).ready(function() {
        initializeDropdowns();
        initializeSidebarToggle();
        initializeSearchFunctionality();
    });

    /**
     * Initialize all dropdown menus in the sidebar
     */
    function initializeDropdowns() {
        // Handle dropdown toggle clicks
        $('.nav-item.dropdown > .nav-link.has-dropdown').on('click', function(e) {
            e.preventDefault();
            
            const $this = $(this);
            const $parent = $this.parent('.nav-item.dropdown');
            const $dropdownMenu = $parent.find('.dropdown-menu');
            
            // Toggle current dropdown
            toggleDropdown($parent, $dropdownMenu);
            
            // Close other open dropdowns (accordion behavior)
            $('.nav-item.dropdown').not($parent).each(function() {
                const $otherDropdown = $(this).find('.dropdown-menu');
                if ($otherDropdown.is(':visible')) {
                    closeDropdown($(this), $otherDropdown);
                }
            });
        });

        // Handle submenu item clicks
        $('.dropdown-menu a').on('click', function(e) {
            // Allow normal navigation for submenu items
            // Remove active class from all menu items
            $('.sidebar-menu li').removeClass('active');
            
            // Add active class to parent dropdown
            $(this).closest('.nav-item.dropdown').addClass('active');
        });

        // Set initial active states based on current URL
        setActiveMenuItems();
    }

    /**
     * Toggle dropdown menu open/close
     */
    function toggleDropdown($parent, $dropdownMenu) {
        if ($dropdownMenu.is(':visible')) {
            closeDropdown($parent, $dropdownMenu);
        } else {
            openDropdown($parent, $dropdownMenu);
        }
    }

    /**
     * Open dropdown menu with animation
     */
    function openDropdown($parent, $dropdownMenu) {
        $parent.addClass('active');
        $dropdownMenu.slideDown(300, function() {
            $(this).css('display', 'block');
        });
        
        // Add rotation to dropdown arrow if exists
        $parent.find('.fas.fa-chevron-down, .fas.fa-angle-down').addClass('rotate-180');
    }

    /**
     * Close dropdown menu with animation
     */
    function closeDropdown($parent, $dropdownMenu) {
        $parent.removeClass('active');
        $dropdownMenu.slideUp(300, function() {
            $(this).css('display', 'none');
        });
        
        // Remove rotation from dropdown arrow if exists
        $parent.find('.fas.fa-chevron-down, .fas.fa-angle-down').removeClass('rotate-180');
    }

    /**
     * Set active menu items based on current URL
     */
    function setActiveMenuItems() {
        const currentPath = window.location.pathname;
        
        // Remove all active classes first
        $('.sidebar-menu li').removeClass('active');
        
        // Check each menu item
        $('.sidebar-menu a').each(function() {
            const $link = $(this);
            const href = $link.attr('href');
            
            if (href && currentPath.includes(href.split('/').pop())) {
                const $parent = $link.closest('li');
                $parent.addClass('active');
                
                // If it's inside a dropdown, open the dropdown
                if ($parent.closest('.nav-item.dropdown').length) {
                    const $dropdownParent = $parent.closest('.nav-item.dropdown');
                    const $dropdownMenu = $dropdownParent.find('.dropdown-menu');
                    $dropdownParent.addClass('active');
                    $dropdownMenu.show();
                }
            }
        });
    }

    /**
     * Initialize sidebar toggle functionality
     */
    function initializeSidebarToggle() {
        $('[data-toggle="sidebar"]').on('click', function(e) {
            e.preventDefault();
            $('body').toggleClass('sidebar-gone');
        });
    }

    /**
     * Initialize search functionality for admin menu
     */
    function initializeSearchFunctionality() {
        const $searchInput = $('#search_menu');
        const $searchResults = $('#admin_menu_list');
        
        if ($searchInput.length && $searchResults.length) {
            $searchInput.on('input', function() {
                const searchTerm = $(this).val().toLowerCase().trim();
                
                if (searchTerm.length > 0) {
                    performMenuSearch(searchTerm, $searchResults);
                } else {
                    $searchResults.addClass('d-none').empty();
                }
            });
            
            // Hide search results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.search-box').length) {
                    $searchResults.addClass('d-none').empty();
                }
            });
        }
    }

    /**
     * Perform search in menu items
     */
    function performMenuSearch(searchTerm, $searchResults) {
        const results = [];
        
        // Search through all menu items
        $('.sidebar-menu a').each(function() {
            const $link = $(this);
            const text = $link.find('span').text().toLowerCase();
            const href = $link.attr('href');
            
            if (text.includes(searchTerm) && href && href !== '#' && href !== 'javascript:void(0)') {
                results.push({
                    text: $link.find('span').text(),
                    href: href,
                    icon: $link.find('i').attr('class') || 'fas fa-link'
                });
            }
        });
        
        // Display search results
        displaySearchResults(results, $searchResults);
    }

    /**
     * Display search results
     */
    function displaySearchResults(results, $searchResults) {
        $searchResults.empty();
        
        if (results.length > 0) {
            results.forEach(function(result) {
                const $resultItem = $(`
                    <a href="${result.href}" class="d-block p-2 text-decoration-none border-bottom">
                        <i class="${result.icon} mr-2"></i>
                        ${result.text}
                    </a>
                `);
                $searchResults.append($resultItem);
            });
            $searchResults.removeClass('d-none');
        } else {
            $searchResults.html('<div class="p-2 text-muted">No results found</div>');
            $searchResults.removeClass('d-none');
        }
    }

    /**
     * Add smooth scrolling to sidebar
     */
    function initializeSmoothScrolling() {
        if (typeof $.fn.niceScroll !== 'undefined') {
            $('.main-sidebar').niceScroll({
                cursorcolor: '#6777ef',
                cursorwidth: '6px',
                cursorborder: 'none',
                cursorborderradius: '3px',
                scrollspeed: 60,
                mousescrollstep: 40,
                hwacceleration: true
            });
        }
    }

    // Initialize smooth scrolling if niceScroll is available
    initializeSmoothScrolling();

    /**
     * Handle responsive behavior
     */
    function handleResponsiveBehavior() {
        $(window).on('resize', function() {
            if ($(window).width() < 992) {
                // Close all dropdowns on mobile
                $('.nav-item.dropdown .dropdown-menu').hide();
                $('.nav-item.dropdown').removeClass('active');
            }
        });
    }

    // Initialize responsive behavior
    handleResponsiveBehavior();

})(jQuery);

/**
 * CSS for dropdown animations (add to your CSS file)
 */
const dropdownCSS = `
<style>
.rotate-180 {
    transform: rotate(180deg);
    transition: transform 0.3s ease;
}

.dropdown-menu {
    transition: all 0.3s ease;
}

.nav-item.dropdown.active > .nav-link {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.search-box #admin_menu_list {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    width: 100%;
    top: 100%;
}

.search-box #admin_menu_list a:hover {
    background-color: #f8f9fa;
}

@media (max-width: 991.98px) {
    .sidebar-menu .dropdown-menu {
        position: static !important;
        float: none !important;
        width: auto !important;
        margin-top: 0 !important;
        background-color: transparent !important;
        border: 0 !important;
        box-shadow: none !important;
        padding-left: 20px !important;
    }
}
</style>
`;

// Inject CSS if not already present
if (!document.querySelector('#dropdown-styles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'dropdown-styles';
    styleElement.innerHTML = dropdownCSS;
    document.head.appendChild(styleElement);
}
