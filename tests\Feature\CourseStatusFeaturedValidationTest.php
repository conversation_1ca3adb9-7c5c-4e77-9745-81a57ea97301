<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Category;
use App\Models\Course;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CourseStatusFeaturedValidationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $category;
    protected $instructor;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);

        // Create a category
        $this->category = Category::factory()->create([
            'name' => 'Test Category',
            'is_active' => true
        ]);

        // Create an instructor
        $this->instructor = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function course_creation_requires_status_field_with_featured_checkbox()
    {
        $courseData = $this->getValidCourseData();
        unset($courseData['status']); // Remove status field
        $courseData['is_featured'] = true; // Set featured checkbox

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $courseData);

        $response->assertSessionHasErrors(['status']);
        $this->assertDatabaseMissing('courses', ['title' => $courseData['title']]);
    }

    /** @test */
    public function course_creation_accepts_status_with_featured_checkbox_checked()
    {
        $courseData = $this->getValidCourseData();
        $courseData['status'] = 'published';
        $courseData['is_featured'] = true;

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $courseData);

        $response->assertRedirect(route('admin.courses.index'));
        $this->assertDatabaseHas('courses', [
            'title' => $courseData['title'],
            'status' => 'published',
            'is_featured' => true
        ]);
    }

    /** @test */
    public function course_creation_accepts_status_with_featured_checkbox_unchecked()
    {
        $courseData = $this->getValidCourseData();
        $courseData['status'] = 'draft';
        // Remove is_featured to simulate unchecked checkbox
        unset($courseData['is_featured']);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $courseData);

        $response->assertRedirect(route('admin.courses.index'));
        $this->assertDatabaseHas('courses', [
            'title' => $courseData['title'],
            'status' => 'draft',
            'is_featured' => false
        ]);
    }

    /** @test */
    public function course_creation_validates_status_values_with_featured_checkbox()
    {
        $invalidStatuses = ['invalid', 'pending', 'active', ''];

        foreach ($invalidStatuses as $status) {
            $courseData = $this->getValidCourseData();
            $courseData['status'] = $status;
            $courseData['is_featured'] = true;
            $courseData['title'] = 'Test Course ' . $status . time(); // Make title unique

            $response = $this->actingAs($this->admin)
                ->post(route('admin.courses.store'), $courseData);

            $response->assertSessionHasErrors(['status']);
            $this->assertDatabaseMissing('courses', ['title' => $courseData['title']]);
        }
    }

    /** @test */
    public function course_creation_form_displays_both_status_and_featured_fields()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.courses.create'));

        $response->assertStatus(200);
        $response->assertSee('name="status"', false);
        $response->assertSee('required', false);
        $response->assertSee('name="is_featured"', false);
        $response->assertSee('Featured Course');
    }

    /** @test */
    public function course_creation_with_free_course_and_featured_checkbox()
    {
        $courseData = $this->getValidCourseData();
        $courseData['status'] = 'published';
        $courseData['is_featured'] = true;
        $courseData['is_free'] = true;
        unset($courseData['price']); // Remove price for free course

        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), $courseData);

        $response->assertRedirect(route('admin.courses.index'));
        $this->assertDatabaseHas('courses', [
            'title' => $courseData['title'],
            'status' => 'published',
            'is_featured' => true,
            'is_free' => true,
            'price' => 0
        ]);
    }

    /** @test */
    public function course_creation_handles_all_checkbox_combinations()
    {
        // Test all combinations of checkboxes
        $combinations = [
            ['is_featured' => false, 'is_free' => false, 'certificate' => false],
            ['is_featured' => true, 'is_free' => false, 'certificate' => false],
            ['is_featured' => false, 'is_free' => true, 'certificate' => false],
            ['is_featured' => true, 'is_free' => true, 'certificate' => false],
            ['is_featured' => false, 'is_free' => false, 'certificate' => true],
            ['is_featured' => true, 'is_free' => false, 'certificate' => true],
            ['is_featured' => false, 'is_free' => true, 'certificate' => true],
            ['is_featured' => true, 'is_free' => true, 'certificate' => true],
        ];

        foreach ($combinations as $index => $checkboxes) {
            $courseData = $this->getValidCourseData();
            $courseData['status'] = 'published';
            $courseData['title'] = 'Test Course ' . $index; // Make title unique

            // Remove checkboxes that should be unchecked (simulate browser behavior)
            if (!$checkboxes['is_featured']) {
                unset($courseData['is_featured']);
            } else {
                $courseData['is_featured'] = true;
            }

            if (!$checkboxes['is_free']) {
                unset($courseData['is_free']);
            } else {
                $courseData['is_free'] = true;
                unset($courseData['price']); // Free courses don't have price
            }

            if (!$checkboxes['certificate']) {
                unset($courseData['certificate']);
            } else {
                $courseData['certificate'] = true;
            }

            $response = $this->actingAs($this->admin)
                ->post(route('admin.courses.store'), $courseData);

            $response->assertRedirect(route('admin.courses.index'));
            $this->assertDatabaseHas('courses', [
                'title' => $courseData['title'],
                'status' => 'published',
                'is_featured' => $checkboxes['is_featured'],
                'is_free' => $checkboxes['is_free'],
                'certificate' => $checkboxes['certificate'],
            ]);
        }
    }

    /** @test */
    public function course_creation_form_submission_with_status_and_featured_validation()
    {
        // Test form submission with missing status field
        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), [
                'title' => 'Test Course',
                'category_id' => $this->category->id,
                'instructor_id' => $this->instructor->id,
                // Missing status field
                'is_featured' => '1', // Checkbox checked
            ]);

        $response->assertSessionHasErrors(['status']);

        // Test form submission with valid status and featured checkbox
        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), [
                'title' => 'Test Course Valid',
                'category_id' => $this->category->id,
                'instructor_id' => $this->instructor->id,
                'status' => 'published',
                'is_featured' => '1', // Checkbox checked
            ]);

        $response->assertRedirect(route('admin.courses.index'));
        $this->assertDatabaseHas('courses', [
            'title' => 'Test Course Valid',
            'status' => 'published',
            'is_featured' => true,
        ]);

        // Test form submission with valid status but no featured checkbox (unchecked)
        $response = $this->actingAs($this->admin)
            ->post(route('admin.courses.store'), [
                'title' => 'Test Course No Featured',
                'category_id' => $this->category->id,
                'instructor_id' => $this->instructor->id,
                'status' => 'draft',
                // No is_featured field (unchecked checkbox)
            ]);

        $response->assertRedirect(route('admin.courses.index'));
        $this->assertDatabaseHas('courses', [
            'title' => 'Test Course No Featured',
            'status' => 'draft',
            'is_featured' => false,
        ]);
    }

    /**
     * Get valid course data for testing
     */
    private function getValidCourseData(): array
    {
        return [
            'title' => $this->faker->sentence(3),
            'short_description' => $this->faker->paragraph(2),
            'description' => $this->faker->paragraphs(3, true),
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
            'duration' => 10.5,
            'difficulty_level' => 'beginner',
            'language' => 'english',
            'price' => 99.99,
            'is_free' => false,
            'is_featured' => false,
            'certificate' => true,
        ];
    }
}
