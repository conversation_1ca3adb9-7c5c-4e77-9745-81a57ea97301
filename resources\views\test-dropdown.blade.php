<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropdown</title>
    <link rel="stylesheet" href="/frontend/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta http-equiv="content-security-policy" content="default-src 'self'; font-src 'self' data: https:; img-src 'self' data: https:; style-src 'self' 'unsafe-inline' https:; media-src 'self' data:; script-src 'self' 'unsafe-inline'; object-src 'self' data:; frame-src 'self' data:; connect-src 'self';">
</head>
<body>
    <div id="app">
        <div class="main-wrapper">
            <div class="navbar-bg"></div>
            <nav class="navbar navbar-expand-lg main-navbar">
                <div class="mr-2 form-inline">
                    <ul class="mr-3 navbar-nav d-flex align-items-center">
                        <li><a href="#" data-toggle="sidebar" class="nav-link nav-link-lg"><i class="fas fa-bars"></i></a></li>
                    </ul>
                </div>
            </nav>

            @include('user.includes.sidebar')

            <div class="main-content" style="min-height:643px">
                <section class="section">
                    <div class="section-header">
                        <h1>Test Dropdown Functionality</h1>
                    </div>
                    <div class="section-body">
                        <div class="card">
                            <div class="card-body">
                                <h4>Dropdown Test Page</h4>
                                <p>Click on the dropdown menu items in the sidebar to test the functionality:</p>
                                <ul>
                                    <li><strong>Manage Courses</strong> - Should expand to show course management options</li>
                                    <li><strong>Manage Blogs</strong> - Should expand to show blog management options</li>
                                    <li><strong>Manage Order</strong> - Should expand to show order management options</li>
                                    <li><strong>Manage Coupon</strong> - Should expand to show coupon management options</li>
                                </ul>

                                <div class="alert alert-info mt-4">
                                    <h5>Expected Behavior:</h5>
                                    <ul>
                                        <li>✅ Smooth slide down animation when opening</li>
                                        <li>✅ Smooth slide up animation when closing</li>
                                        <li>✅ Only one dropdown open at a time (accordion behavior)</li>
                                        <li>✅ Active state highlighting</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</body>
</html>
