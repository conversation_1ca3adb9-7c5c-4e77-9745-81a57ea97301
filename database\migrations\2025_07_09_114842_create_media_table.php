<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('media', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('original_name');
            $table->string('file_path');
            $table->string('file_type'); // image, document, video, etc.
            $table->string('mime_type');
            $table->bigInteger('file_size'); // in bytes
            $table->json('dimensions')->nullable(); // width, height for images
            $table->text('alt_text')->nullable();
            $table->text('description')->nullable();
            $table->string('folder')->default('uploads'); // organization folder
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            $table->index(['file_type', 'created_at']);
            $table->index('folder');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('media');
    }
};
