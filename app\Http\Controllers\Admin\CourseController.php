<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Category;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class CourseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $courses = Course::with(['category', 'instructor'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $categories = Category::where('is_active', true)->get();

        // Statistics
        $totalCourses = Course::count();
        $publishedCourses = Course::where('status', 'published')->count();
        $draftCourses = Course::where('status', 'draft')->count();
        $featuredCourses = Course::where('is_featured', true)->count();

        return view('admin.courses.index', compact(
            'courses',
            'categories',
            'totalCourses',
            'publishedCourses',
            'draftCourses',
            'featuredCourses'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::where('is_active', true)
            ->orderBy('name')
            ->get();

        $instructors = User::where('role', 'admin')
            ->orderBy('name')
            ->get();

        return view('admin.courses.create', compact('categories', 'instructors'));
    }

    /**
     * Show the debug form for testing course creation.
     */
    public function debug()
    {
        $categories = Category::where('is_active', true)
            ->orderBy('name')
            ->get();

        $instructors = User::where('role', 'admin')
            ->orderBy('name')
            ->get();

        return view('admin.courses.debug', compact('categories', 'instructors'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:courses,slug',
            'short_description' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'learning_objectives' => 'nullable|string',
            'requirements' => 'nullable|string',
            'category_id' => 'required|integer',
            'instructor_id' => 'required|integer',
            'status' => 'required|in:draft,published,archived',
            'duration' => 'nullable|numeric|min:0',
            'difficulty_level' => 'nullable|in:beginner,intermediate,advanced',
            'language' => 'nullable|string',
            'certificate' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_free' => 'nullable|boolean',
            'price' => 'nullable|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'preview_video' => 'nullable|url',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ]);

        try {
            // Generate slug if not provided
            $slug = $request->slug ?: Str::slug($request->title);

            // Handle thumbnail upload
            $thumbnailPath = null;
            if ($request->hasFile('thumbnail')) {
                $thumbnailPath = $request->file('thumbnail')->store('courses', 'public');
            }

            // Validate pricing logic
            if (!$request->has('is_free')) {
                if ($request->discount_price && $request->discount_price >= $request->price) {
                    return redirect()->back()
                        ->withInput()
                        ->with('error', 'Discount price must be less than regular price.');
                }
            }

            // Create the course
            $course = Course::create([
                'title' => $request->title,
                'slug' => $slug,
                'short_description' => $request->short_description,
                'description' => $request->description,
                'learning_objectives' => $request->learning_objectives,
                'requirements' => $request->requirements,
                'category_id' => $request->category_id,
                'instructor_id' => $request->instructor_id,
                'status' => $request->status,
                'duration' => $request->duration,
                'difficulty_level' => $request->difficulty_level,
                'language' => $request->language,
                'certificate' => $request->has('certificate'),
                'is_featured' => $request->has('is_featured'),
                'is_free' => $request->has('is_free'),
                'price' => $request->has('is_free') ? 0 : $request->price,
                'discount_price' => $request->has('is_free') ? 0 : $request->discount_price,
                'thumbnail' => $thumbnailPath,
                'preview_video' => $request->preview_video,
                'meta_title' => $request->meta_title,
                'meta_description' => $request->meta_description,
                'tags' => $request->tags,
                'created_by' => Auth::id(),
                'updated_by' => Auth::id(),
            ]);

            return redirect()->route('admin.courses.index')
                ->with('success', 'Course created successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error creating course: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // For now, redirect to edit
        return redirect()->route('admin.courses.edit', $id);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        try {
            $course = Course::with(['category', 'instructor'])->findOrFail($id);

            $categories = Category::where('is_active', true)
                ->orderBy('name')
                ->get();

            $instructors = User::where('role', 'admin')
                ->orderBy('name')
                ->get();

            return view('admin.courses.edit', compact('course', 'categories', 'instructors'));
        } catch (\Exception $e) {
            return redirect()->route('admin.courses.index')
                ->with('error', 'Course not found.');
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:courses,slug,' . $id,
            'short_description' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'learning_objectives' => 'nullable|string',
            'requirements' => 'nullable|string',
            'category_id' => 'required|integer',
            'instructor_id' => 'required|integer',
            'status' => 'required|in:draft,published,archived',
            'duration' => 'nullable|numeric|min:0',
            'difficulty_level' => 'nullable|in:beginner,intermediate,advanced',
            'language' => 'nullable|string',
            'certificate' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_free' => 'nullable|boolean',
            'price' => 'nullable|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'preview_video' => 'nullable|url',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ]);

        try {
            $course = Course::findOrFail($id);

            // Generate slug if not provided
            $slug = $request->slug ?: Str::slug($request->title);

            // Handle thumbnail upload
            $thumbnailPath = $course->thumbnail; // Keep existing thumbnail by default
            if ($request->hasFile('thumbnail')) {
                // Delete old thumbnail if it exists
                if ($course->thumbnail && Storage::disk('public')->exists($course->thumbnail)) {
                    Storage::disk('public')->delete($course->thumbnail);
                }
                $thumbnailPath = $request->file('thumbnail')->store('courses', 'public');
            }

            // Validate pricing logic
            if (!$request->has('is_free')) {
                if ($request->discount_price && $request->discount_price >= $request->price) {
                    return redirect()->back()
                        ->withInput()
                        ->with('error', 'Discount price must be less than regular price.');
                }
            }

            // Update the course
            $course->update([
                'title' => $request->title,
                'slug' => $slug,
                'short_description' => $request->short_description,
                'description' => $request->description,
                'learning_objectives' => $request->learning_objectives,
                'requirements' => $request->requirements,
                'category_id' => $request->category_id,
                'instructor_id' => $request->instructor_id,
                'status' => $request->status,
                'duration' => $request->duration,
                'difficulty_level' => $request->difficulty_level,
                'language' => $request->language,
                'certificate' => $request->has('certificate'),
                'is_featured' => $request->has('is_featured'),
                'is_free' => $request->has('is_free'),
                'price' => $request->has('is_free') ? 0 : $request->price,
                'discount_price' => $request->has('is_free') ? 0 : $request->discount_price,
                'thumbnail' => $thumbnailPath,
                'preview_video' => $request->preview_video,
                'meta_title' => $request->meta_title,
                'meta_description' => $request->meta_description,
                'tags' => $request->tags,
                'updated_by' => Auth::id(),
            ]);

            return redirect()->route('admin.courses.index')
                ->with('success', 'Course updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error updating course: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            // Here you would typically delete from database
            // Example:
            // $course = Course::findOrFail($id);
            // $course->delete();

            return redirect()->route('admin.courses.index')
                ->with('success', 'Course deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error deleting course: ' . $e->getMessage());
        }
    }

    /**
     * Handle bulk actions for courses.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:publish,draft,delete',
            'course_ids' => 'required|array',
            'course_ids.*' => 'integer',
        ]);

        try {
            $action = $request->action;
            $courseIds = $request->course_ids;
            $count = count($courseIds);

            // Here you would typically perform bulk operations on database
            // Example:
            // switch ($action) {
            //     case 'publish':
            //         Course::whereIn('id', $courseIds)->update(['status' => 'published']);
            //         break;
            //     case 'draft':
            //         Course::whereIn('id', $courseIds)->update(['status' => 'draft']);
            //         break;
            //     case 'delete':
            //         Course::whereIn('id', $courseIds)->delete();
            //         break;
            // }

            $actionText = match($action) {
                'publish' => 'published',
                'draft' => 'moved to draft',
                'delete' => 'deleted',
            };

            return redirect()->route('admin.courses.index')
                ->with('success', "{$count} courses {$actionText} successfully!");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error performing bulk action: ' . $e->getMessage());
        }
    }

    /**
     * Create a new category via AJAX
     */
    public function createCategoryAjax(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:255',
        ]);

        try {
            $category = Category::create([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'color' => $request->color ?: '#6777ef',
                'icon' => $request->icon,
                'is_active' => true,
                'sort_order' => 0,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Category created successfully!',
                'category' => [
                    'id' => $category->id,
                    'name' => $category->name,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating category: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Create a new instructor via AJAX
     */
    public function createInstructorAjax(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8',
        ]);

        try {
            $instructor = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => bcrypt($request->password),
                'role' => 'admin', // Instructors are admin users
                'email_verified_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Instructor created successfully!',
                'instructor' => [
                    'id' => $instructor->id,
                    'name' => $instructor->name,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating instructor: ' . $e->getMessage()
            ], 422);
        }
    }
}
