<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'Admin Dashboard') - {{ config('app.name', 'Lernovate') }}</title>
    
    <!-- Favicon -->
    <link rel="icon" href="{{ asset('favicon.ico') }}">
    
    <!-- Styles -->
    <link rel="stylesheet" href="{{ asset('frontend/css/admin.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    @stack('styles')
</head>

<body>
    <div id="app">
        <div class="main-wrapper">
            <!-- Navbar Background -->
            <div class="navbar-bg"></div>
            
            <!-- Top Navigation -->
            <nav class="navbar navbar-expand-lg main-navbar">
                <div class="mr-2 form-inline">
                    <ul class="mr-3 navbar-nav d-flex align-items-center">
                        <li>
                            <a href="#" data-toggle="sidebar" class="nav-link nav-link-lg">
                                <i class="fas fa-bars"></i>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <ul class="navbar-nav navbar-right">
                    <li class="dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg nav-link-user">
                            <img alt="image" src="{{ asset('frontend/images/default-avatar.png') }}" class="mr-1 rounded-circle">
                            <div class="d-sm-none d-lg-inline-block">{{ Auth::user()->name }}</div>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a href="{{ route('profile.edit') }}" class="dropdown-item has-icon">
                                <i class="far fa-user"></i> Profile
                            </a>
                            <div class="dropdown-divider"></div>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item has-icon text-danger">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </button>
                            </form>
                        </div>
                    </li>
                </ul>
            </nav>
            
            <!-- Sidebar -->
            <div class="main-sidebar">
                <aside id="sidebar-wrapper">
                    <!-- Sidebar Brand -->
                    <div class="sidebar-brand">
                        <a href="{{ route('admin.dashboard') }}">
                            <img class="admin_logo" src="{{ asset('frontend/images/logo.png') }}" alt="{{ config('app.name') }}">
                        </a>
                    </div>
                    
                    <!-- Sidebar Menu -->
                    <ul class="sidebar-menu">
                        <!-- Dashboard -->
                        <li class="{{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                            <a class="nav-link" href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        
                        <!-- Content Management -->
                        <li class="menu-header">Content Management</li>
                        <li class="nav-item dropdown {{ request()->is('admin/cms*') ? 'active' : '' }}">
                            <a href="javascript:void(0)" class="nav-link has-dropdown">
                                <i class="fas fa-edit"></i>
                                <span>Content Management</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li class="{{ request()->routeIs('admin.cms.pages*') ? 'active' : '' }}">
                                    <a class="nav-link" href="{{ route('admin.cms.pages.index') }}">
                                        <i class="fas fa-file-alt"></i> Pages
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('admin.cms.posts*') ? 'active' : '' }}">
                                    <a class="nav-link" href="{{ route('admin.cms.posts.index') }}">
                                        <i class="fas fa-blog"></i> Blog Posts
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('admin.cms.categories*') ? 'active' : '' }}">
                                    <a class="nav-link" href="{{ route('admin.cms.categories.index') }}">
                                        <i class="fas fa-folder"></i> Categories
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('admin.cms.tags*') ? 'active' : '' }}">
                                    <a class="nav-link" href="{{ route('admin.cms.tags.index') }}">
                                        <i class="fas fa-tags"></i> Tags
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('admin.cms.media*') ? 'active' : '' }}">
                                    <a class="nav-link" href="{{ route('admin.cms.media.index') }}">
                                        <i class="fas fa-images"></i> Media Library
                                    </a>
                                </li>
                                <li class="{{ request()->routeIs('admin.cms.menus*') ? 'active' : '' }}">
                                    <a class="nav-link" href="{{ route('admin.cms.menus.index') }}">
                                        <i class="fas fa-bars"></i> Menus
                                    </a>
                                </li>
                            </ul>
                        </li>
                        
                        <!-- Existing Menu Items -->
                        <li class="menu-header">Manage Contents</li>
                        <li class="nav-item dropdown">
                            <a href="javascript:void(0)" class="nav-link has-dropdown">
                                <i class="fas fa-graduation-cap"></i>
                                <span>Manage Courses</span>
                            </a>
                            <ul class="dropdown-menu">
                                <!-- Course management items would go here -->
                            </ul>
                        </li>
                        
                        <li>
                            <a class="nav-link" href="#">
                                <i class="fas fa-certificate"></i>
                                <span>Certificate Builder</span>
                            </a>
                        </li>
                        
                        <li>
                            <a class="nav-link" href="#">
                                <i class="fas fa-award"></i>
                                <span>Badges</span>
                            </a>
                        </li>
                        
                        <!-- Settings -->
                        <li class="menu-header">Settings</li>
                        <li class="{{ request()->routeIs('admin.cms.settings*') ? 'active' : '' }}">
                            <a class="nav-link" href="{{ route('admin.cms.settings.index') }}">
                                <i class="fas fa-cog"></i>
                                <span>Site Settings</span>
                            </a>
                        </li>
                    </ul>
                </aside>
            </div>
            
            <!-- Main Content -->
            <div class="main-content">
                <section class="section">
                    <!-- Page Header -->
                    @hasSection('page-header')
                        <div class="section-header">
                            <h1>@yield('page-title', 'Dashboard')</h1>
                            @hasSection('breadcrumb')
                                <div class="section-header-breadcrumb">
                                    @yield('breadcrumb')
                                </div>
                            @endif
                        </div>
                    @endif
                    
                    <!-- Flash Messages -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif
                    
                    <!-- Page Content -->
                    <div class="section-body">
                        @yield('content')
                    </div>
                </section>
            </div>
            
            <!-- Footer -->
            <footer class="main-footer">
                <div class="footer-left">
                    Copyright &copy; {{ date('Y') }} <div class="bullet"></div> Design By <a href="#">{{ config('app.name') }}</a>
                </div>
                <div class="footer-right">
                    <span>Version: 1.0.0</span>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('frontend/js/admin.js') }}"></script>
    
    @stack('scripts')
</body>
</html>
