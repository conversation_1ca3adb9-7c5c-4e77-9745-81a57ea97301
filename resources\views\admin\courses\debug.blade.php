@extends('admin.dashboard')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Debug Course Creation</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('admin.courses.index') }}">Courses</a></li>
                                <li class="breadcrumb-item active">Debug</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            @if ($errors->any())
                <div class="alert alert-danger">
                    <h5>Validation Errors:</h5>
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @if (session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title">Debug Form - Minimal Required Fields</h4>
                            <p class="card-title-desc">This form only includes the absolutely required fields to test validation.</p>

                            <form action="{{ route('admin.courses.store') }}" method="POST" id="debugForm">
                                @csrf
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="title">Course Title <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control {{ $errors->has('title') ? 'is-invalid' : '' }}" 
                                                   id="title" name="title" value="{{ old('title', 'Debug Test Course') }}" required>
                                            @if($errors->has('title'))
                                                <div class="invalid-feedback">{{ $errors->first('title') }}</div>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="category_id">Category <span class="text-danger">*</span></label>
                                            <select class="form-select {{ $errors->has('category_id') ? 'is-invalid' : '' }}" 
                                                    id="category_id" name="category_id" required>
                                                <option value="">Select Category</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @if($errors->has('category_id'))
                                                <div class="invalid-feedback">{{ $errors->first('category_id') }}</div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="instructor_id">Instructor <span class="text-danger">*</span></label>
                                            <select class="form-select {{ $errors->has('instructor_id') ? 'is-invalid' : '' }}" 
                                                    id="instructor_id" name="instructor_id" required>
                                                <option value="">Select Instructor</option>
                                                @foreach($instructors as $instructor)
                                                    <option value="{{ $instructor->id }}" {{ old('instructor_id') == $instructor->id ? 'selected' : '' }}>
                                                        {{ $instructor->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @if($errors->has('instructor_id'))
                                                <div class="invalid-feedback">{{ $errors->first('instructor_id') }}</div>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="status">Status <span class="text-danger">*</span></label>
                                            <select class="form-select {{ $errors->has('status') ? 'is-invalid' : '' }}" 
                                                    id="status" name="status" required>
                                                <option value="">Select Status</option>
                                                <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="published" {{ old('status') == 'published' ? 'selected' : '' }}>Published</option>
                                                <option value="archived" {{ old('status') == 'archived' ? 'selected' : '' }}>Archived</option>
                                            </select>
                                            @if($errors->has('status'))
                                                <div class="invalid-feedback">{{ $errors->first('status') }}</div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" value="1"
                                                       {{ old('is_featured') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_featured">
                                                    Featured Course
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="uil-check me-1"></i> Create Course (Debug)
                                    </button>
                                    <a href="{{ route('admin.courses.create') }}" class="btn btn-secondary">
                                        <i class="uil-arrow-left me-1"></i> Back to Full Form
                                    </a>
                                </div>
                            </form>

                            <div class="mt-4">
                                <h5>Debug Information</h5>
                                <div id="debugInfo" class="alert alert-info">
                                    <p><strong>Form will show debug information here when submitted.</strong></p>
                                    <p>Check browser console for detailed form data.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('debugForm');
    const debugInfo = document.getElementById('debugInfo');
    
    form.addEventListener('submit', function(event) {
        const formData = new FormData(form);
        let debugHtml = '<h6>Form Data Being Submitted:</h6><ul>';
        
        for (let [key, value] of formData.entries()) {
            debugHtml += `<li><strong>${key}:</strong> "${value}"</li>`;
        }
        debugHtml += '</ul>';
        
        debugHtml += '<h6>Field Values:</h6><ul>';
        debugHtml += `<li><strong>Status field value:</strong> "${document.getElementById('status').value}"</li>`;
        debugHtml += `<li><strong>Featured checkbox checked:</strong> ${document.getElementById('is_featured').checked}</li>`;
        debugHtml += '</ul>';
        
        debugInfo.innerHTML = debugHtml;
        
        console.log('=== DEBUG FORM SUBMISSION ===');
        console.log('Form data:', Object.fromEntries(formData));
        console.log('Status value:', document.getElementById('status').value);
        console.log('Featured checked:', document.getElementById('is_featured').checked);
    });
});
</script>
@endsection
